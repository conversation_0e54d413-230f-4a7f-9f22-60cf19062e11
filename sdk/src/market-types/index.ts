import { Keypair } from '@mysten/sui/dist/cjs/cryptography';

export * from './decimal';
export * from './assets';
export * from './market';
export * from './obligation';
export * from './operation';

// Transaction types for executeTransaction method
export interface TransactionOptions {
  gasBudget?: number;
  gasPrice?: number;
}

export interface TransactionResult {
  digest: string;
  effects?: any;
  events?: any[] | null | undefined;
  objectChanges?: any[] | null | undefined;
  balanceChanges?: any[] | null | undefined;
}

// Signer types for executeTransaction method
export interface WalletAdapter {
  toSuiAddress: () => string;
  signAndExecuteTransaction: (transaction: any) => Promise<any>;
}

export type Signer = Keypair | WalletAdapter;