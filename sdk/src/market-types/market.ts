import { Decimal } from './decimal';
import { TypeName, AssetBorrow, AssetDeposit } from './assets';
import { CoinMetadata, getCoinMetadata, parseCoinDecimals } from '../utils/coin-metadata';
import { SuiClient } from '@mysten/sui/dist/cjs/client';

export interface InterestModel {
  type: string;
  params: {
    baseBorrowRatePerSec: Decimal;
    borrowRateOnMidKink: Decimal;
    midKink: Decimal;
    borrowRateOnHighKink: Decimal;
    highKink: Decimal;
    maxBorrowRate: Decimal;
  };
}

export interface BorrowConfig {
  minBorrowAmount: bigint;
  maxBorrowAmount: bigint;
  maxDepositAmount: bigint;
  flashLoanFeeRate: Decimal;
  repayFeeRate: Decimal;
}

export interface CollateralSetting {
  collateralFactor: Decimal;
  liquidationIncentive: Decimal;
  liquidationRevenueFactor: Decimal;
}

export class AssetConfiguration {
  constructor(
    public coinType: TypeName,
    private interestModel: InterestModel,
    private borrowConfig: BorrowConfig,
    private collateralConfig?: CollateralSetting,
    public borrowPaused: boolean = false,
    public depositPaused: boolean = false,
    public withdrawPaused: boolean = false,
    public utilizationRate: Decimal = Decimal.zero(),
  ) {}

  public getInterestModel(): InterestModel {
    return this.interestModel;
  }

  public borrowInterestRate(utilizationRate: Decimal): Decimal {
    const model = this.interestModel.params;
    const baseRate = model.baseBorrowRatePerSec;
    const midKink = model.midKink;
    const highKink = model.highKink;
    const borrowRateOnMidKink = model.borrowRateOnMidKink;
    const borrowRateOnHighKink = model.borrowRateOnHighKink;
    const maxBorrowRate = model.maxBorrowRate;

    let borrowRate: Decimal;

    if (!utilizationRate.greaterThan(midKink)) {
      // util_rate <= mid_kink
      const weight = utilizationRate.divDecimal(midKink);
      const range = borrowRateOnMidKink.sub(baseRate);
      borrowRate = weight.mul(range).add(baseRate);
    } else if (!utilizationRate.greaterThan(highKink)) {
      // mid_kink < util_rate <= high_kink
      const weight = utilizationRate.sub(midKink).divDecimal(highKink.sub(midKink));
      const range = borrowRateOnHighKink.sub(borrowRateOnMidKink);
      borrowRate = weight.mul(range).add(borrowRateOnMidKink);
    } else {
      // util_rate > high_kink
      const one = Decimal.one();
      const weight = utilizationRate.sub(highKink).divDecimal(one.sub(highKink));
      const range = maxBorrowRate.sub(borrowRateOnHighKink);
      borrowRate = weight.mul(range).add(borrowRateOnHighKink);
    }

    return borrowRate;
  }

  public depositInterestRate(utilizationRate: Decimal, repayFeeRate: Decimal): Decimal {
    const borrowRate = this.borrowInterestRate(utilizationRate);
    return borrowRate.mul(Decimal.one().sub(repayFeeRate)).mul(utilizationRate);
  }

  public borrowSetting(): BorrowConfig {
    return this.borrowConfig;
  }

  public collateralSetting(): CollateralSetting | undefined {
    return this.collateralConfig;
  }
}

export interface AssetData {
  coinType: TypeName;
  interestModel: InterestModel;
  utilizationRate: Decimal;
  collateralSetting?: CollateralSetting;
  borrowPaused: boolean;
  depositPaused: boolean;
  withdrawPaused: boolean;
  assetSetting: BorrowConfig;
  depositUsage: AssetDeposit;
  borrowUsage: AssetBorrow;
}

export interface MarketData {
  assets: AssetData[];
}

export class Market {
  private assetConfigurations: Map<TypeName, AssetConfiguration>;
  private borrows: Map<TypeName, AssetBorrow>;
  private deposits: Map<TypeName, AssetDeposit>;
  readonly coinMetadatas: Map<TypeName, CoinMetadata>;

  readonly typeName: TypeName;
  readonly id: string;

  constructor(typeName: TypeName, id: string, marketData: MarketData, coinMetadatas: Map<TypeName, CoinMetadata>) {
    this.assetConfigurations = new Map();
    this.borrows = new Map();
    this.deposits = new Map();

    for (const asset of marketData.assets) {
      const config = new AssetConfiguration(
        asset.coinType,
        asset.interestModel,
        asset.assetSetting,
        asset.collateralSetting,
        asset.borrowPaused,
        asset.depositPaused,
        asset.withdrawPaused,
        asset.utilizationRate,
      );
      
      this.assetConfigurations.set(asset.coinType, config);
      this.borrows.set(asset.coinType, asset.borrowUsage);
      this.deposits.set(asset.coinType, asset.depositUsage);
    }

    this.coinMetadatas = coinMetadatas;
    this.typeName = typeName;
    this.id = id;
  }

  public static async new(typeName: TypeName, id: string, marketData: MarketData, client: SuiClient): Promise<Market> {
    const metadatas = new Map();
    for (const asset of marketData.assets) {
      const metadata = await getCoinMetadata(client, asset.coinType);
      metadatas.set(asset.coinType, metadata);
    }

    return new Market(typeName, id, marketData, metadatas);
  }

  public coinDecimal(coinType: TypeName): number {
    const metadata = this.coinMetadatas.get(coinType);
    if (!metadata) {
      throw new Error("no coin type in market");
    }

    return metadata.decimals;
  }

  assets(): AssetConfiguration[] {
    return Array.from(this.assetConfigurations.values());
  }

  getBorrow(asset: TypeName): AssetBorrow {
    const borrow = this.borrows.get(asset);
    if (!borrow) {
      throw new Error(`Borrow data not found for asset: ${asset}`);
    }
    return borrow;
  }

  public getTokenUsdEvaluation(amount: bigint, asset: TypeName): Decimal {
    const deposit = this.deposits.get(asset);
    if (!deposit) {
      throw new Error(`Deposit data not found for asset: ${asset}`);
    }

    const amountDecimal = Decimal.fromBigInt(amount);
    const price = deposit.price();
    const decimals = parseCoinDecimals(this.coinDecimal(asset));

    return amountDecimal.mul(price).divDecimal(decimals);
  }

  public getTokenAmountFromUsdValuation(usd: Decimal, asset: TypeName): Decimal {
    const deposit = this.deposits.get(asset);
    if (!deposit) {
      throw new Error(`Deposit data not found for asset: ${asset}`);
    }

    const price = deposit.price();
    const decimals = parseCoinDecimals(this.coinDecimal(asset));
    
    return usd.divDecimal(price).mul(decimals);
  }

  public getWeightedUsdEvaluation(amount: bigint, asset: TypeName, targetLTV: Decimal): Decimal {
    const deposit = this.deposits.get(asset);
    if (!deposit) {
      throw new Error(`Deposit data not found for asset: ${asset}`);
    }

    const amountDecimal = Decimal.fromBigInt(amount);
    const collateralFactor = this.getAssetCollateralFactor(asset);
    const price = deposit.price();
    const decimals = parseCoinDecimals(this.coinDecimal(asset));

    return amountDecimal.mul(price).divDecimal(decimals).mul(collateralFactor).mul(targetLTV);
  }

  public deriveCollateralAmount(usd: Decimal, asset: TypeName, targetLTV: Decimal): Decimal {
    const deposit = this.deposits.get(asset);
    if (!deposit) {
      throw new Error(`Deposit data not found for asset: ${asset}`);
    }

    const collateralFactor = this.getAssetCollateralFactor(asset);
    if (collateralFactor.isZero()) {
      return Decimal.zero();
    }

    const price = deposit.price();
    const decimals = parseCoinDecimals(this.coinDecimal(asset));
    
    return usd.divDecimal(collateralFactor).divDecimal(targetLTV).divDecimal(price).mul(decimals);
  }

  public deriveDebtUsdEvaluation(amount: bigint, asset: TypeName): Decimal {
    const borrow = this.borrows.get(asset);
    if (!borrow) {
      throw new Error(`Borrow data not found for asset: ${asset}`);
    }
    const price = borrow.price();
    const decimals = parseCoinDecimals(this.coinDecimal(asset));
    
    return Decimal.fromBigInt(amount).mul(price).divDecimal(decimals);
  }

  public deriveDebtAmount(usd: Decimal, asset: TypeName): Decimal {
    const borrow = this.borrows.get(asset);
    if (!borrow) {
      throw new Error(`Borrow data not found for asset: ${asset}`);
    }
    const price = borrow.price();
    const decimals = parseCoinDecimals(this.coinDecimal(asset));
    
    return usd.divDecimal(price).mul(decimals);
  }

  public exchangeRate(asset: TypeName): Decimal {
    const deposit = this.deposits.get(asset);
    if (!deposit) {
      throw new Error(`Deposit data not found for asset: ${asset}`);
    }

    return deposit.exchangeRate();
  }

  public borrowIndex(asset: TypeName): Decimal {
    const borrow = this.borrows.get(asset);
    if (!borrow) {
      throw new Error(`Borrow data not found for asset: ${asset}`);
    }

    return borrow.borrowIndex();
  }

  public depositToCtokens(asset: TypeName, amount: bigint): bigint{
    const exchangeRate = this.exchangeRate(asset);
    return Decimal.fromBigInt(amount).divDecimal(exchangeRate).floor();
  }

  getDeposit(asset: TypeName): AssetDeposit {
    const deposit = this.deposits.get(asset);
    if (!deposit) {
      throw new Error(`Deposit data not found for asset: ${asset}`);
    }
    return deposit;
  }

  utilizationRate(asset: TypeName): Decimal {
    const config = this.assetConfigurations.get(asset);
    if (!config) {
      throw new Error(`Asset configuration not found for: ${asset}`);
    }
    return config.utilizationRate;
  }

  public getAssetConfiguration(asset: TypeName): AssetConfiguration | undefined {
    return this.assetConfigurations.get(asset);
  }

  public getAssetCollateralFactor(asset: TypeName): Decimal {
    const assetConfig = this.getAssetConfiguration(asset);
    if (!assetConfig) {
      throw new Error(`no asset in market: ${asset}`);
    }
    
    const setting = assetConfig.collateralSetting();
    if (!setting) {
      throw new Error("asset not collateral");
    }

    return setting.collateralFactor;
  }
}