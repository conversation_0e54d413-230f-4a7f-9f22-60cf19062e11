import { Decimal } from './decimal';
import { TypeName, AssetBorrow, AssetDeposit } from './assets';
import { Operation, OperationName } from './operation';
import { Market } from './market';
import { parseCoinDecimals } from '../utils/coin-metadata';

export interface ObligationData {
  borrows: AssetBorrow[];
  deposits: AssetDeposit[];
}

export type ObligationID = string;

export class Obligation {
  private borrowsMap: Map<TypeName, AssetBorrow>;
  private depositsMap: Map<TypeName, AssetDeposit>;

  constructor(data: ObligationData) {
    this.borrowsMap = new Map();
    this.depositsMap = new Map();

    for (const borrow of data.borrows) {
      this.borrowsMap.set(borrow.coinType, borrow);
    }

    for (const deposit of data.deposits) {
      this.depositsMap.set(deposit.coinType, deposit);
    }
  }

  public static emptyObligation(): Obligation {
    return new Obligation({ borrows: [], deposits: [] });
  }

  public isActive(): boolean {
    return this.borrowsMap.size > 0 || this.depositsMap.size > 0;
  }

  borrowedAssets(): TypeName[] {
    return Array.from(this.borrowsMap.keys());
  }

  depositAssets(): TypeName[] {
    return Array.from(this.depositsMap.keys());
  }

  /// Estimate the debt amount since the last operation. This is only an estimate
  /// as the Market data lags slightly the actual data onchain
  estimateDebtAmount(asset: TypeName, market: Market): bigint {
    const borrow = this.getBorrow(asset, market);
    
    const obligationBorrowIndex = borrow.borrowIndex();
    const debt = borrow.amount();

    const marketBorrowIndex = market.borrowIndex(asset);

    return marketBorrowIndex.mulBigInt(debt).divDecimal(obligationBorrowIndex).ceiling();
  }

  public hasBorrow(asset: TypeName): boolean {
    return this.borrowsMap.has(asset);
  }

  public getBorrow(asset: TypeName, market?: Market): AssetBorrow {
    const borrow = this.borrowsMap.get(asset);
    if (!borrow) {
      let price = Decimal.zero();
      if (market) {
        price = market.getBorrow(asset).price();
      }
      const valuation = {
        coinType: asset,
        amount: 0n,
        usd: Decimal.zero(),
        price,
      }
      const borrowIndex = Decimal.one();
      return new AssetBorrow(valuation, borrowIndex);
    }
    return borrow;
  }

  hasDeposit(asset: TypeName): boolean {
    return this.depositsMap.has(asset);
  }

  getDeposit(asset: TypeName): AssetDeposit {
    const deposit = this.depositsMap.get(asset);
    if (!deposit) {
      throw new Error(`No deposit found for asset: ${asset}`);
    }
    return deposit;
  }

  public currentLTV(market: Market): Decimal {
    const totalCollateralValue = this.maxBorrowValueWithMarket(market);
    if (totalCollateralValue.equals(Decimal.zero())) {
      return Decimal.zero();
    }

    const totalBorrowValue = this.totalBorrowUsd();

    return totalBorrowValue.divDecimal(totalCollateralValue);
  }

  public netValue(): Decimal {
    let totalCollateralValue = Decimal.zero();
    const totalBorrowValue = this.totalBorrowUsd();;

    // Calculate total collateral value with collateral factors
    for (const deposit of this.depositsMap.values()) {
      totalCollateralValue = totalCollateralValue.add(deposit.usdValue);
    }

    return totalCollateralValue.sub(totalBorrowValue);
  }

  public maxBorrow(targetLTV: Decimal, market: Market, assetType: TypeName, decimals: number): Decimal {
    const deciamlMulti = Decimal.fromString(`1${"0".repeat(decimals)}`);

    const price = market.getBorrow(assetType).price();

    const surplus = this.surplus(market, targetLTV);
    const obligationMaxBorrow = surplus.divDecimal(price).mul(deciamlMulti);

    return obligationMaxBorrow.min(
      Decimal.fromBigInt(this.marketAvailableTokenAmount(market, assetType))
    );
  }

  public marketAvailableTokenAmount(market: Market, assetType: TypeName): bigint {
    // Get market deposit and borrow data for this asset
    const marketDeposit = market.getDeposit(assetType);
    const marketBorrow = market.getBorrow(assetType);

    return marketDeposit.amount() - marketBorrow.amount();
  }

  public maxWithdraw(market: Market, assetType: TypeName, decimals: number, targetLTV: Decimal = Decimal.fromString("0.8")): Decimal {
    // Get the current deposit for this asset if it exists
    const currentDeposit = this.depositsMap.get(assetType);
    if (!currentDeposit) {
      return Decimal.zero();
    }

    const availableToken = Decimal.fromBigInt(this.marketAvailableTokenAmount(market, assetType));
    const depositAmount = Decimal.fromBigInt(currentDeposit.amount());
    const availableAmount = depositAmount.min(availableToken);

    if (this.totalBorrowUsd().equals(Decimal.zero())) {
      // there is no borrow at all, return everything
      return availableAmount;
    }

    const assetConfig = market.getAssetConfiguration(assetType);
    if (!assetConfig) {
      throw new Error("asset not supported");
    }
    const collateralFactor = assetConfig.collateralSetting()!.collateralFactor;
    if (collateralFactor.isZero()) {
      // this is not collateral, just withdraw whatever is available
      return availableAmount;
    }

    const surplus = this.surplus(market, targetLTV);
    if (surplus.isZero()) {
      return Decimal.zero();
    }

    // we are withdrawing collateral
    const price = market.getDeposit(assetType).price();
    const decimalMulti = Decimal.fromString(`1${"0".repeat(decimals)}`);
    // Calculate max withdrawable amount based on surplus collateral (convert USD to token amount)
    // surplus (USD) = maxWithdraw * price / decimalMulti * collateralFactor
    // withdrawAmount = surplus * decimalMulti / (price * collateralFactor)
    const maxWithdraw = surplus.mul(decimalMulti).divDecimal(price.mul(collateralFactor));

    return maxWithdraw.min(availableAmount);
  }

  // Calculate maximum borrow value based on current collateral
  public maxBorrowValueWithMarket(market: Market, exclude: Set<TypeName> = new Set()): Decimal {
    let totalCollateralValue = Decimal.zero();

    // Calculate total collateral value with collateral factors
    for (const deposit of this.depositsMap.values()) {
      if (deposit.canBeCollateral && !exclude.has(deposit.coinType)) {
        const assetConfig = market.getAssetConfiguration(deposit.coinType);
        if (assetConfig && assetConfig.collateralSetting()) {
          const collateralFactor = assetConfig.collateralSetting()!.collateralFactor;
          // Multiply deposit value by collateral factor
          const collateralValue = deposit.usdValue.mul(collateralFactor);
          totalCollateralValue = totalCollateralValue.add(collateralValue);
        }
      }
    }

    return totalCollateralValue;
  }

  public totalBorrowUsd(exclude: Set<TypeName> = new Set()): Decimal {
    let totalBorrowValue = Decimal.zero();
    for (const borrow of this.borrowsMap.values()) {
      if (exclude.has(borrow.coinType)) {
        continue;
      }
      totalBorrowValue = totalBorrowValue.add(borrow.usdValue);
    }
    return totalBorrowValue;
  }

  public surplus(market: Market, targetLTV: Decimal = Decimal.one()): Decimal {
    const collateralValue = this.maxBorrowValueWithMarket(market);
    const totalBorrowValue = this.totalBorrowUsd();

    const weightedCollateralValue = collateralValue.mul(targetLTV);

    if (weightedCollateralValue.lessThan(totalBorrowValue)) {
      return Decimal.zero();
    }

    return weightedCollateralValue.sub(totalBorrowValue);
  }

  // Calculate net APY based on historical market data
  public netAPY(exchangeRates: Map<TypeName, Decimal>, borrowIndexes: Map<TypeName, Decimal>, previousTimestamp: number): Decimal {
    let totalDepositInterest = Decimal.zero();
    let totalBorrowInterest = Decimal.zero();
    let totalDepositValue = Decimal.zero();

    // Calculate weighted deposit interest based on exchange rate changes
    for (const deposit of this.depositsMap.values()) {
      const exchangeRate = exchangeRates.get(deposit.coinType);
      if (!exchangeRate) continue;

      // Calculate APY from exchange rate changes
      // The deposit already contains the current exchange rate
      const depositAPY = deposit.apy(exchangeRate, previousTimestamp);
      const depositInterest = deposit.usdValue.mul(depositAPY);

      totalDepositInterest = totalDepositInterest.add(depositInterest);
      totalDepositValue = totalDepositValue.add(deposit.usdValue);
    }

    // Calculate weighted borrow interest based on borrow index changes
    for (const borrow of this.borrowsMap.values()) {
      const borrowIndex = borrowIndexes.get(borrow.coinType);
      if (!borrowIndex) continue;

      // Calculate APY from borrow index changes
      // The borrow already contains the current borrow index
      const borrowAPY = borrow.apy(borrowIndex, previousTimestamp);
      const borrowInterest = borrow.usdValue.mul(borrowAPY);
      
      totalBorrowInterest = totalBorrowInterest.add(borrowInterest);
    }

    // Net APY = (Deposit Interest - Borrow Interest) / Total Deposit Value
    if (totalDepositValue.equals(Decimal.zero())) {
      return Decimal.zero();
    }

    const netInterest = totalDepositInterest.sub(totalBorrowInterest);
    return netInterest.divDecimal(totalDepositValue);
  }

  // Calculate total interest earned (deposit interest - borrow interest) in USD
  interestEarned(exchangeRates: Map<TypeName, Decimal>, borrowIndexes: Map<TypeName, Decimal>, previousTimestamp: number): Decimal {
    let totalDepositInterestEarned = Decimal.zero();
    let totalBorrowInterestPaid = Decimal.zero();

    for (const deposit of this.depositsMap.values()) {
      const currentExchangeRate = deposit.exchangeRate();
      const previousExchangeRate = exchangeRates.get(deposit.coinType);
      if (!previousExchangeRate) continue;
      
      // Calculate the value increase for the deposit amount
      const depositAmount = deposit.amount();
      const currentValue = Decimal.fromBigInt(depositAmount).mul(currentExchangeRate);
      const previousValue = Decimal.fromBigInt(depositAmount).mul(previousExchangeRate);
      
      const interestEarned = currentValue.sub(previousValue).mul(deposit.price());
      totalDepositInterestEarned = totalDepositInterestEarned.add(interestEarned);
    }

    // Calculate borrow interest paid from borrow index increases
    for (const borrow of this.borrowsMap.values()) {
      const currentBorrowIndex = borrow.borrowIndex();
      const previousBorrowIndex = borrowIndexes.get(borrow.coinType);
      if (!previousBorrowIndex) continue;
      
      // For a fixed principal, debt grows with the borrow index
      // We need to calculate how much extra we owe due to index increase
      const borrowAmount = borrow.amount();
      const indexRatio = currentBorrowIndex.divDecimal(previousBorrowIndex);
      const currentDebt = Decimal.fromBigInt(borrowAmount);
      const previousDebt = currentDebt.divDecimal(indexRatio);
      
      const interestPaid = currentDebt.sub(previousDebt).mul(borrow.price());
      totalBorrowInterestPaid = totalBorrowInterestPaid.add(interestPaid);
    }

    // Net interest earned = Deposit interest earned - Borrow interest paid
    return totalDepositInterestEarned.sub(totalBorrowInterestPaid);
  }

  applyOperation(operation: Operation, market: Market): Obligation {
    // Create deep copies of current state
    const newBorrows = new Map<TypeName, AssetBorrow>();
    const newDeposits = new Map<TypeName, AssetDeposit>();

    // Copy existing borrows
    for (const [coinType, borrow] of this.borrowsMap.entries()) {
      newBorrows.set(coinType, borrow);
    }

    // Copy existing deposits
    for (const [coinType, deposit] of this.depositsMap.entries()) {
      newDeposits.set(coinType, deposit);
    }

    const assetConfig = market.getAssetConfiguration(operation.assetType);
    if (!assetConfig) {
      throw new Error(`Asset configuration not found for: ${operation.assetType}`);
    }

    // Get current market data for the asset
    const marketDeposit = market.getDeposit(operation.assetType);
    const marketBorrow = market.getBorrow(operation.assetType);
    const price = marketDeposit.price();

    const deciamls = parseCoinDecimals(market.coinMetadatas.get(operation.assetType)!.decimals);

    switch (operation.name) {
      case OperationName.Deposit: {
        if (assetConfig.depositPaused) {
          throw new Error(`Asset ${operation.assetType} cannot be deposited`);
        }
        const existingDeposit = newDeposits.get(operation.assetType);
        const isCollateral = assetConfig.collateralSetting() !== undefined;

        if (existingDeposit) {
          // Update existing deposit
          const newAmount = existingDeposit.amount() + operation.amount.asBigInt();
          const newUsdValue = Decimal.fromBigInt(newAmount).mul(price).divDecimal(deciamls);
          // Calculate ctoken amount based on exchange rate
          const ctokenAmount = Decimal.fromBigInt(newAmount).divDecimal(marketDeposit.exchangeRate()).asBigInt();

          const updatedDeposit = new AssetDeposit(
            isCollateral,
            {
              coinType: operation.assetType,
              amount: newAmount,
              usd: newUsdValue,
              price: price,
            },
            ctokenAmount,
            marketDeposit.exchangeRate(),
          );
          newDeposits.set(operation.assetType, updatedDeposit);
        } else {
          // Create new deposit
          const newUsdValue = operation.amount.mul(price).divDecimal(deciamls);
          // Calculate ctoken amount based on exchange rate
          const ctokenAmount = operation.amount.divDecimal(marketDeposit.exchangeRate()).asBigInt();

          const newDeposit = new AssetDeposit(
            isCollateral,
            {
              coinType: operation.assetType,
              amount: operation.amount.asBigInt(),
              usd: newUsdValue,
              price: price,
            },
            ctokenAmount,
            marketDeposit.exchangeRate(),
          );
          newDeposits.set(operation.assetType, newDeposit);
        }
        break;
      }

      case OperationName.Withdraw: {
        if (assetConfig.withdrawPaused) {
          throw new Error(`Asset ${operation.assetType} cannot be withdrawn`);
        }
        const existingDeposit = newDeposits.get(operation.assetType);
        if (!existingDeposit) {
          throw new Error(`No deposit found to withdraw from asset: ${operation.assetType}`);
        }

        const withdrawAmount = operation.amount.asBigInt();
        
        let newAmount = 0n;
        if (withdrawAmount < existingDeposit.amount()) {
          newAmount = existingDeposit.amount() - withdrawAmount;
        }
        // if withdraw amount more than existing deposit, we dont really care at this level
        // as contract will always refund.

        if (newAmount === 0n) {
          // Remove deposit if fully withdrawn
          newDeposits.delete(operation.assetType);
        } else {
          // Update deposit with reduced amount
          const newUsdValue = Decimal.fromBigInt(newAmount).mul(price).divDecimal(deciamls);
          const isCollateral = existingDeposit.canBeCollateral;
          // Calculate ctoken amount based on exchange rate
          const ctokenAmount = Decimal.fromBigInt(newAmount).divDecimal(marketDeposit.exchangeRate()).asBigInt();

          const updatedDeposit = new AssetDeposit(
            isCollateral,
            {
              coinType: operation.assetType,
              amount: newAmount,
              usd: newUsdValue,
              price: price,
            },
            ctokenAmount,
            marketDeposit.exchangeRate(),
          );
          newDeposits.set(operation.assetType, updatedDeposit);
        }
        break;
      }

      case OperationName.Borrow: {
        if (assetConfig.borrowPaused) {
          throw new Error(`Asset ${operation.assetType} cannot be borrowed`);
        }

        const existingBorrow = newBorrows.get(operation.assetType);

        if (existingBorrow) {
          // Update existing borrow
          const newAmount = existingBorrow.amount() + operation.amount.asBigInt();
          const newUsdValue = Decimal.fromBigInt(newAmount).mul(price).divDecimal(deciamls);

          const updatedBorrow = new AssetBorrow(
            {
              coinType: operation.assetType,
              amount: newAmount,
              usd: newUsdValue,
              price: price,
            },
            marketBorrow.borrowIndex(),
          );
          newBorrows.set(operation.assetType, updatedBorrow);
        } else {
          // Create new borrow
          const newUsdValue = operation.amount.mul(price).divDecimal(deciamls);
          const newBorrow = new AssetBorrow(
            {
              coinType: operation.assetType,
              amount: operation.amount.asBigInt(),
              usd: newUsdValue,
              price: price,
            },
            marketBorrow.borrowIndex(),
          );
          newBorrows.set(operation.assetType, newBorrow);
        }
        break;
      }

      case OperationName.Repay: {
        const existingBorrow = newBorrows.get(operation.assetType);
        if (!existingBorrow) {
          throw new Error(`No borrow found to repay for asset: ${operation.assetType}`);
        }

        const repayAmount = operation.amount.asBigInt();
        let newAmount;
        if (repayAmount > existingBorrow.amount()) {
          // don't worry, contract will refund
          newAmount = 0n;
        } else {
          newAmount = existingBorrow.amount() - repayAmount;
        }

        if (newAmount === 0n) {
          // Remove borrow if fully repaid
          newBorrows.delete(operation.assetType);
        } else {
          // Update borrow with reduced amount
          const newUsdValue = Decimal.fromBigInt(newAmount).mul(price).divDecimal(deciamls);

          const updatedBorrow = new AssetBorrow(
            {
              coinType: operation.assetType,
              amount: newAmount,
              usd: newUsdValue,
              price: price,
            },
            marketBorrow.borrowIndex(),
          );
          newBorrows.set(operation.assetType, updatedBorrow);
        }
        break;
      }

      default:
        throw new Error(`Unknown operation: ${operation.name}`);
    }

    // Create new obligation data
    const newData: ObligationData = {
      borrows: Array.from(newBorrows.values()),
      deposits: Array.from(newDeposits.values()),
    };

    // Validate the new state
    return new Obligation(newData);
  }
}