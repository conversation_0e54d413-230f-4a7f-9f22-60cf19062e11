import { LeverageObligation, LeverageOperation } from './operation';
import { Market, Decimal, Obligation, TypeName, MarketData, AssetData, AssetDeposit, AssetBorrow } from '../market-types';
import { CoinMetadata } from '../utils/coin-metadata';

describe('LeverageOperation', () => {
  function createTestMarket(
    assets: {
      coinType: TypeName;
      collateralFactor?: number;
      flashLoanFee?: number;
      price: number;
      decimals: number;
    }[]
  ): Market {
    const marketData: MarketData = {
      assets: assets.map(asset => ({
        coinType: asset.coinType,
        interestModel: {
          type: 'jump',
          params: {
            baseBorrowRatePerSec: Decimal.fromNumber(0.01),
            borrowRateOnMidKink: Decimal.fromNumber(0.05),
            midKink: Decimal.fromNumber(0.5),
            borrowRateOnHighKink: Decimal.fromNumber(0.1),
            highKink: Decimal.fromNumber(0.8),
            maxBorrowRate: Decimal.fromNumber(0.2),
          },
        },
        utilizationRate: Decimal.fromNumber(0.5),
        collateralSetting: asset.collateralFactor ? {
          collateralFactor: Decimal.fromNumber(asset.collateralFactor),
          liquidationIncentive: Decimal.fromNumber(0.1),
          liquidationRevenueFactor: Decimal.fromNumber(0.05),
        } : undefined,
        borrowPaused: false,
        depositPaused: false,
        withdrawPaused: false,
        assetSetting: {
          minBorrowAmount: BigInt(0),
          maxBorrowAmount: BigInt(1000000000000),
          maxDepositAmount: BigInt(1000000000000),
          flashLoanFeeRate: Decimal.fromNumber(asset.flashLoanFee || 0.001),
          repayFeeRate: Decimal.fromNumber(0.001),
        },
        depositUsage: new AssetDeposit(
          true,
          {
            coinType: asset.coinType,
            amount: BigInt(0),
            usd: Decimal.zero(),
            price: Decimal.fromNumber(asset.price),
          },
          BigInt(0),  // ctoken_amount
          Decimal.fromNumber(1)
        ),
        borrowUsage: new AssetBorrow(
          {
            coinType: asset.coinType,
            amount: BigInt(0),
            usd: Decimal.zero(),
            price: Decimal.fromNumber(asset.price),
          },
          Decimal.fromNumber(1)
        ),
      } as AssetData)),
    };

    const coinMetadatas = new Map<TypeName, CoinMetadata>();
    assets.forEach(asset => {
      coinMetadatas.set(asset.coinType, {
        decimals: asset.decimals,
        name: asset.coinType,
        symbol: asset.coinType,
        description: '',
        iconUrl: undefined,
        id: '',
      });
    });

    // Now that the constructor is public, we can use it directly
    return new Market('test-market', '0x1', marketData, coinMetadatas);
  }

  describe('basic', () => {
    const targetLTV = Decimal.fromNumber(0.987);
    const swapSlippage = Decimal.fromNumber(0.005);
    
    const left: TypeName = 'SUI' as TypeName;
    const right: TypeName = 'USDC' as TypeName;

    it('should throw error when collateral factor is zero', () => {
      const market = createTestMarket([
        {
          coinType: left,
          collateralFactor: 0.75,  // SUI has collateral factor but won't be used
          flashLoanFee: 0.001,
          price: 50,
          decimals: 9,
        },
        {
          coinType: right,
          collateralFactor: 0,  // USDC has zero collateral factor
          flashLoanFee: 0.001,
          price: 1,
          decimals: 6,
        },
      ]);

      expect(() => {
        LeverageOperation.maxLeverageForLong(
          market,
          targetLTV,
          right,  // USDC
          BigInt(1000000),
          right,  // USDC collateral (but has 0 factor)
          left,   // SUI debt
          swapSlippage
        );
      }).toThrow('not collateral');
    });
  });

  describe('maxLeverage calculation', () => {
    const targetLTV = Decimal.fromNumber(0.987);
    const swapSlippage = Decimal.fromNumber(0.005);
    
    const left: TypeName = 'SUI' as TypeName;
    const right: TypeName = 'USDC' as TypeName;
    
    it('max leverage - long left with left/right are correct', () => {
      const amount = BigInt(2_000_000_000);
      
      const market = createTestMarket([
        {
          coinType: left,
          collateralFactor: 0.75,
          flashLoanFee: 0.00001,
          price: 183.08,
          decimals: 9,
        },
        {
          coinType: right,
          flashLoanFee: 0.00001,
          price: 1,
          decimals: 6,
        },
      ]);

      let result = LeverageOperation.maxLeverageForLong(
        market,
        targetLTV,
        left,
        amount,
        left,
        right,
        swapSlippage
      );
      
      expect(result.asNumber()).toBeGreaterThan(3.7);
      expect(result.asNumber()).toBeLessThan(3.8);
      expect(result).toEqual(Decimal.fromRaw(3795927859962256390n, 18));

      result = LeverageOperation.maxLeverageForLong(
        market,
        targetLTV,
        right,
        amount,
        left,
        right,
        swapSlippage
      );
      
      expect(result.asNumber()).toBeGreaterThan(3.7);
      expect(result.asNumber()).toBeLessThan(3.8);
      expect(result).toEqual(Decimal.fromRaw(3795927859962256390n, 18));
    });
  });

  describe('Test Long', () => {
    const targetLTV = Decimal.fromNumber(0.987);
    const swapSlippage = Decimal.fromNumber(0.005);
    
    const left: TypeName = 'SUI' as TypeName;
    const right: TypeName = 'USDC' as TypeName;

    it('leverage - long left with left is correct', () => {
      const amount = BigInt(2_000_000_000);
      const leverage = Decimal.fromNumber(3.65);

      const market = createTestMarket([
        {
          coinType: left,
          collateralFactor: 0.75,
          flashLoanFee: 0.00001,
          price: 183.49,
          decimals: 9,
        },
        {
          coinType: right,
          flashLoanFee: 0.00001,
          price: 1,
          decimals: 6,
        },
      ]);

      const operation = LeverageOperation.long(
        market,
        targetLTV,
        left, // inputCoinType
        amount, // 2 SUI
        leverage,
        left, // collateral
        right, // debt
        swapSlippage,
        
      );
      
      // Check principle amount equals 2 SUI
      expect(operation.principleAmount.asNumber()).toBeCloseTo(Number(amount), 2);
      
      // Check the total collateral is 7.30 SUI (2 * 3.65) => 7300000000
      expect(operation.collateralAmount.asNumber()).toBeCloseTo(7300000000, 2);

      const obligation = LeverageObligation.from(operation, market);
      // 178.51 usd
      expect(obligation.collateralLiquidationPrice(market).asNumber()).toBeCloseTo(178.5149);
      expect(obligation.lendingMarketObligation.currentLTV(market).asNumber()).toBeLessThan(targetLTV.asNumber());
    });

    it('leverage - long left with right is correct', () => {
      const amount = BigInt(1_000_000_000);
      const leverage = Decimal.fromNumber(3.65);

      const market = createTestMarket([
        {
          coinType: left,
          collateralFactor: 0.75,
          flashLoanFee: 0.00001,
          price: 183.49,
          decimals: 9,
        },
        {
          coinType: right,
          flashLoanFee: 0.00001,
          price: 1,
          decimals: 6,
        },
      ]);

      const operation = LeverageOperation.long(
        market,
        targetLTV,
        right, // inputCoinType
        amount,
        leverage,
        left, // collateral
        right, // debt
        swapSlippage,
        
      );
      
      expect(operation.principleAmount.asNumber()).toBeCloseTo(5422774405, 0);
      expect(operation.collateralAmount.asNumber()).toBeCloseTo(19793126579, 0);

      const obligation = LeverageObligation.from(operation, market);
      // 178.51 usd
      expect(obligation.collateralLiquidationPrice(market).asNumber()).toBeCloseTo(178.5149);
      expect(obligation.lendingMarketObligation.currentLTV(market).asNumber()).toBeLessThan(targetLTV.asNumber());
    });
  });

  describe('Test Short', () => {
    const targetLTV = Decimal.fromNumber(0.889);
    const swapSlippage = Decimal.fromNumber(0.005);
    
    const left: TypeName = 'SUI' as TypeName;  // Keep SUI as left
    const right: TypeName = 'USDC' as TypeName; // Keep USDC as right

    it('leverage - short left with right is correct', () => {
      const amount = BigInt(1000_000_000); // 1 USDC
      const leverage = Decimal.fromNumber(2.64);

      const market = createTestMarket([
        {
          coinType: left,
          collateralFactor: 0,  // SUI is not collateral
          flashLoanFee: 0.00001,
          price: 183.35,
          decimals: 9,
        },
        {
          coinType: right,
          collateralFactor: 0.9,  // USDC is collateral
          flashLoanFee: 0.00001,
          price: 1,
          decimals: 6,
        },
      ]);

      const operation = LeverageOperation.short(
        market,
        targetLTV,
        right,  // inputCoinType (USDC)
        amount, // 1 USDC
        leverage,
        left,   // debt (SUI)
        right,  // collateral (USDC)
        swapSlippage
      );
      
      expect(operation.principleAmount.asBigInt()).toEqual(1000_000_000n);
      expect(operation.debtAmount.asBigInt()).toEqual(14470829195n);
 
      const obligation = LeverageObligation.from(operation, market);
      expect(obligation.debtLiquidationPrice(market).asBigInt()).toEqual(226n);
      expect(obligation.lendingMarketObligation.currentLTV(market).asNumber()).toBeLessThan(targetLTV.asNumber());
    });

    it('leverage - short left with left is correct', () => {
      const amount = BigInt(2_000_000_000); // 2 SUI
      const leverage = Decimal.fromNumber(2.65);

      const market = createTestMarket([
        {
          coinType: left,
          collateralFactor: 0,  // SUI is not collateral
          flashLoanFee: 0.00001,
          price: 184.85,
          decimals: 9,
        },
        {
          coinType: right,
          collateralFactor: 0.9,  // USDC is collateral
          flashLoanFee: 0.00001,
          price: 1,
          decimals: 6,
        },
      ]);

      const operation = LeverageOperation.short(
        market,
        targetLTV,
        left,   // inputCoinType (SUI)
        amount,
        leverage,
        left,   // debt (SUI)
        right,  // collateral (USDC)
        swapSlippage
      );

      // Check principle amount, should be 2 SUI which should be 369, but there is swap and slippage, which should
      // be around 367.8
      expect(operation.principleAmount.asBigInt()).toEqual(367860696n);
      expect(operation.debtAmount.asBigInt()).toEqual(5300053000n);
 
      const obligation = LeverageObligation.from(operation, market);
      expect(obligation.debtLiquidationPrice(market).asBigInt()).toEqual(228n);
      expect(obligation.lendingMarketObligation.currentLTV(market).asNumber()).toBeLessThan(targetLTV.asNumber());
    });
  });
});