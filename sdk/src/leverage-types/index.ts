import { TypeName } from '../market-types';
import { Decimal } from '../market-types/decimal';

export * from "./operation";

export enum LeverageOperationType {
  SwapBorrow = 0,
  BorrowSwap = 1,
}

export interface LeveragePositionInfo {
  operation: LeverageOperationType;
  deposit: TypeName;
  borrow: TypeName;
  // principle token amount
  amount: string;
  // average price at which the position was entered
  average_price: Decimal;
}

export interface LeverageObligationDetail {
  lendingObligationId: string;
  info: LeveragePositionInfo | null;
}