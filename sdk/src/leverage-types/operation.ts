import { LeverageObligationDetail, LeverageOperationType } from '.';
import { Decimal, Market, Obligation, Operation, OperationName, TypeName } from '../market-types';
import { parseCoinDecimals } from '../utils/coin-metadata';

export class LeverageOperation {
  readonly operationType: LeverageOperationType;
  readonly collateralType: TypeName;
  readonly collateralAmount: Decimal;
  readonly debtType: TypeName;
  readonly debtAmount: Decimal;
  readonly principleAmount: Decimal;
  readonly maxLeverage: Decimal;

  private constructor(
    operationType: LeverageOperationType,
    collateralType: TypeName,
    collateralAmount: Decimal,
    debtType: TypeName,
    debtAmount: Decimal,
    principleAmount: Decimal,
    maxLeverage: Decimal,
  ) {
    this.operationType = operationType;
    this.collateralType = collateralType;
    this.collateralAmount = collateralAmount;
    this.debtType = debtType;
    this.debtAmount = debtAmount;
    this.principleAmount = principleAmount;
    this.maxLeverage = maxLeverage;
  }

  public static maxLeverageForLong(
    market: Market,
    targetLTV: Decimal,

    inputCoinType: TypeName,
    amount: bigint,

    collateral: TypeName,
    debt: TypeName,

    swapSlippage: Decimal,
  ): Decimal {
    const collateralFactor = market.getAssetCollateralFactor(collateral);
    if (collateralFactor.isZero()) {
      throw new Error("not collateral");
    }

    const obligation = Obligation.emptyObligation();
    if (inputCoinType === collateral) {
      return maxLeverageBorrowThenSwap(
        targetLTV,
        collateral,
        debt,
        amount,
        swapSlippage,
        obligation,
        market
      );
    } else {
      return maxLeverageSwapThenBorrow(
        targetLTV,
        collateral,
        debt,
        amount,
        swapSlippage,
        obligation,
        market
      );
    }
  }

  public static maxLeverageForShort(
    market: Market,
    targetLTV: Decimal,

    inputCoinType: TypeName,
    amount: bigint,

    leftCoinType: TypeName,
    rightCoinType: TypeName,

    swapSlippage: Decimal,
  ): Decimal {
    const leverage = LeverageOperation.maxLeverageForLong(
      market, targetLTV, inputCoinType, amount, rightCoinType, leftCoinType, swapSlippage
    );
    return leverage.sub(Decimal.one());
  }

  public static long(
    market: Market,
    targetLTV: Decimal,

    inputCoinType: TypeName,
    amount: bigint,
    leverage: Decimal,

    collateral: TypeName,
    debt: TypeName,

    swapSlippage: Decimal,
  ): LeverageOperation {
    const maxLeverage = LeverageOperation.maxLeverageForLong(
      market,
      targetLTV,
      inputCoinType,
      amount,
      collateral,
      debt,
      swapSlippage
    );
    if (leverage.greaterThan(maxLeverage)) {
      throw new Error("exceeding max leverage");
    }

    if (inputCoinType === collateral) {
      return LeverageOperation.fromBorrowSwap(market, amount, leverage, collateral, debt, swapSlippage, maxLeverage);
    } else {
      return LeverageOperation.fromSwapBorrow(market, amount, leverage, collateral, debt, swapSlippage, maxLeverage);
    }
  }

  public static short(
    market: Market,
    targetLTV: Decimal,

    inputCoinType: TypeName,
    amount: bigint,
    leverage: Decimal,

    leftCoinType: TypeName,
    rightCoinType: TypeName,

    swapSlippage: Decimal,
  ): LeverageOperation {
    const adjustedLeverage = leverage.add(Decimal.one());
    return LeverageOperation.long(
      market, targetLTV, inputCoinType, amount, adjustedLeverage, rightCoinType, leftCoinType, swapSlippage
    );
  }

  private static fromSwapBorrow(
    market: Market,
    amount: bigint,
    leverage: Decimal,
    collateral: TypeName,
    debt: TypeName,
    swapSlippage: Decimal,
    maxLeverage: Decimal,
  ): LeverageOperation {
    const flashloanFee = market.getAssetConfiguration(debt)!.borrowSetting().flashLoanFeeRate;
    const flashLoanMul = Decimal.one().add(flashloanFee);

    const swapSlippageMul = Decimal.one().add(swapSlippage);

    const amountDecimal = Decimal.fromBigInt(amount);
    const totalLeverageDebt = amountDecimal.mul(leverage);

    // rmb to repay the flash loan fee as well
    const totalDebtAmount = totalLeverageDebt.sub(amountDecimal).mul(flashLoanMul);

    const totalLeverageUsd = market.deriveDebtUsdEvaluation(totalLeverageDebt.ceiling(), debt);
    const swappedUsd = totalLeverageUsd.divDecimal(swapSlippageMul);
    const totalCollateral = market.getTokenAmountFromUsdValuation(swappedUsd, collateral);
    const principleAmount = totalCollateral.divDecimal(leverage);

    return new LeverageOperation(
      LeverageOperationType.SwapBorrow,
      collateral,
      totalCollateral,
      debt,
      totalDebtAmount,
      principleAmount,
      maxLeverage,
    );
  }

  private static fromBorrowSwap(
    market: Market,
    amount: bigint,
    leverage: Decimal,
    collateral: TypeName,
    debt: TypeName,
    swapSlippage: Decimal,
    maxLeverage: Decimal,
  ): LeverageOperation {
    const flashloanFee = market.getAssetConfiguration(collateral)!.borrowSetting().flashLoanFeeRate;
    const flashLoanRepayMul = Decimal.one().add(flashloanFee);
    const swapSlippageMul = Decimal.one().add(swapSlippage);

    // no need slippage due to the swap happens after borrow
    const principleAmount = Decimal.fromBigInt(amount);
    const totalCollateral = principleAmount.mul(leverage);

    const borrowedCollateral = totalCollateral.sub(principleAmount);
    const flashLoanRepay = borrowedCollateral
      .mul(flashLoanRepayMul)
      .mul(swapSlippageMul)
      // add 1 just to bump up estimation by a bit
      .asBigInt() + 1n;
    const flashLoanRepayUsd = market.getTokenUsdEvaluation(flashLoanRepay, collateral);
    const totalDebtAmount = market.deriveDebtAmount(flashLoanRepayUsd, debt);

    return new LeverageOperation(
      LeverageOperationType.BorrowSwap,
      collateral,
      totalCollateral,
      debt,
      totalDebtAmount,
      principleAmount,
      maxLeverage
    );
  }
}

export class LeverageObligation {
  readonly leverageObligation: LeverageObligationDetail;
  readonly lendingMarketObligation: Obligation;

  constructor(
    leverageObligation: LeverageObligationDetail,
    lendingMarketObligation: Obligation,
  ) {
    this.leverageObligation = leverageObligation;
    this.lendingMarketObligation = lendingMarketObligation;
  }

  public operationType(): LeverageOperationType {
    return this.leverageObligation.info!.operation;
  }

  public isActive(): boolean {
    return this.lendingMarketObligation.isActive();
  }

  public isLong(leftCoinType: TypeName): boolean {
    return isLong(leftCoinType, this.leverageObligation.info!.deposit);
  }

  public principleCoinType(): TypeName {
    if (!this.leverageObligation.info) {
      throw new Error("no leverage position yet");
    }

    const info = this.leverageObligation.info;
    return getPrincipleCoinType(info.deposit, info.borrow, info.operation);
  }

  public static from(operation: LeverageOperation, market: Market): LeverageObligation {
    let lending = Obligation.emptyObligation();
    lending = lending.applyOperation(
      new Operation(
        OperationName.Deposit,
        operation.collateralType,
        operation.collateralAmount,
      ),
      market
    );

    lending = lending.applyOperation(
      new Operation(
        OperationName.Borrow,
        operation.debtType,
        operation.debtAmount,
      ),
      market
    );

    const collateralPrice = market.getDeposit(operation.collateralType)!.price();
    const debtPrice = market.getDeposit(operation.debtType)!.price();
    return new LeverageObligation(
      {
        lendingObligationId: "0x",
        info: {
          operation: operation.operationType,
          deposit: operation.collateralType,
          borrow: operation.debtType,
          amount: operation.principleAmount.asBigInt().toString(),
          average_price: collateralPrice.divDecimal(debtPrice),
        }
      },
      lending
    );
  }

  public collateralLiquidationPrice(market: Market): Decimal {
    if (!this.leverageObligation.info) {
      return Decimal.zero();
    }

    if (!this.leverageObligation.info.deposit) {
      return Decimal.zero();
    }

    const collateralType = this.leverageObligation.info.deposit;

    const maxBorrowValueWithMarket = this.lendingMarketObligation.maxBorrowValueWithMarket(market, new Set([collateralType]))
    const totalBorrow = this.lendingMarketObligation.totalBorrowUsd();

    // totalBorrow == maxBorrowValueWithMarket + collateralFactor * newCollateralPrice * amount / decimals
    const decimalMulti = parseCoinDecimals(market.coinDecimal(collateralType));
    const collateralFactor = market.getAssetConfiguration(collateralType)!.collateralSetting()!.collateralFactor;
    const amountDecimal = this.lendingMarketObligation.depositAssets().includes(collateralType) ? Decimal.fromBigInt(this.lendingMarketObligation.getDeposit(collateralType).amount()) : Decimal.zero();
    const mul = amountDecimal.divDecimal(decimalMulti).mul(collateralFactor);

    return mul.isZero() ? Decimal.zero() : totalBorrow.sub(maxBorrowValueWithMarket).divDecimal(mul);
  }

  public debtLiquidationPrice(market: Market): Decimal {
    if (!this.leverageObligation.info) {
      return Decimal.zero();
    }

    if (!this.leverageObligation.info.deposit) {
      return Decimal.zero();
    }

    const debtType = this.leverageObligation.info.borrow;

    const maxBorrowValueWithMarket = this.lendingMarketObligation.maxBorrowValueWithMarket(market);
    const totalBorrow = this.lendingMarketObligation.totalBorrowUsd(new Set([debtType]));

    // totalBorrow + newDebtPrice * amount / decimals == maxBorrowValueWithMarket
    const decimalMulti = parseCoinDecimals(market.coinDecimal(debtType));
    const amountDecimal = Decimal.fromBigInt(this.lendingMarketObligation.getBorrow(debtType, market).amount());
    if (amountDecimal.isZero()) {
      return Decimal.zero();
    }

    return maxBorrowValueWithMarket.sub(totalBorrow).mul(decimalMulti).divDecimal(amountDecimal);
  }

  public maxLeverage(
    swapSlippage: Decimal,
    market: Market,
    targetLTV: Decimal,
    extraAmount: bigint = 0n,
  ): Decimal {
    if (!this.leverageObligation.info) {
      throw new Error("no leverage position yet");
    }
    const info = this.leverageObligation.info;
    if (info.operation === LeverageOperationType.BorrowSwap) {
      return maxLeverageBorrowThenSwap(
        targetLTV, info.deposit, info.deposit, extraAmount, swapSlippage, this.lendingMarketObligation, market
      )
    } else if (this.leverageObligation.info.operation === LeverageOperationType.SwapBorrow) {
      return maxLeverageSwapThenBorrow(
        targetLTV, info.deposit, info.borrow, extraAmount, swapSlippage, this.lendingMarketObligation, market
      )
    } else {
      throw new Error("invalid coin type setting");
    }
  }

  public applyIncreaseOperation(operation: LeverageOperation, market: Market): LeverageObligation {
    if (operation.operationType !== this.leverageObligation.info!.operation) {
      throw new Error("inconsistent leverage operation type");
    }

    // Deep copy leverageObligation
    const copiedLeverageObligation: LeverageObligationDetail = {
      lendingObligationId: this.leverageObligation.lendingObligationId,
      info: this.leverageObligation.info ? {
        operation: this.leverageObligation.info.operation,
        deposit: this.leverageObligation.info.deposit,
        borrow: this.leverageObligation.info.borrow,
        amount: (BigInt(this.leverageObligation.info.amount) + operation.principleAmount.asBigInt()).toString(),
        average_price: this.leverageObligation.info.average_price,
      } : null,
    };

    // Apply deposit then borrow operations (applyOperation already creates deep copy)
    const updatedLendingObligation = this.lendingMarketObligation
      .applyOperation(
        new Operation(
          OperationName.Deposit,
          operation.collateralType,
          operation.collateralAmount,
        ),
        market
      )
      .applyOperation(
        new Operation(
          OperationName.Borrow,
          operation.debtType,
          operation.debtAmount,
        ),
        market
      );

    return new LeverageObligation(copiedLeverageObligation, updatedLendingObligation);
  }

  public applyReduceSize(percentage: Decimal, swapSlippage: Decimal, market: Market): LeverageObligation {
    if (!this.leverageObligation.info) {
      throw new Error("no leverage position yet");
    }

    // Calculate the reduction amounts
    const { collateral: collateralReduction, debt: debtReduction } = this.reduceSizeBorrowSwap(percentage, swapSlippage, market);

    // Calculate new principle amount (reduce by percentage)
    const currentPrinciple = BigInt(this.leverageObligation.info.amount);
    const reducedPrinciple = percentage.mulBigInt(currentPrinciple).asBigInt();
    const newPrinciple = currentPrinciple - reducedPrinciple;

    // Calculate new average price using current market price
    const currentMarketPrice = getCurrentMarketPriceRatio(
      this.leverageObligation.info.deposit,
      this.leverageObligation.info.borrow,
      market
    );
    
    const newAveragePrice = calculateAveragePriceAfterReduction(
      currentPrinciple,
      this.leverageObligation.info.average_price,
      reducedPrinciple,
      currentMarketPrice
    );

    // Deep copy leverageObligation with reduced principle
    const copiedLeverageObligation: LeverageObligationDetail = {
      lendingObligationId: this.leverageObligation.lendingObligationId,
      info: {
        operation: this.leverageObligation.info.operation,
        deposit: this.leverageObligation.info.deposit,
        borrow: this.leverageObligation.info.borrow,
        amount: newPrinciple.toString(),
        average_price: newAveragePrice,
      },
    };

    // Apply withdraw and repay operations to reduce the lending obligation
    const updatedLendingObligation = this.lendingMarketObligation
      .applyOperation(
        new Operation(
          OperationName.Repay,
          this.leverageObligation.info.borrow,
          Decimal.fromBigInt(debtReduction),
        ),
        market
      )
      .applyOperation(
        new Operation(
          OperationName.Withdraw,
          this.leverageObligation.info.deposit,
          Decimal.fromBigInt(collateralReduction),
        ),
        market
      );

    return new LeverageObligation(copiedLeverageObligation, updatedLendingObligation);
  }

  public applyReduceLeverage(toLeverage: Decimal, swapSlippage: Decimal, market: Market): LeverageObligation {
    if (!this.leverageObligation.info) {
      throw new Error("no leverage position yet");
    }

    // Calculate the reduction amounts using reduceLeverage
    const { collateral: collateralReduction, debt: debtReduction } = this.reduceLeverageToCollateral(toLeverage, swapSlippage, market);

    // Deep copy leverageObligation - principle and average price remain the same
    const copiedLeverageObligation: LeverageObligationDetail = {
      lendingObligationId: this.leverageObligation.lendingObligationId,
      info: {
        operation: this.leverageObligation.info.operation,
        deposit: this.leverageObligation.info.deposit,
        borrow: this.leverageObligation.info.borrow,
        amount: this.leverageObligation.info.amount, // Principle stays the same
        average_price: this.leverageObligation.info.average_price, // Average price stays the same
      },
    };

    // Apply repay and withdraw operations to reduce the lending obligation
    const updatedLendingObligation = this.lendingMarketObligation
      .applyOperation(
        new Operation(
          OperationName.Repay,
          this.leverageObligation.info.borrow,
          Decimal.fromBigInt(debtReduction),
        ),
        market
      )
      .applyOperation(
        new Operation(
          OperationName.Withdraw,
          this.leverageObligation.info.deposit,
          Decimal.fromBigInt(collateralReduction),
        ),
        market
      );

    return new LeverageObligation(copiedLeverageObligation, updatedLendingObligation);
  }
  
  public averagePrice(): Decimal {
    return this.leverageObligation.info?.average_price!;
  }

  public principleAmount(): bigint {
    if (!this.leverageObligation.info) {
      throw new Error("no leverage position yet");
    }
    return BigInt(this.leverageObligation.info.amount);
  }

  /// The average entry usd, this is actually
  // TODO: rename to princple evaluation
  public principleUSD(market: Market, isAsDebt: boolean = false): Decimal {
    const principleAmount = this.principleAmount();
    
    let price = this.averagePrice();
    if (isAsDebt) {
      throw new Error("todo");
    }

    const collateralType = this.leverageObligation.info!.deposit;
    const decimalMulti = parseCoinDecimals(market.coinDecimal(collateralType));

    return price.mulBigInt(principleAmount).divDecimal(decimalMulti);
  }

  public principleUSDByMarkPrice(market: Market): Decimal {
    const principleAmount = this.principleAmount();
    const collateralType = this.leverageObligation.info!.deposit;
    return market.getTokenUsdEvaluation(principleAmount, collateralType);
  }

  public leverageSizeUSD(market: Market): Decimal {
    const amount = this.collateralAmount();
    const collateralType = this.leverageObligation.info!.deposit;
    return market.getTokenUsdEvaluation(amount, collateralType);
  }

  public collateralAmount(): bigint {
    if (!this.leverageObligation.info) {
      throw new Error("no leverage position yet");
    }

    const collateralType = this.leverageObligation.info!.deposit;

    if (this.lendingMarketObligation.hasDeposit(collateralType)) {
      return this.lendingMarketObligation.getDeposit(collateralType).amount();
    }
    return 0n;
  }

  public debtAmount(): bigint {
    if (!this.leverageObligation.info) {
      throw new Error("no leverage position yet");
    }

    const borrowType = this.leverageObligation.info!.borrow;

    if (this.lendingMarketObligation.hasBorrow(borrowType)) {
      return this.lendingMarketObligation.getBorrow(borrowType).amount();
    }

    return 0n;
  }

  public debtUSD(market: Market): Decimal {
    const debt = this.debtAmount();
    const borrowType = this.leverageObligation.info!.borrow;
    return market.getTokenUsdEvaluation(debt, borrowType);
  }

  public netValueUSD(market: Market): Decimal {
    return this.leverageSizeUSD(market).sub(this.debtUSD(market));
  }

  public pnl(market: Market): Decimal {
    return this.netValueUSD(market).sub(this.principleUSD(market));
  }

  public leverage(): Decimal {
    const principleAmount = this.principleAmount();

    if (principleAmount === 0n) {
      return Decimal.zero();
    }

    return Decimal.fromQuotientBigInt(this.collateralAmount(), this.principleAmount());
  }

  public leverageAsFixedOne(): number {
    return Number(this.leverage().asNumber().toFixed(1));
  }

  public reduceSizeBorrowSwap(percentage: Decimal, swapSlippage: Decimal, market: Market): {collateral: bigint, debt: bigint, debtSwapOut: bigint } {
    if (!this.leverageObligation.info) {
      throw new Error("no obligation");
    }

    const info = this.leverageObligation.info!;

    const {borrow: borrowType, deposit: collateralType} = info;

    const depositAmount = this.lendingMarketObligation.getDeposit(collateralType).amount();
    const totalDebt = this.lendingMarketObligation.estimateDebtAmount(borrowType, market);

    if (percentage.lessThan(Decimal.one())) {
      return this.requestReduceBorrowSwap(getAmount(percentage, depositAmount), swapSlippage, market);
    }

    const withdrawAmount = depositAmount;
    // slightly over estimate to cover unacrrued interest, will be refunded anyways
    const debt = totalDebt * 1008n / 1000n;

    const flashLoanFee = market.getAssetConfiguration(borrowType)?.borrowSetting()!.flashLoanFeeRate!;
    const flashLoanMul = flashLoanFee.add(Decimal.one());

    return { collateral: withdrawAmount, debt, debtSwapOut: flashLoanMul.mulBigInt(debt).ceiling() }
  }

  public reduceLeverageToCollateral(toLeverage: Decimal, slippage: Decimal, market: Market): {collateral: bigint, debt: bigint, debtSwapOut: bigint } {
    const newCollateral = toLeverage.mulBigInt(this.principleAmount()).ceiling();
    const collateralWithdraw = this.collateralAmount() - newCollateral;
    return this.requestReduceBorrowSwap(collateralWithdraw, slippage, market);
  }

  private requestReduceBorrowSwap(collateralWithdraw: bigint, slippage: Decimal, market: Market): {collateral: bigint, debt: bigint, debtSwapOut: bigint } {
    const collateralType = this.leverageObligation.info!.deposit;
    const debtType = this.leverageObligation.info!.borrow;

    const flashLoanFee = market.getAssetConfiguration(debtType)?.borrowSetting()!.flashLoanFeeRate!;
    const flashLoanMul = flashLoanFee.add(Decimal.one());
    const slippageMul = slippage.add(Decimal.one());

    // in execution, it is swapping collateral to debt, flash loan total debt = debt * (1_+ flashLoanFee)
    // collateral usd after slippage = collateralInUSD / (1 + swapSlippage)
    // collateralInUSD / (1 + swapSlippage) > debtUSD * (1_+ flashLoanFee)
    const collateralInUsd = market.getTokenUsdEvaluation(collateralWithdraw, collateralType);

    let repayUsd = collateralInUsd.divDecimal(slippageMul);
    repayUsd = repayUsd.divDecimal(flashLoanMul);

    const debtRepay = this.calculateSwapCollateralToBorrow(slippage, collateralWithdraw, market);
    return { collateral: collateralWithdraw, debt: debtRepay, debtSwapOut: flashLoanMul.mulBigInt(debtRepay).ceiling() };
  }

  // One flash loans collateral then deposit then borrow to debt then swap to collateral
  public increaseLeverageBorrowSwap(toLeverage: Decimal, slippage: Decimal, market: Market): {collateral: bigint, debt: bigint } {
    const newCollateral = toLeverage.mulBigInt(this.principleAmount()).ceiling();
    const collateral = newCollateral - this.collateralAmount();

    const collateralType = this.leverageObligation.info!.deposit;
    const debtType = this.leverageObligation.info!.borrow;

    const collateralUsd = market.getTokenUsdEvaluation(collateral, collateralType);

    const flashLoanFee = market.getAssetConfiguration(debtType)?.borrowSetting()!.flashLoanFeeRate!;
    const flashLoanMul = flashLoanFee.add(Decimal.one());
    const slippageMul = slippage.add(Decimal.one());

    // so in execution, it's debt swapped to collateral then repay flash loan
    let repayUsd = collateralUsd.mul(flashLoanMul);
    repayUsd = repayUsd.mul(slippageMul);

    const debt = market.getTokenAmountFromUsdValuation(repayUsd, debtType).ceiling();

    return { collateral, debt};
  }

  public deduceBorrowToSwapToCollateral(
    swapSlippage: Decimal,
    collateralAmount: bigint,
    market: Market,
  ): bigint {
    const collateralType = this.leverageObligation.info!.deposit;
    const debtType = this.leverageObligation.info!.borrow;

    const collateralUsd = market.getTokenUsdEvaluation(collateralAmount, collateralType);

    const flashLoanFee = market.getAssetConfiguration(debtType)?.borrowSetting()!.flashLoanFeeRate!;
    const flashLoanMul = flashLoanFee.add(Decimal.one());
    const slippageMul = swapSlippage.add(Decimal.one());

    let repayUsd = collateralUsd.divDecimal(slippageMul);
    repayUsd = repayUsd.divDecimal(flashLoanMul); 

    return market.getTokenAmountFromUsdValuation(repayUsd, debtType).ceiling();
  }

  public calculateSwapCollateralToBorrow(
    swapSlippage: Decimal,
    collateralAmount: bigint,
    market: Market,
  ): bigint {    
    const collateralType = this.leverageObligation.info!.deposit;
    const debtType = this.leverageObligation.info!.borrow;

    const collateralUsd = market.getTokenUsdEvaluation(collateralAmount, collateralType);

    const flashLoanFee = market.getAssetConfiguration(debtType)?.borrowSetting()!.flashLoanFeeRate!;
    const flashLoanMul = flashLoanFee.add(Decimal.one());
    const slippageMul = swapSlippage.add(Decimal.one());

    let repayUsd = collateralUsd.divDecimal(slippageMul);
    repayUsd = repayUsd.divDecimal(flashLoanMul);

    return market.getTokenAmountFromUsdValuation(repayUsd, debtType).ceiling();
  }
}

function maxLeverageSwapThenBorrow(
  targetLTV: Decimal,

  collateralCoinType: TypeName,
  borrowCoinType: TypeName,

  amount: bigint,

  swapSlippage: Decimal,
  obligation: Obligation,
  market: Market,
): Decimal {
  // swapThenBorrow:
  //     swappedCollateralUSD = x * amount * priceBorrow / borrowDeciaml / (1 + swapSlippage)
  //     totalCollateralEvaluationUSD = existingCollateralUSD + swappedCollateralUSD * collateralFactor
  //     maxBorrowedUSD = targetLTV * totalCollateralEvaluationUSD - currentBorrow
  //     borrowAmount = maxBorrowedUSD / priceBorrow * borrowDeciaml
  //     flashLoanRepay = (x - 1) * amount * (1 + flashLoanFee)
  //     flashLoanRepay < borrowAmount

  //     A = amount * (1 + flashLoanFee)
  //     flashLoanRepay = A * x - A
  //
  //     maxBorrowedUSD = targetLTV * (existingCollateralUSD + swappedCollateralUSD * collateralFactor) - currentBorrow
  //                    = targetLTV * existingCollateralUSD - currentBorrow + targetLTV * swappedCollateralUSD * collateralFactor
  //                    = surplus + targetLTV * swappedCollateralUSD * collateralFactor
  //                    = surplus + targetLTV * x * amount * priceBorrow / borrowDeciaml / (1 + swapSlippage) * collateralFactor
  //     B = targetLTV * amount * priceBorrow / borrowDeciaml / (1 + swapSlippage) * collateralFactor
  //     maxBorrowedUSD = surplus + x * B
  //     borrowAmount = (surplus + x * B) * borrowDecimal / priceBorrow
  //     E = borrowDecimal / priceBorrow
  //     C = surplus * E
  //     D = B * E
  //     borrowAmount = C + x * D
  //     A * x - A < C + x * D
  //     (A - D) * x < A + C
  //     x < (A + C) / (A - D)

  const collateralFactor = market.getAssetConfiguration(collateralCoinType)!.collateralSetting()?.collateralFactor!;

  const flashLoanFee = market.getAssetConfiguration(borrowCoinType)!.borrowSetting()!.flashLoanFeeRate;
  const borrowPrice = market.getBorrow(borrowCoinType).price();
  const borrowDecimals = Decimal.fromNumber(Math.pow(10, market.coinMetadatas.get(borrowCoinType)?.decimals!));

  const borrowAmount = Decimal.fromBigInt(amount);
  const slippage = Decimal.one().add(swapSlippage);

  const surplus = obligation.surplus(market, targetLTV);

  const A = borrowAmount.mul(Decimal.one().add(flashLoanFee));
  const B = targetLTV.mul(borrowAmount).mul(borrowPrice).divDecimal(borrowDecimals).divDecimal(slippage).mul(collateralFactor);
  const E = borrowDecimals.divDecimal(borrowPrice);
  const C = surplus.mul(E);
  const D = B.mul(E);

  if (A.lessThan(D)) {
    throw new Error("borrow amount too much");
  }

  return A.add(C).divDecimal(A.sub(D));
}

function maxLeverageBorrowThenSwap(
  targetLTV: Decimal,

  collateralCoinType: TypeName,
  borrowCoinType: TypeName,

  amount: bigint,

  swapSlippage: Decimal,
  obligation: Obligation,
  market: Market,
): Decimal {
  // borrowThenSwap:
  //     canBeBorrowedUSD = assetLTV * targetLTV * x * newAmount * price / collateralDecimals + surplus
  //     canBeBorrowedAmount = canBeBorrowedUSD / priceBorrowOracle * borrowDecimals
  //     minSwapped = canBeBorrowedUSD / priceBorrowOracle * priceBorrowDex / priceCollateralDex / (1 + slippage) * collateralDecimals -----------------> amount in collateral coin
  //     minSwapped = (assetLTV * targetLTV * x * newAmount * price / collateralDecimals + surplus) / priceBorrowOracle * priceBorrowDex / priceCollateralDex / (1 + slippage) * collateralDecimals -----------------> amount in collateral coin

  //     minSwapped = (assetLTV * targetLTV * x * newAmount * price / collateralDecimals + surplus) / priceCollateralDex / (1 + slippage) * collateralDecimals -----------------> amount in collateral coin
  //     minSwapped = (assetLTV * targetLTV * x * newAmount / (1 + slippage) + surplus / priceCollateralDex / (1 + slippage) * collateralDecimals -----------------> amount in collateral coin

  //     swapPrice = collateralPrice * (1 + slippage)

  //     minSwapped = canBeBorrowedUSD / swapPrice * collateralDecimals        
  //     flashLoanTotal = (x - 1) * newAmount * (1 + flashLoanFee) 
  //     flashLoanTotal < minSwapped

  //     A = assetLTV * targetLTV * newAmount / (1 + slippage)
  //     B = surplus / swapPrice * collateralDecimals
  //     C = newAmount * (1 + flashLoanFee)

  //     minSwapped = A * x + B
  //     flashLoanTotal = (x - 1) * C = x * C - C
  //     x * C - C < A * x + B
  //     (C - A) * x < B + C
  //     x < (B + C) / (C - A)
  const collateralAmount = Decimal.fromBigInt(amount);

  const collateralFactor = market.getAssetConfiguration(collateralCoinType)!.collateralSetting()?.collateralFactor!;
  const collateralPrice = market.getDeposit(collateralCoinType).price();
  const collateralDecimals = Decimal.fromNumber(Math.pow(10, market.coinMetadatas.get(collateralCoinType)?.decimals!));

  const flashLoanFee = market.getAssetConfiguration(borrowCoinType)!.borrowSetting()!.flashLoanFeeRate;
  const surplus = obligation.surplus(market, targetLTV);

  const swapPrice = collateralPrice.mul(Decimal.one().add(swapSlippage));

  const A = collateralFactor.mul(collateralAmount).mul(targetLTV).divDecimal(Decimal.one().add(swapSlippage));
  const B = surplus.mul(collateralDecimals).divDecimal(swapPrice);
  const C = collateralAmount.mul(Decimal.one().add(flashLoanFee));

  if (C.lessThan(A)) {
    throw new Error("Bug in calculation");
  }

  return B.add(C).divDecimal(C.sub(A));
}

function getAmount(percentage: Decimal, amount: bigint): bigint {
  if (percentage.equals(Decimal.one())) { return amount; }
  return percentage.mulBigInt(amount).asBigInt();
}

function calculateAveragePriceAfterReduction(
  oldAmount: bigint,
  oldAveragePrice: Decimal,
  reducedAmount: bigint,
  currentPrice: Decimal,
): Decimal {
  const newAmount = oldAmount - reducedAmount;
  if (newAmount === 0n) {
    return Decimal.zero();
  }
  
  // (old_amount * old_avg_price - reduced_amount * current_price) / new_amount
  const oldValue = Decimal.fromBigInt(oldAmount).mul(oldAveragePrice);
  const reducedValue = Decimal.fromBigInt(reducedAmount).mul(currentPrice);
  const newValue = oldValue.sub(reducedValue);
  
  return newValue.divDecimal(Decimal.fromBigInt(newAmount));
}

function getCurrentMarketPriceRatio(
  collateralType: TypeName,
  debtType: TypeName,
  market: Market
): Decimal {
  const collateralPrice = market.getDeposit(collateralType).price();
  const debtPrice = market.getDeposit(debtType).price();
  return collateralPrice.divDecimal(debtPrice);
}

function getPrincipleCoinType(
  deposit: TypeName,
  borrow: TypeName,
  operation: LeverageOperationType
): TypeName {
  // sui long sui => borrow swap, deposit (SUI), borrow (USDC), principle (sui)
  // usdc long sui => swap borrow, deposit (SUI), borrow (USDC), principle (usdc)
  // sui short sui => swap borrow, deposit (USDC), borrow (SUI), principle (SUI)
  // usdc short sui => borrow swap, deposit (USDC), borrow (SUI), principle (USDC)
  return operation === LeverageOperationType.BorrowSwap ? deposit : borrow;
}

export function isLong(leftCoinType: TypeName, collateral: TypeName): boolean {
  return leftCoinType === collateral;
}