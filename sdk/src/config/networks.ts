import { TypeName } from "../market-types";

export interface Market {
  name: string;
  type: string; // Full Move type with package ID (e.g., "0x123...::market_type::MainMarket")
  objectId: string; // Market object ID
}

export interface LeverageMarket {
  lendingMarketType: TypeName;
  lendingMarketId: string;
  lendingMarketName: string;
  objectId: string; // leverage market object ID
}

export interface NetworkConfig {
  protocolQueryPackageId: string;
  protocolPackageId: string;
  xOraclePackageId: string;
  coinDecimalsRegistryId: string;
  xOracleId: string;
  markets: Market[];
  leverageMarkets: LeverageMarket[];
  protocolAppId?: string;
  leveragePackageId: string;
}

export const NETWORK_CONFIGS: Record<string, NetworkConfig> = {
    testnet: {
      protocolQueryPackageId: "0xffb136790a1a8ca6de6bbeb1d7410308a62881e84378b93f3322214589186ef9",
      protocolPackageId: "0x4e4afd5b9013cece38ac93ccb263e2aebc6667337c9a070f12b034b445c9f001",
      xOraclePackageId: "0x8d7f93f84a3eb5cbd04799b7ccbcdda1cc2b02584bc4f2516ee4e9be4acda0b6",
      coinDecimalsRegistryId: "0xf753661e71d0563c02c0ec4a1f55eb35269ec133c3691c12c17b344c2f9bf865",
      xOracleId: "0x38fe27601401299a36a617a0e960a9c2db0a035e96ec3a9f31ffcda8907f34ee",
      markets: [
        {
          name: "MainMarket",
          type: "0x810fb7f16beb3f6c2b688fd06365decfa064997f9cdf6f818a97aee22c98dff3::market_type::MainMarket",
          objectId: "0x5d06d839807796a49da0c4bf944015a19d7bef8320d3665d1acc61732170e484"
        }
      ],
      leverageMarkets: [],
      protocolAppId: "0x2f7d10e3d3a2855951a04b62f83c13afdfeccb63a3f704ba56658c4b77cd1ae0",
      leveragePackageId: "",
    },
    mainnet: {
      "protocolQueryPackageId": "0xeb89706ae10184c95ef5e7c8502e1eeaec7a23e5f41a92c9213f7cdf2b72f3e1",
      "protocolPackageId": "0x4efe2d4ea4457a898d050a311da458f11e049e2b8c4c40427b021e0406e38898",
      "xOraclePackageId": "0x988609a6772a8ce45037fa78bdc1eda593c5872c16dd883002c8aa4939a2a7bb",
      "coinDecimalsRegistryId": "0xc9252d7773e718de38af1dbcea85c258d6a67fff6f7e71c1fb146066e6e93f65",
      "xOracleId": "0xfdea4f99e70981bdda2d79af5ee442384b84c5e3fb24130d895cb21c3ecf5d85",
      "markets": [
        {
          "name": "MainMarket",
          "type": "0xa61de42b812c9b385b433db1421de3631145f262fd02e4b219a3827ac39eaf4a::market_type::MainMarket",
          "objectId": "0xed0ce305e36ba765fdee6fac6e0827305963039a7778839559bff25bfdff8aad"
        }
      ],
      "leverageMarkets": [
        {
          "lendingMarketType": "0xa61de42b812c9b385b433db1421de3631145f262fd02e4b219a3827ac39eaf4a::market_type::MainMarket",
          "lendingMarketId": "0xed0ce305e36ba765fdee6fac6e0827305963039a7778839559bff25bfdff8aad",
          "lendingMarketName": "MainMarket",
          "objectId": "0xf69b571276a608585b2b251a9fe162358a25bddff2d855778d815031b27cbef0"
        }
      ],
      "leveragePackageId": "0x1e7d7781d4e316250edf8f0d03af7abfe4b16d1dce3287bf50475bc02bc6268a",
      "protocolAppId": "0x690d3d2bdec8b6121cfbb44604498728da59fc0aab3fb29dfa678f0d1d81d4e1"
    }
  };

// Move types for obligation-related objects
export const MOVE_TYPES = {
  testnet: {
    ObligationOwnerCap: `${NETWORK_CONFIGS.testnet.protocolPackageId}::obligation::ObligationOwnerCap`,
    ObligationMainMarket: `${NETWORK_CONFIGS.testnet.protocolPackageId}::obligation::Obligation<${NETWORK_CONFIGS.testnet.markets[0].type}>`,
  },
  mainnet: {
    ObligationOwnerCap: `${NETWORK_CONFIGS.mainnet.protocolPackageId}::obligation::ObligationOwnerCap`,
    ObligationMainMarket: `${NETWORK_CONFIGS.mainnet.protocolPackageId}::obligation::Obligation<${NETWORK_CONFIGS.mainnet.markets[0].type}>`,
  },
};

// Helper function to get network config
export function getNetworkConfig(network: string): NetworkConfig {
  const config = NETWORK_CONFIGS[network];
  if (!config) {
    throw new Error(`Network configuration not found for: ${network}`);
  }
  return config;
}

// Helper function to get market by name
export function getMarket(network: string, marketName: string): Market {
  const config = getNetworkConfig(network);
  const market = config.markets.find(m => m.name === marketName);
  if (!market) {
    throw new Error(`Market '${marketName}' not found on ${network}`);
  }
  return market;
}

// Helper function to get market ID by name (for backward compatibility)
export function getMarketId(network: 'testnet' | 'mainnet', marketName: string): string {
  const market = getMarket(network, marketName);
  return market.objectId;
}

// Helper function to get market type by name
export function getMarketType(network: 'testnet' | 'mainnet', marketName: string): string {
  const market = getMarket(network, marketName);
  return market.type;
}

// Helper function to get Move types for a network
export function getMoveTypes(network: 'testnet' | 'mainnet') {
  const types = MOVE_TYPES[network];
  if (!types) {
    throw new Error(`Move types not found for network: ${network}`);
  }
  return types;
}