import { getFullnodeUrl, SuiClient, SuiTransactionBlockResponse } from '@mysten/sui/client';
import { Transaction, TransactionObjectInput } from '@mysten/sui/transactions';
import { bcs } from '@mysten/sui/bcs';
import { SuiPriceServiceConnection } from '@pythnetwork/pyth-sui-js';
import { Keypair } from '@mysten/sui/cryptography';
import { MarketData, AssetData } from '../market-types/market';
import { ObligationData } from '../market-types/obligation';
import { TypeName, AssetBorrow, AssetDeposit, AssetValuation } from '../market-types/assets';
import { Decimal } from '../market-types/decimal';
import { TransactionOptions, TransactionResult, Signer, WalletAdapter } from '../market-types';
import { NetworkConfig, getNetworkConfig, Market as ConfigMarket, getMoveTypes } from '../config/networks';
import { updatePythPriceByTxn } from '../utils/pyth-manager';
import { getPythConfig } from '../utils/pyth-utils';
import { waitForTransaction } from '../utils/transaction-utils';

export type TxnHash = string;

export interface PebbleClientConfig {
  network: 'mainnet' | 'testnet',
  suiEndpoint?: string,
  pythEndpoint?: string,
}

export class PebbleClient {
  readonly provider: SuiClient;
  readonly config: NetworkConfig;
  private network: 'testnet' | 'mainnet';
  private pythConnnection: SuiPriceServiceConnection;

  private constructor(config: PebbleClientConfig) {
    this.network = config.network;
    this.config = getNetworkConfig(config.network);

    const suiURL = config.suiEndpoint || getFullnodeUrl(config.network);
    this.provider = new SuiClient({ url: suiURL });

    let pythUrl = config.pythEndpoint;
    if (!pythUrl) {
      const p = getPythConfig(config.network);
      pythUrl = p.hermesEndpoint;
    }
    this.pythConnnection = new SuiPriceServiceConnection(pythUrl);
  }

  public static fromConfig(config: PebbleClientConfig): PebbleClient {
    return new PebbleClient(config);
  }

  public static fromNetwork(network: string): PebbleClient {
    if (network === "mainnet") {
      return PebbleClient.mainnet();
    }
    return PebbleClient.testnet();
  }

  public static mainnet(): PebbleClient {
    return new PebbleClient({ network: 'mainnet' });
  }

  public static testnet(): PebbleClient {
    return new PebbleClient({ network: 'testnet' });
  }

  /**
   * Creates fresh BCS schemas - must be called for each parsing operation
   * to avoid state conflicts in the BCS library
   */
  public createBcsSchemas() {
    const decimalSchema = bcs.struct('Decimal', {
      value: bcs.u256(),
    });

    const assetValuationSchema = bcs.struct('AssetValuation', {
      coin_type: bcs.string(),
      amount: bcs.u64(),
      usd: decimalSchema,
      price: decimalSchema,
    });

    const assetBorrowSchema = bcs.struct('AssetBorrow', {
      valuation: assetValuationSchema,
      borrow_index: decimalSchema,
    });

    const assetDepositSchema = bcs.struct('AssetDeposit', {
      is_collateral: bcs.bool(),
      valuation: assetValuationSchema,
      ctoken_amount: bcs.u64(),
      exchange_rate: decimalSchema,
    });

    const interestModelSchema = bcs.struct('InterestModel', {
      base_borrow_rate_per_sec: decimalSchema,
      borrow_rate_on_mid_kink: decimalSchema,
      mid_kink: decimalSchema,
      borrow_rate_on_high_kink: decimalSchema,
      high_kink: decimalSchema,
      max_borrow_rate: decimalSchema,
    });

    const borrowConfigSchema = bcs.struct('BorrowConfig', {
      min_borrow_amount: bcs.u64(),
      max_borrow_amount: bcs.u64(),
      max_deposit_amount: bcs.u64(),
      flash_loan_fee_rate: decimalSchema,
      repay_fee_rate: decimalSchema,
    });

    const collateralSchema = bcs.struct('Collateral', {
      collateral_factor: decimalSchema,
      liquidation_incentive: decimalSchema,
      liquidation_revenue_factor: decimalSchema,
    });

    const assetDataSchema = bcs.struct('AssetData', {
      coin_type: bcs.string(),
      interest_model: interestModelSchema,
      utilization_rate: decimalSchema,
      collateral_setting: bcs.option(collateralSchema),
      borrow_paused: bcs.bool(),
      deposit_paused: bcs.bool(),
      withdraw_paused: bcs.bool(),
      asset_setting: borrowConfigSchema,
      deposit_usage: assetDepositSchema,
      borrow_usage: assetBorrowSchema,
    });

    const marketDataSchema = bcs.struct('MarketData', {
      assets: bcs.vector(assetDataSchema),
    });

    return {
      decimalSchema,
      assetValuationSchema,
      assetBorrowSchema,
      assetDepositSchema,
      interestModelSchema,
      borrowConfigSchema,
      collateralSchema,
      assetDataSchema,
      marketDataSchema,
    };
  }

  public async getMarketAssetDetail(marketObjectId: string, marketType: TypeName, asset: TypeName): Promise<AssetData> {
    const tx = new Transaction();

    await this.refreshPythOracle(tx, [asset]);

    tx.moveCall({
      target: `${this.config.protocolQueryPackageId}::market_query::get_asset_detail`,
      arguments: [
        tx.object(marketObjectId),
        tx.pure.string(asset),
        tx.object(this.config.coinDecimalsRegistryId),
        tx.object(this.config.xOracleId),
        tx.object('0x6'), // Clock object ID
      ],
      typeArguments: [marketType],
    });

    const result = await this.provider.devInspectTransactionBlock({
      transactionBlock: tx,
      sender: '0x0000000000000000000000000000000000000000000000000000000000000000',
    });

    if (!result.results || result.results.length === 0) {
      throw new Error('No results from market detail query');
    }

    return this.parseAssetData(result.results[result.results.length - 1].returnValues![0][0]);
  }

  public async getMarketDetail(marketObjectId: string, marketType: TypeName): Promise<MarketData> {
    // First get all supported assets from the market
    const supportedAssets = await this.getSupportedAssets(marketObjectId, marketType);

    const tx = new Transaction();

    await this.refreshPythOracle(tx, supportedAssets);

    tx.moveCall({
      target: `${this.config.protocolQueryPackageId}::market_query::get_market_detail`,
      arguments: [
        tx.object(marketObjectId),
        tx.object(this.config.coinDecimalsRegistryId),
        tx.object(this.config.xOracleId),
        tx.object('0x6'), // Clock object ID
      ],
      typeArguments: [marketType],
    });

    const result = await this.provider.devInspectTransactionBlock({
      transactionBlock: tx,
      sender: '0x0000000000000000000000000000000000000000000000000000000000000000',
    });

    if (!result.results || result.results.length === 0) {
      throw new Error('No results from market detail query');
    }
    return this.parseMarketData(result.results[result.results.length - 1].returnValues![0][0]);
  }

  public async getMarketDetailByName(marketName: string): Promise<MarketData> {
    const market = this.listMarkets().find(m => m.name === marketName);
    if (!market) {
      throw new Error(`Market '${marketName}' not found`);
    }

    return await this.getMarketDetail(market.objectId, market.type);
  }

  public async getSupportedAssets(marketObjectId: string, marketType: TypeName): Promise<TypeName[]> {
    const tx = new Transaction();

    // Call the public supported_assets method directly on the market
    tx.moveCall({
      target: `${this.config.protocolPackageId}::market::supported_assets`,
      arguments: [
        tx.object(marketObjectId),
      ],
      typeArguments: [marketType],
    });

    const result = await this.provider.devInspectTransactionBlock({
      transactionBlock: tx,
      sender: '0x0000000000000000000000000000000000000000000000000000000000000000',
    });

    if (!result.results || result.results.length === 0) {
      throw new Error('No results from supported assets query');
    }

    const supportedAssets = bcs.vector(bcs.String).parse(Uint8Array.from(result.results[0].returnValues![0][0]));
    return supportedAssets;
  }

  public async getObligationOwnerCapDetail(obligationOwnerCapId: string): Promise<{obligationId: string, marketType: TypeName, marketId: string }> {
    const res = await this.provider.getObject({
      id: obligationOwnerCapId,  // ← the ID of the ObligationOwnerCap object
      options: { showContent: true },
    });
    if (res.data?.content?.dataType === 'moveObject') {
      const fields = res.data.content.fields as any;
      const obligationId = fields.obligation_id;
      const marketType = fields.market_type.fields.name;
      const marketId = fields.market_id;
      return { obligationId, marketId, marketType };
    } else {
      throw new Error('Not a MoveObject or missing content.');
    }
  }

  public async getObligationIdFromOwnerCapId(obligationOwnerCapId: string): Promise<string> {
    const res = await this.provider.getObject({
      id: obligationOwnerCapId,  // ← the ID of the ObligationOwnerCap object
      options: { showContent: true },
    });
    console.log(res);
    if (res.data?.content?.dataType === 'moveObject') {
      const fields = res.data.content.fields as any;
      const obligationId = fields.obligation_id;
      return obligationId;
    } else {
      throw new Error('Not a MoveObject or missing content.');
    }
  }

  public async getObligationAssets(marketType: TypeName, marketID: string, obligationID: string): Promise<{ debtTypes: TypeName[], depositTypes: TypeName[] }> {
    const tx = new Transaction();

    tx.moveCall({
      target: `${this.config.protocolQueryPackageId}::obligation_query::get_obligation_assets`,
      arguments: [
        tx.object(marketID),
        tx.pure.id(obligationID),
      ],
      typeArguments: [marketType],
    });

    const simulation = await this.provider.devInspectTransactionBlock({
      transactionBlock: tx,
      sender: '0x0000000000000000000000000000000000000000000000000000000000000000',
    });

    if (!simulation.results || simulation.results.length === 0) {
      throw new Error('No results from obligation assets query');
    }

    const result = simulation.results[0];
    if (!result.returnValues || result.returnValues.length < 2) {
      throw new Error('Invalid return values from obligation assets query');
    }

    // Parse debt types (first return value)
    const debtTypes = bcs.vector(bcs.String).parse(Uint8Array.from(result.returnValues![0][0]));
    const depositTypes = bcs.vector(bcs.String).parse(Uint8Array.from(result.returnValues![1][0]));

    return { debtTypes, depositTypes };
  }

  public async getObligationDetail(marketType: TypeName, marketID: string, obligationID: string): Promise<ObligationData> {
    const { debtTypes, depositTypes } = await this.getObligationAssets(marketType, marketID, obligationID);
    const assets = [...debtTypes, ...depositTypes];

    const tx = new Transaction();
    await this.refreshPythOracle(tx, assets);

    tx.moveCall({
      target: `${this.config.protocolQueryPackageId}::obligation_query::get_obligation_detail`,
      arguments: [
        tx.object(marketID),
        tx.pure.id(obligationID),
        tx.object(this.config.coinDecimalsRegistryId),
        tx.object(this.config.xOracleId),
        tx.object('0x6'), // Clock object ID
      ],
      typeArguments: [marketType],
    });

    const result = await this.provider.devInspectTransactionBlock({
      transactionBlock: tx,
      sender: '0x0000000000000000000000000000000000000000000000000000000000000000',
    });

    if (!result.results || result.results.length === 0) {
      throw new Error('No results from obligation query');
    }

    return this.parseObligationData(result.results[result.results.length - 1].returnValues![0][0]);
  }

  public async borrow(
    marketObjectId: string,
    marketType: TypeName,
    obligationOwnerCapId: string,
    coinType: TypeName,
    borrowAmount: bigint,
    signer: Signer,
  ): Promise<TxnHash> {
    const tx = new Transaction();

    const obligationId = await this.getObligationIdFromOwnerCapId(obligationOwnerCapId);
    const { debtTypes, depositTypes } = await this.getObligationAssets(marketType, marketObjectId, obligationId);
    const allAssets = [...debtTypes, ...depositTypes];

    if (!allAssets.includes(coinType)) {
      allAssets.push(coinType);
    }

    await this.refreshPythOracle(tx, allAssets);

    const [borrowedCoin] = tx.moveCall({
      target: `${this.config.protocolPackageId}::borrow::borrow`,
      arguments: [
        tx.object(obligationOwnerCapId),
        tx.object(marketObjectId),
        tx.object(this.config.coinDecimalsRegistryId),
        tx.pure.u64(borrowAmount),
        tx.object(this.config.xOracleId),
        tx.object('0x6'), // Clock object
      ],
      typeArguments: [marketType, coinType],
    });

    // Transfer the borrowed coin to the signer
    const signerAddress = 'getPublicKey' in signer ? signer.getPublicKey().toSuiAddress() : signer.toSuiAddress();
    tx.transferObjects([borrowedCoin], tx.pure.address(signerAddress));

    // Execute the transaction
    const result = await this.executeTransaction(tx, signer);

    return result.digest;
  }

  public async borrowFromWallet(
    marketName: string,
    coinType: TypeName,
    borrowAmount: bigint,
    signer: Signer,
  ): Promise<TxnHash> {
    // Get market configuration
    const market = this.listMarkets().find(m => m.name === marketName);
    if (!market) {
      throw new Error(`Market '${marketName}' not found`);
    }

    // Note: We could check market liquidity here, but it requires additional Move query functions
    // For now, we'll rely on the transaction itself to fail with a clear error message

    // Get sender address
    const sender = 'getPublicKey' in signer ? signer.getPublicKey().toSuiAddress() : signer.toSuiAddress();

    // Fetch obligation owner caps from user's wallet
    const obligationOwnerCaps = await this.getObligationsFromWallet(sender);
    if (obligationOwnerCaps.length === 0) {
      throw new Error('No obligation owner caps found in wallet. Please enter the market first.');
    }

    // Use the first obligation owner cap
    const obligationOwnerCapId = obligationOwnerCaps[0];
    console.log(`Using obligation owner cap: ${obligationOwnerCapId}`);

    // Call the main borrow method
    return await this.borrow(
      market.objectId,
      market.type,
      obligationOwnerCapId,
      coinType,
      borrowAmount,
      signer,
    );
  }

  public async withdraw(
    marketObjectId: string,
    marketType: TypeName,
    obligationOwnerCapId: string,
    coinType: TypeName,
    withdrawCtokenAmount: bigint,
    signer: Signer,
  ): Promise<TxnHash> {
    const tx = new Transaction();

    // Get the obligation ID to fetch all existing assets
    const obligationId = await this.getObligationIdFromOwnerCapId(obligationOwnerCapId);
    const { debtTypes, depositTypes } = await this.getObligationAssets(marketType, marketObjectId, obligationId);
    const allAssets = [...debtTypes, ...depositTypes];
    if (!allAssets.includes(coinType)) {
      allAssets.push(coinType);
    }

    await this.refreshPythOracle(tx, allAssets);

    tx.moveCall({
      target: `${this.config.protocolPackageId}::withdraw::withdraw`,
      arguments: [
        tx.object(marketObjectId),
        tx.object(obligationOwnerCapId),
        tx.object(this.config.coinDecimalsRegistryId),
        tx.pure.u64(withdrawCtokenAmount),
        tx.object(this.config.xOracleId),
        tx.object('0x6'), // Clock object
      ],
      typeArguments: [marketType, coinType],
    });

    const result = await this.executeTransaction(tx, signer);
    return result.digest;
  }

  public async repay(
    marketObjectId: string,
    marketType: TypeName,
    obligationOwnerCapId: string,
    coinType: TypeName,
    coinObjectId: string,
    signer: Signer,
  ): Promise<TxnHash> {
    const tx = new Transaction();
    tx.moveCall({
      target: `${this.config.protocolPackageId}::repay::repay`,
      arguments: [
        tx.object(obligationOwnerCapId),
        tx.object(marketObjectId),
        tx.object(coinObjectId),
        tx.object('0x6'), // Clock object
      ],
      typeArguments: [marketType, coinType],
    });

    const result = await this.executeTransaction(tx, signer);

    return result.digest;
  }

  public async repayFromWallet(
    marketName: string,
    coinType: TypeName,
    coinObjectId: string,
    signer: Signer,
  ): Promise<TxnHash> {
    try {
      // Get market configuration
      const market = this.listMarkets().find(m => m.name === marketName);
      if (!market) {
        throw new Error(`Market '${marketName}' not found`);
      }

      // Get sender address
      const sender = 'getPublicKey' in signer ? signer.getPublicKey().toSuiAddress() : signer.toSuiAddress();

      // Fetch obligation owner caps from user's wallet
      const obligationOwnerCaps = await this.getObligationsFromWallet(sender);
      if (obligationOwnerCaps.length === 0) {
        throw new Error('No obligation owner caps found in wallet. Please enter the market first.');
      }

      // Use the first obligation owner cap
      const obligationOwnerCapId = obligationOwnerCaps[0];
      console.log(`Using obligation owner cap: ${obligationOwnerCapId}`);

      // Call the main repay method
      return await this.repay(
        market.objectId,
        market.type,
        obligationOwnerCapId,
        coinType,
        coinObjectId,
        signer,
      );

    } catch (error) {
      throw new Error(`Failed to repay from wallet: ${error}`);
    }
  }

  public async enterMarket(marketObjectId: string, marketType: TypeName, signer: Signer): Promise<TxnHash> {
    try {
      const tx = new Transaction();

      tx.moveCall({
        target: `${this.config.protocolPackageId}::enter_market::enter_market`,
        arguments: [
          tx.object(marketObjectId),
        ],
        typeArguments: [marketType],
      });

      const result = await this.executeTransaction(tx, signer);

      return result.digest;
    } catch (error) {
      throw new Error(`Failed to enter market: ${error}`);
    }
  }
  
  public populateEnterMarketTxn(txn: Transaction, marketObjectId: string, marketType: TypeName): TransactionObjectInput {
    return txn.moveCall({
      target: `${this.config.protocolPackageId}::enter_market::enter_market_return`,
      arguments: [
        txn.object(marketObjectId),
      ],
      typeArguments: [marketType],
    });
  }

  public async deposit(
    marketObjectId: string,
    marketType: TypeName,
    obligationOwnerCapId: string,
    coinType: TypeName,
    coinObjectId: string,
    signer: Signer,
  ): Promise<TxnHash> {
    const tx = new Transaction();

    tx.moveCall({
      target: `${this.config.protocolPackageId}::deposit::deposit`,
      arguments: [
        tx.object(obligationOwnerCapId),
        tx.object(marketObjectId),
        tx.object(coinObjectId),
        tx.object('0x6'), // Clock object
      ],
      typeArguments: [marketType, coinType],
    });

    const result = await this.executeTransaction(tx, signer);

    return result.digest;
  }

  public populatedDepositTxn(
    tx: Transaction,
    marketObjectId: string,
    marketType: TypeName,
    obligationOwnerCapId: TransactionObjectInput,
    coinType: TypeName,
    coinObject: any,
  ) {
    if (typeof (coinObject) === 'string') {
      coinObject = tx.object(coinObject);
    }

    tx.moveCall({
      target: `${this.config.protocolPackageId}::deposit::deposit`,
      arguments: [
        tx.object(obligationOwnerCapId),
        tx.object(marketObjectId),
        coinObject,
        tx.object('0x6'), // Clock object
      ],
      typeArguments: [marketType, coinType],
    });

  }

  public populateRepayTxn(
    tx: Transaction,
    marketObjectId: string,
    marketType: TypeName,
    obligationOwnerCapId: string,
    coinType: TypeName,
    coinObject: any,
  ) {
    if (typeof (coinObject) === 'string') {
      coinObject = tx.object(coinObject);
    }

    tx.moveCall({
      target: `${this.config.protocolPackageId}::repay::repay`,
      arguments: [
        tx.object(obligationOwnerCapId),
        tx.object(marketObjectId),
        coinObject,
        tx.object('0x6'), // Clock object
      ],
      typeArguments: [marketType, coinType],
    });
  }

  public async populateLiquidateTxn(
    tx: Transaction,
    marketObjectId: string,
    marketType: TypeName,
    obligationId: string,
    debtType: TypeName,
    collateralType: TypeName,
    repayCoinObjectId: TransactionObjectInput,
  ): Promise<void> {
    // Get obligation's existing assets for oracle refresh
    const { debtTypes, depositTypes } = await this.getObligationAssets(marketType, marketObjectId, obligationId);
    const allAssets = [...new Set([...debtTypes, ...depositTypes])];

    // Refresh oracle prices for all relevant assets
    await this.refreshPythOracle(tx, allAssets);

    // Call the liquidate function
    tx.moveCall({
      target: `${this.config.protocolPackageId}::liquidate::liquidate`,
      arguments: [
        tx.pure.id(obligationId),
        tx.object(marketObjectId),
        tx.object(repayCoinObjectId),
        tx.object(this.config.coinDecimalsRegistryId),
        tx.object(this.config.xOracleId),
        tx.object('0x6'), // Clock object
      ],
      typeArguments: [marketType, debtType, collateralType],
    });
  }

  public listMarkets(): ConfigMarket[] {
    const config = getNetworkConfig(this.network);
    return config.markets;
  }

  public async getObligationsFromWallet(address: string): Promise<string[]> {
    try {
      const moveTypes = getMoveTypes(this.network);

      // Get obligation owner caps from wallet
      const obligationOwnerCapType = moveTypes.ObligationOwnerCap;

      const result = await this.provider.getOwnedObjects({
        owner: address,
        filter: {
          StructType: obligationOwnerCapType,
        },
        options: {
          showContent: true,
          showType: true,
        },
      });

      const obligationOwnerCaps: string[] = [];

      for (const obj of result.data) {
        if (obj.data && obj.data.objectId) {
          obligationOwnerCaps.push(obj.data.objectId);
        }
      }

      return obligationOwnerCaps;
    } catch (error) {
      throw new Error(`Failed to fetch obligations from wallet: ${error}`);
    }
  }

  public async refreshPythOracle(
    tx: Transaction,
    assets: TypeName[],
  ): Promise<Transaction> {
    // Get Pyth configuration for the network
    const pythConfig = getPythConfig(this.network);

    // Get Pyth state IDs
    const stateIds = {
      pyth: pythConfig.pythStateId,
      wormhole: pythConfig.wormholeStateId,
    };

    for (const assetType of assets) {
      const coinSymbol = this.extractCoinSymbol(assetType);
      const feedConfig = pythConfig.priceFeeds[coinSymbol];

      if (!feedConfig) {
        throw Error(`No Pyth price feed found for coin: ${coinSymbol}`);
      }

      const priceInfoObject = await updatePythPriceByTxn(
        tx,
        this.provider,
        stateIds,
        this.pythConnnection,
        feedConfig.priceFeedId,
      );

      tx.moveCall({
        target: `${this.config.xOraclePackageId}::x_oracle::refresh_pyth_price`,
        typeArguments: [assetType],
        arguments: [
          tx.object(this.config.xOracleId),
          tx.object(pythConfig.pythStateId),
          tx.object(priceInfoObject),
          tx.object('0x6'),
        ],
      });
    }

    return tx;
  }

  private parseAssetData(returnValues: any): AssetData {
    if (!returnValues) {
      throw new Error('No return values provided for market data parsing');
    }
    // Create fresh BCS schemas
    const { assetDataSchema } = this.createBcsSchemas();

    // Parse the BCS-encoded return value
    const bcsBytes = new Uint8Array(returnValues);

    const asset = assetDataSchema.parse(bcsBytes);
    const depositValuation = {
      coinType: asset.deposit_usage.valuation.coin_type,
      amount: BigInt(asset.deposit_usage.valuation.amount),
      usd: new Decimal(BigInt(asset.deposit_usage.valuation.usd.value)),
      price: new Decimal(BigInt(asset.deposit_usage.valuation.price.value)),
    };

    const borrowValuation = {
      coinType: asset.borrow_usage.valuation.coin_type,
      amount: BigInt(asset.borrow_usage.valuation.amount),
      usd: new Decimal(BigInt(asset.borrow_usage.valuation.usd.value)),
      price: new Decimal(BigInt(asset.borrow_usage.valuation.price.value)),
    };

    return {
      coinType: asset.coin_type,
      interestModel: {
        type: 'kinked_rate_model',
        params: {
          baseBorrowRatePerSec: new Decimal(BigInt(asset.interest_model.base_borrow_rate_per_sec.value)),
          borrowRateOnMidKink: new Decimal(BigInt(asset.interest_model.borrow_rate_on_mid_kink.value)),
          midKink: new Decimal(BigInt(asset.interest_model.mid_kink.value)),
          borrowRateOnHighKink: new Decimal(BigInt(asset.interest_model.borrow_rate_on_high_kink.value)),
          highKink: new Decimal(BigInt(asset.interest_model.high_kink.value)),
          maxBorrowRate: new Decimal(BigInt(asset.interest_model.max_borrow_rate.value)),
        },
      },
      utilizationRate: new Decimal(BigInt(asset.utilization_rate.value)),
      collateralSetting: asset.collateral_setting ? {
        collateralFactor: new Decimal(BigInt(asset.collateral_setting.collateral_factor.value)),
        liquidationIncentive: new Decimal(BigInt(asset.collateral_setting.liquidation_incentive.value)),
        liquidationRevenueFactor: new Decimal(BigInt(asset.collateral_setting.liquidation_revenue_factor.value)),
      } : undefined,
      borrowPaused: asset.borrow_paused,
      depositPaused: asset.deposit_paused,
      withdrawPaused: asset.withdraw_paused,
      assetSetting: {
        minBorrowAmount: BigInt(asset.asset_setting.min_borrow_amount),
        maxBorrowAmount: BigInt(asset.asset_setting.max_borrow_amount),
        maxDepositAmount: BigInt(asset.asset_setting.max_deposit_amount),
        flashLoanFeeRate: new Decimal(BigInt(asset.asset_setting.flash_loan_fee_rate.value)),
        repayFeeRate: new Decimal(BigInt(asset.asset_setting.repay_fee_rate.value)),
      },
      depositUsage: new AssetDeposit(
        asset.deposit_usage.is_collateral,
        depositValuation,
        BigInt(asset.deposit_usage.ctoken_amount),
        new Decimal(BigInt(asset.deposit_usage.exchange_rate.value)),
      ),
      borrowUsage: new AssetBorrow(
        borrowValuation,
        new Decimal(BigInt(asset.borrow_usage.borrow_index.value)),
      ),
    };
  }

  private parseMarketData(returnValues: any): MarketData {
    if (!returnValues) {
      throw new Error('No return values provided for market data parsing');
    }

    // Parse the BCS-encoded return value
    const bcsBytes = new Uint8Array(returnValues);

    const { marketDataSchema } = this.createBcsSchemas();
    const parsedData = marketDataSchema.parse(bcsBytes);
      
    // Convert parsed data to our TypeScript types
    const assets: AssetData[] = parsedData.assets.map((asset: any) => {
      const depositValuation = {
        coinType: asset.deposit_usage.valuation.coin_type,
        amount: BigInt(asset.deposit_usage.valuation.amount),
        ctokenAmount: BigInt(asset.deposit_usage.ctoken_amount),
        usd: new Decimal(BigInt(asset.deposit_usage.valuation.usd.value)),
        price: new Decimal(BigInt(asset.deposit_usage.valuation.price.value)),
      };
      const borrowValuation = {
        coinType: asset.borrow_usage.valuation.coin_type,
        amount: BigInt(asset.borrow_usage.valuation.amount),
        usd: new Decimal(BigInt(asset.borrow_usage.valuation.usd.value)),
        price: new Decimal(BigInt(asset.borrow_usage.valuation.price.value)),
      };
      return {
        coinType: asset.coin_type,
        interestModel: {
          type: 'kinked_rate_model',
          params: {
            baseBorrowRatePerSec: new Decimal(BigInt(asset.interest_model.base_borrow_rate_per_sec.value)),
            borrowRateOnMidKink: new Decimal(BigInt(asset.interest_model.borrow_rate_on_mid_kink.value)),
            midKink: new Decimal(BigInt(asset.interest_model.mid_kink.value)),
            borrowRateOnHighKink: new Decimal(BigInt(asset.interest_model.borrow_rate_on_high_kink.value)),
            highKink: new Decimal(BigInt(asset.interest_model.high_kink.value)),
            maxBorrowRate: new Decimal(BigInt(asset.interest_model.max_borrow_rate.value)),
          },
        },
        utilizationRate: new Decimal(BigInt(asset.utilization_rate.value)),
        collateralSetting: asset.collateral_setting ? {
          collateralFactor: new Decimal(BigInt(asset.collateral_setting.collateral_factor.value)),
          liquidationIncentive: new Decimal(BigInt(asset.collateral_setting.liquidation_incentive.value)),
          liquidationRevenueFactor: new Decimal(BigInt(asset.collateral_setting.liquidation_revenue_factor.value)),
        } : undefined,
        borrowPaused: asset.borrow_paused,
        depositPaused: asset.deposit_paused,
        withdrawPaused: asset.withdraw_paused,
        assetSetting: {
          minBorrowAmount: BigInt(asset.asset_setting.min_borrow_amount),
          maxBorrowAmount: BigInt(asset.asset_setting.max_borrow_amount),
          maxDepositAmount: BigInt(asset.asset_setting.max_deposit_amount),
          flashLoanFeeRate: new Decimal(BigInt(asset.asset_setting.flash_loan_fee_rate.value)),
          repayFeeRate: new Decimal(BigInt(asset.asset_setting.repay_fee_rate.value)),
        },
        depositUsage: new AssetDeposit(
          asset.deposit_usage.is_collateral,
          depositValuation,
          BigInt(asset.deposit_usage.ctoken_amount),
          new Decimal(BigInt(asset.deposit_usage.exchange_rate.value)),
        ),
        borrowUsage: new AssetBorrow(
          borrowValuation,
          new Decimal(BigInt(asset.borrow_usage.borrow_index.value)),
        ),
      };
    });

    return { assets };
  }

  private parseObligationData(returnValues: any): ObligationData {
    if (!returnValues) {
      throw new Error('No return values provided for obligation data parsing');
    }
    // Create fresh BCS schemas
    const { assetBorrowSchema, assetDepositSchema } = this.createBcsSchemas();

      const obligationDataSchema = bcs.struct('ObligationData', {
        borrows: bcs.vector(assetBorrowSchema),
        deposits: bcs.vector(assetDepositSchema),
      });

      // Parse the BCS-encoded return value
      const bcsBytes = new Uint8Array(returnValues);

      // Check if we have empty data (just two empty vectors)
      if (bcsBytes.length === 2 && bcsBytes[0] === 0 && bcsBytes[1] === 0) {
        return { borrows: [], deposits: [] };
      }

      const parsedData = obligationDataSchema.parse(bcsBytes);
      // Handle empty arrays gracefully
      const borrows: AssetBorrow[] = parsedData.borrows?.map((borrow: any) => {
        const valuation: AssetValuation = {
          coinType: borrow.valuation.coin_type,
          amount: BigInt(borrow.valuation.amount),
          usd: new Decimal(BigInt(borrow.valuation.usd.value)),
          price: new Decimal(BigInt(borrow.valuation.price.value)),
        };

        return new AssetBorrow(
          valuation,
          new Decimal(BigInt(borrow.borrow_index.value)),
        );
      }) || [];

      const deposits: AssetDeposit[] = parsedData.deposits?.map((deposit: any) => {
        const valuation: AssetValuation = {
          coinType: deposit.valuation.coin_type,
          amount: BigInt(deposit.valuation.amount),
          usd: new Decimal(BigInt(deposit.valuation.usd.value)),
          price: new Decimal(BigInt(deposit.valuation.price.value)),
        };

        return new AssetDeposit(
          deposit.is_collateral,
          valuation,
          BigInt(deposit.ctoken_amount),
          new Decimal(BigInt(deposit.exchange_rate.value)),
        );
      }) || [];

      return { borrows, deposits };
  }

  private extractCoinSymbol(coinType: TypeName): string {
    // Extract coin symbol from Move type
    // Example: "0x123...::usdt::USDT" -> "USDT"
    // Example: "0x123...::eth::ETH" -> "ETH"

    const parts = coinType.split('::');
    if (parts.length >= 3) {
      return parts[parts.length - 1].toUpperCase();
    }

    // Fallback: try to extract from module name
    if (parts.length >= 2) {
      return parts[parts.length - 2].toUpperCase();
    }

    throw new Error(`Cannot extract coin symbol from type: ${coinType}`);
  }

  /**
   * Execute a transaction with a signer
   */
  public async executeTransaction(
    transaction: Transaction,
    signer: Signer,
    options: TransactionOptions = {}
  ): Promise<TransactionResult> {
    const signerAddress = 'getPublicKey' in signer ? signer.getPublicKey().toSuiAddress() : signer.toSuiAddress();
    const simulated = await this.provider.devInspectTransactionBlock(
      {
        transactionBlock: transaction,
        sender: signerAddress,
      }
    );

    if (simulated.effects.status.status !== "success") {
      throw new Error("txn most likely to fail");
    }

    // Execute the transaction
    let result: SuiTransactionBlockResponse;
    // Keypair
    if ('getSecretKey' in signer) {
      result = await this.provider.signAndExecuteTransaction({
        signer: signer as Keypair,
        transaction,
        options: {
          showEffects: true,
          showEvents: true,
          showObjectChanges: true,
          showBalanceChanges: true,
        },
      });
    } else {
      // WalletAdapter
      result = await (signer as WalletAdapter).signAndExecuteTransaction(transaction);
    }

    // Wait for transaction confirmation
    await waitForTransaction(this.provider, result.digest);

    return {
      digest: result.digest,
      effects: result.effects,
      events: result.events,
      objectChanges: result.objectChanges,
      balanceChanges: result.balanceChanges,
    };
  }
}
