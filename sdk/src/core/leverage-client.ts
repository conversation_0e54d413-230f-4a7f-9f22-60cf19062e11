import { Transaction, TransactionObjectArgument, TransactionObjectInput } from '@mysten/sui/transactions';
import { bcs } from '@mysten/sui/bcs';
import { PebbleClient } from './client';
import { TypeName, Signer, TransactionResult, Market, Decimal, Obligation, ObligationData } from '../market-types';
import { AggregatorClient } from '@cetusprotocol/aggregator-sdk';
import { LeverageObligation, LeverageObligationDetail, LeverageOperation, LeverageOperationType, LeveragePositionInfo } from '../leverage-types';
import { getSender } from '../utils/transaction-utils';
import { parseCoinDecimals } from '../utils/coin-metadata';

export interface LeverageClientParams {
  leverageMarketId: string;
  leveragePackageId: string;
}

export interface LeverageMarketConfig {
  leverageMarketId: string;
  leveragePackageId: string;
  lendingMarketId: string;
  lendingMarketType: TypeName;
}


export class LeverageClient {
  private swapAggregator: AggregatorClient;
  readonly pebbleClient: PebbleClient;
  readonly leverageConfig: LeverageMarketConfig;

  constructor(swapAggregator: AggregatorClient, pebbleClient: PebbleClient, params: LeverageClientParams) {
    this.swapAggregator = swapAggregator;
    this.pebbleClient = pebbleClient;
    
    // Get network config from pebble client
    const networkConfig = this.pebbleClient.config;
    
    // Find matching leverage market configuration
    const leverageMarket = networkConfig.leverageMarkets.find(
      market => market.objectId === params.leverageMarketId
    );
    
    if (!leverageMarket) {
      throw new Error(`Leverage market with ID ${params.leverageMarketId} not found in network configuration`);
    }
    
    // Set leverage config with lending market info from network config
    this.leverageConfig = {
      leverageMarketId: params.leverageMarketId,
      leveragePackageId: params.leveragePackageId,
      lendingMarketId: leverageMarket.lendingMarketId,
      lendingMarketType: leverageMarket.lendingMarketType,
    };
  }

  public async obtainUnderlyingMarket(): Promise<Market> {
    const marketDetails = await this.pebbleClient.getMarketDetail(this.leverageConfig.lendingMarketId, this.leverageConfig.lendingMarketType);
    return await Market.new(this.leverageConfig.lendingMarketType, this.leverageConfig.lendingMarketId, marketDetails, this.pebbleClient.provider);
  }

  public async getObligationSummary(leverageOwnerCapId: string): Promise<LeverageObligation> {
    const leverageObligation = await this.getLeverageObligationDetail(leverageOwnerCapId);
    const lendingObligation = new Obligation(await this.pebbleClient.getObligationDetail(
      this.leverageConfig.lendingMarketType,
      this.leverageConfig.lendingMarketId,
      leverageObligation.lendingObligationId,
    ));
    return new LeverageObligation(leverageObligation, lendingObligation);
  }

  public async getObligationsFromWallet(address: string): Promise<string[]> {
    // Get obligation owner caps from wallet
    const obligationOwnerCapType = `${this.leverageConfig.leveragePackageId}::leverage_obligation::LeverageMarketOwnerCap`;
  
    const result = await this.pebbleClient.provider.getOwnedObjects({
      owner: address,
      filter: {
        StructType: obligationOwnerCapType,
      },
      options: {
        showContent: true,
        showType: true,
      },
    });
  
    const obligationOwnerCaps: string[] = [];
  
    for (const obj of result.data) {
      if (obj.data && obj.data.objectId) {
        obligationOwnerCaps.push(obj.data.objectId);
      }
    }

    return obligationOwnerCaps;
  }

  public async getCollateralAmount(
    leverageOwnerCapId: string,
    collateralType: TypeName
  ): Promise<{ collateralAmount: bigint; ctokenAmount: bigint; exchangeRate: Decimal }> {
    const tx = new Transaction();
    
    tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_obligation::collateral_amount`,
      arguments: [
        tx.object(leverageOwnerCapId),
        tx.object(this.leverageConfig.lendingMarketId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, collateralType],
    });

    const inspectResult = await this.pebbleClient.provider.devInspectTransactionBlock({
      transactionBlock: tx,
      sender: '0x0000000000000000000000000000000000000000000000000000000000000000',
    });

    if (!inspectResult.results || inspectResult.results.length === 0) {
      throw new Error('Failed to query collateral amount');
    }

    const returnValues = inspectResult.results[0].returnValues;
    if (!returnValues || returnValues.length < 3) {
      throw new Error('Invalid return values from collateral_amount query');
    }

    // Decode the return values - first two are u64, last is Decimal
    const collateralAmount = BigInt(bcs.u64().parse(new Uint8Array(returnValues[0][0])));
    const ctokenAmount = BigInt(bcs.u64().parse(new Uint8Array(returnValues[1][0])));
    
    // Get decimal schema from client and decode
    const schemas = this.pebbleClient.createBcsSchemas();
    const exchangeRateStruct = schemas.decimalSchema.parse(new Uint8Array(returnValues[2][0]));
    const exchangeRate = new Decimal(BigInt(exchangeRateStruct.value));

    return {
      collateralAmount,
      ctokenAmount,
      exchangeRate,
    };
  }

  public async getLeverageObligationDetail(leverageOwnerCapId: string): Promise<LeverageObligationDetail> {
    const res = await this.pebbleClient.provider.getObject({
      id: leverageOwnerCapId,
      options: { showContent: true },
    });

    if (res.data?.content?.dataType === 'moveObject') {
      const fields = res.data.content.fields as any;
      
      // Parse the position info if it exists
      let info: LeveragePositionInfo | null = null;

      const operation = parseLeverageOperationTypeFromStr(fields.info.fields.operation.variant);

      if (fields.info && fields.info.fields) {
        info = {
          operation,
          deposit: fields.info.fields.deposit.fields.name,
          borrow: fields.info.fields.borrow.fields.name,
          amount: fields.info.fields.amount,
          average_price: new Decimal(BigInt(fields.info.fields.average_price.fields.value)),
        };
      }

      const lendingObligationId = fields.obligation_owner_cap?.fields?.obligation_id;
      return {
        lendingObligationId,
        info,
      };
    }

    throw new Error('Failed to fetch leverage obligation details');
  }

  public async populateDepositTransaction(
    tx: Transaction,
    ownerCapId: string,
    leverageObligation: LeverageObligation,
    coinObjectId: TransactionObjectInput,
  ): Promise<void> {
    const info = leverageObligation.leverageObligation.info!;
    await this.refreshLeveragePrice(tx, [info.deposit, info.borrow]);
    
    tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_delegate::deposit`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(ownerCapId),

        tx.object(coinObjectId),

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, info.deposit, info.borrow],
    });
  }

  public async populateRepayTransaction(
    tx: Transaction,
    ownerCapId: string,
    leverageObligation: LeverageObligation,
    coinObjectId: TransactionObjectInput,
    signer: Signer,
  ): Promise<void> {
    const info = leverageObligation.leverageObligation.info!;
    await this.refreshLeveragePrice(tx, [info.deposit, info.borrow]);
    
    const refundCoin = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_delegate::repay`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(ownerCapId),
        tx.object(coinObjectId),

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, info.borrow],
    });

    // Transfer any refunded coins back to signer
    tx.transferObjects([refundCoin], getSender(signer));
  }

  private async populateWithdrawLeverageTransaction(
    tx: Transaction,

    ownerCapId: string,
    leverageObligation: LeverageObligation,

    amount: bigint,
  ): Promise<TransactionObjectArgument> {
    const info = leverageObligation.leverageObligation.info!;

    await this.refreshLeveragePrice(tx, [info.deposit, info.borrow]);
    console.log("here", [info.deposit, info.borrow]);
    return tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_collateral_coin::withdraw_leverage`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(ownerCapId),

        tx.pure.u64(amount),

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, info.deposit, info.borrow],
    });
  }

  private async populateWithdrawSizeTransaction(
    tx: Transaction,

    ownerCapId: string,
    leverageObligation: LeverageObligation,

    percentage: Decimal,
  ): Promise<TransactionObjectArgument> {
    const info = leverageObligation.leverageObligation.info!;

    await this.refreshLeveragePrice(tx, [info.deposit, info.borrow]);
    console.log("here", [info.deposit, info.borrow]);
    return tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_collateral_coin::withdraw_size`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(ownerCapId),

        tx.pure.u8(percentage.mulBigInt(100n).asNumber()),

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, info.deposit, info.borrow],
    });
  }

  public async openAndApplyOperation(
    tx: Transaction,

    operation: LeverageOperation,

    swapSlippage: number,
    signer: Signer,

    coinObjectId: TransactionObjectInput,
  ) {
    // First, open a leverage obligation if not already opened
    const leverageOwnerCap = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_obligation::open_obligation`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType],
    });

    await this.populateLeverageOperation(
      tx,
      leverageOwnerCap,
      operation,
      swapSlippage,
      signer,
      coinObjectId,
    );

    tx.transferObjects([leverageOwnerCap], getSender(signer));
  }

  public async reduceLeverageSize(
    tx: Transaction,

    leftCoinType: TypeName,
    market: Market,

    leverageOwnerCapId: string,
    leverageObligation: LeverageObligation,

    percentage: Decimal,

    swapSlippage: number,
    signer: Signer,
  ): Promise<void> {
    const collateralType = leverageObligation.leverageObligation.info!.deposit;
    if (leftCoinType === collateralType) {
      return await this.reduceLeverageSizeAsCollateral(tx, market, leverageOwnerCapId, leverageObligation, percentage, swapSlippage, signer);
    }
    return await this.reduceLeverageSizeAsBorrow(tx, market, leverageOwnerCapId, leverageObligation, percentage, swapSlippage, signer);
  }

  public async reduceLeverageSizeAsBorrow(
    tx: Transaction,

    market: Market,

    leverageOwnerCapId: string,
    leverageObligation: LeverageObligation,

    percentage: Decimal,

    swapSlippage: number,
    signer: Signer,
  ): Promise<void> {
    const collateralType = leverageObligation.leverageObligation.info!.deposit;
    const borrowType = leverageObligation.leverageObligation.info!.borrow;

    const totalDebt = leverageObligation.debtAmount();
    if (totalDebt === 0n) {
      const totalDeposit = leverageObligation.collateralAmount();
      const withdraw = percentage.mulBigInt(totalDeposit).floor();
      const refund = await this.populateWithdrawSizeTransaction(
        tx, leverageOwnerCapId, leverageObligation, percentage
      );
      
      const swappedCoins = await this.swapIn(
        tx, 
        collateralType,
        borrowType, 
        withdraw, 
        refund, 
        swapSlippage
      );

      tx.transferObjects([swappedCoins], getSender(signer));

      return;
    }


    let { collateral: withdrawAmount, debt: debtRepay } = leverageObligation.reduceSizeBorrowSwap(percentage, Decimal.fromNumber(swapSlippage), market);

    await this.refreshLeveragePrice(tx, [collateralType, borrowType]);

    // Request repay: returns flash loan and collateral coins
    const [flashLoan, collateralCoins] = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_borrow_coin::request_reduce_size`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(leverageOwnerCapId),
        tx.object(this.leverageConfig.leverageMarketId),

        tx.pure.u8(percentage.mulBigInt(100n).asNumber()),
        tx.pure.u64(debtRepay),

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, collateralType, borrowType]
    });

    // Swap collateral coins to borrow type to repay the flash loan
    const swappedCoins = await this.swapIn(
      tx, 
      collateralType,
      borrowType, 
      withdrawAmount, 
      collateralCoins, 
      swapSlippage
    );

    // Complete the leverage operation
    const refundCoin = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_borrow_coin::complete_reduce`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(this.leverageConfig.leverageMarketId),
        tx.object(leverageOwnerCapId),
        flashLoan,
        swappedCoins,

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, borrowType],
    });

    // Transfer any refunded coins back to the signer
    tx.transferObjects([refundCoin], getSender(signer));
  }

  public async reduceLeverageSizeAsCollateral(
    tx: Transaction,

    market: Market,

    leverageOwnerCapId: string,
    leverageObligation: LeverageObligation,

    percentage: Decimal,

    swapSlippage: number,
    signer: Signer,
  ): Promise<void> {
    const collateralType = leverageObligation.leverageObligation.info!.deposit;
    const borrowType = leverageObligation.leverageObligation.info!.borrow;

    const totalDebt = leverageObligation.debtAmount();
    if (totalDebt === 0n) {
      const refunded = await this.populateWithdrawSizeTransaction(
        tx, leverageOwnerCapId, leverageObligation, percentage
      );
      tx.transferObjects([refunded], getSender(signer));
      return;
    }

    let { debt: debtRepay, debtSwapOut } = leverageObligation.reduceSizeBorrowSwap(percentage, Decimal.fromNumber(swapSlippage), market);
    debtRepay = this.ensureCanFlashLoan(
      debtRepay,
      market.getAssetConfiguration(borrowType)?.borrowSetting().flashLoanFeeRate!,
      parseCoinDecimals(market.coinDecimal(borrowType))
    );

    await this.refreshLeveragePrice(tx, [collateralType, borrowType]);

    // Request repay: returns flash loan and collateral coins
    const [flashLoan, collateralCoins] = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_borrow_coin::request_reduce_size`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(leverageOwnerCapId),
        tx.object(this.leverageConfig.leverageMarketId),

        tx.pure.u8(percentage.mulBigInt(100n).asNumber()),
        tx.pure.u64(debtRepay),

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, collateralType, borrowType]
    });

    this.swapAggregator.signer = getSender(signer);

    // Swap collateral coins to borrow type to repay the flash loan
    const swappedCoins = await this.swapOut(
      tx, 
      collateralType,
      borrowType, 
      debtSwapOut, 
      collateralCoins, 
      swapSlippage
    );

    // Complete the leverage operation
    const refundCoin = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_borrow_coin::complete_reduce`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(this.leverageConfig.leverageMarketId),
        tx.object(leverageOwnerCapId),
        flashLoan,
        swappedCoins,

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, borrowType],
    });

    // Transfer any refunded coins back to the signer
    tx.transferObjects([refundCoin], getSender(signer));

    this.swapAggregator.signer = "";
  }

  public async populateLeverageOperation(
    tx: Transaction,

    obligationOwnerCapId: string | TransactionObjectArgument,

    operation: LeverageOperation,

    swapSlippage: number,
    signer: Signer,

    coinObjectId: TransactionObjectInput,
  ): Promise<void> {
    if (operation.operationType === LeverageOperationType.BorrowSwap) {
      await this.borrowThenSwap(
        tx,
        this.leverageConfig.lendingMarketId,
        this.leverageConfig.lendingMarketType,
        obligationOwnerCapId,
        operation.collateralType,
        operation.debtType,
        coinObjectId,
        operation.collateralAmount.asBigInt(),
        operation.debtAmount.asBigInt(),
        swapSlippage,
        signer
      )
    } else {
      await this.swapThenBorrow(
        tx,
        this.leverageConfig.lendingMarketId,
        this.leverageConfig.lendingMarketType,
        obligationOwnerCapId,
        operation.collateralType,
        operation.debtType,
        coinObjectId,
        operation.debtAmount.asBigInt(),
        swapSlippage,
        signer
      )
    }
  }

  public async increaseLeverage(
    tx: Transaction,

    market: Market, 
    obligationOwnerCapId: string | TransactionObjectArgument,
    obligation: LeverageObligation,

    newLeverage: Decimal,

    swapSlippage: number,
    signer: Signer,
  ): Promise<void> {
    if (obligation.leverage().greaterThan(newLeverage)) {
      throw new Error("invalid leverage");
    }

    const info = obligation.leverageObligation.info!;

    if (info.operation === LeverageOperationType.BorrowSwap) {
      const { collateral, debt } = obligation.increaseLeverageBorrowSwap(newLeverage, Decimal.fromNumber(swapSlippage), market);

      const zeroCoin = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [info.deposit],
        arguments: [],
      });
      await this.borrowThenSwap(
        tx,
        this.leverageConfig.lendingMarketId,
        this.leverageConfig.lendingMarketType,
        obligationOwnerCapId,
        info.deposit,
        info.borrow,
        zeroCoin,
        collateral,
        debt,
        swapSlippage,
        signer
      )
    } else if (obligation.leverageObligation.info?.operation === LeverageOperationType.SwapBorrow) {
      const debt = 0n;
      throw new Error("todo");

      const zeroCoin = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [info.borrow],
        arguments: [],
      });
      await this.swapThenBorrow(
        tx,
        this.leverageConfig.lendingMarketId,
        this.leverageConfig.lendingMarketType,
        obligationOwnerCapId,
        info.deposit,
        info.borrow,
        zeroCoin,
        debt,
        swapSlippage,
        signer
      )
    }
  }

  public async reduceLeverage(
    tx: Transaction,

    market: Market, 

    obligationOwnerCapId: string | TransactionObjectArgument,
    obligation: LeverageObligation,

    newLeverage: Decimal,

    swapSlippage: number,
    signer: Signer,

    debtBuffer: Decimal = Decimal.fromQuotient(99, 100),
  ): Promise<void> {
    const collateralType = obligation.leverageObligation.info!.deposit;
    const principleCoinType = obligation.principleCoinType();

    if (principleCoinType === collateralType) {
      return await this.reduceLeverageAsCollateral(tx, market, obligationOwnerCapId, obligation, newLeverage, swapSlippage, signer);
    }
    return await this.reduceLeverageAsBorrow(tx, market, obligationOwnerCapId, obligation, newLeverage, swapSlippage, signer, debtBuffer);
  }

  // Take a debt buffer, i.e. percentage reduction from actual debt to pay. This is ensure the onchain 
  // fluctuation causing precision calculation that can ensure flash loan fees can be covered after
  // swap.
  public async reduceLeverageAsBorrow(
    tx: Transaction,

    market: Market, 
    obligationOwnerCapId: string | TransactionObjectArgument,
    obligation: LeverageObligation,

    newLeverage: Decimal,

    swapSlippage: number,
    signer: Signer,

    debtBuffer: Decimal = Decimal.fromQuotient(99, 100),
  ): Promise<void> {
    if (obligation.leverage().lessThan(newLeverage)) {
      throw new Error("invalid leverage");
    }

    if (newLeverage.asNumber() < 1.1) {
      throw new Error("leverage too small");
    }

    const info = obligation.leverageObligation.info!;

    let { collateral, debt } = obligation.reduceLeverageToCollateral(newLeverage, Decimal.fromNumber(swapSlippage), market);
    collateral = collateral * 101n / 100n;

    console.log(collateral, debt);

    // Refresh oracle prices for all assets
    await this.refreshLeveragePrice(tx, [info.deposit, info.borrow]);

    // Request reduce leverage: returns flash loan and collateral coins
    const [flashLoan, collateralCoins] = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_borrow_coin::request_reduce_leverage`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(obligationOwnerCapId),
        tx.object(this.leverageConfig.leverageMarketId),
        
        tx.pure.u64(debt),
        tx.pure.u64(collateral),
        
        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, info.deposit, info.borrow]
    });

    const swappedCoins = await this.swapIn(
      tx, 
      info.deposit,
      info.borrow,
      collateral,
      collateralCoins, 
      swapSlippage
    );

    // Complete the leverage operation
    const refundCoin = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_borrow_coin::complete_reduce`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(this.leverageConfig.leverageMarketId),
        tx.object(obligationOwnerCapId),
        flashLoan,
        swappedCoins,
        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, info.borrow],
    });

    // Transfer any refunded coins back to the signer
    tx.transferObjects([refundCoin], getSender(signer));
  }

  public async reduceLeverageAsCollateral(
    tx: Transaction,

    market: Market, 
    obligationOwnerCapId: string | TransactionObjectArgument,
    obligation: LeverageObligation,

    newLeverage: Decimal,

    swapSlippage: number,
    signer: Signer,
  ): Promise<void> {
    if (obligation.leverage().lessThan(newLeverage)) {
      throw new Error("invalid leverage");
    }

    if (newLeverage.asNumber() < 1.1) {
      throw new Error("leverage too small");
    }

    const info = obligation.leverageObligation.info!;

    let { collateral, debt, debtSwapOut } = obligation.reduceLeverageToCollateral(newLeverage, Decimal.fromNumber(swapSlippage), market);
    debt = this.ensureCanFlashLoan(
      debt,
      market.getAssetConfiguration(info.borrow)?.borrowSetting().flashLoanFeeRate!,
      parseCoinDecimals(market.coinDecimal(info.borrow))
    );

    // Refresh oracle prices for all assets
    await this.refreshLeveragePrice(tx, [info.deposit, info.borrow]);

    // Request reduce leverage: returns flash loan and collateral coins
    const [flashLoan, collateralCoins] = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_borrow_coin::request_reduce_leverage`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(obligationOwnerCapId),
        tx.object(this.leverageConfig.leverageMarketId),
        
        tx.pure.u64(debt),
        tx.pure.u64(collateral),
        
        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, info.deposit, info.borrow]
    });

    this.swapAggregator.signer = getSender(signer);

    const swappedCoins = await this.swapOut(
      tx, 
      info.deposit,
      info.borrow,
      debtSwapOut,
      collateralCoins, 
      swapSlippage
    );

    // Complete the leverage operation
    const refundCoin = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_reduce_to_borrow_coin::complete_reduce`,
      arguments: [
        tx.object(this.leverageConfig.lendingMarketId),
        tx.object(this.leverageConfig.leverageMarketId),
        tx.object(obligationOwnerCapId),
        flashLoan,
        swappedCoins,
        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [this.leverageConfig.lendingMarketType, info.borrow],
    });

    // Transfer any refunded coins back to the signer
    tx.transferObjects([refundCoin], getSender(signer));

    this.swapAggregator.signer = "";
  }

  private async borrowThenSwap(
    tx: Transaction,

    lendingMarketId: string,
    lendingMarketType: TypeName,

    obligationOwnerCapId: string | TransactionObjectArgument,

    collateralCoinType: TypeName,
    borrowCoinType: TypeName,

    coinObjectId: TransactionObjectInput,

    totalLeveragedAmount: bigint,
    totalDebtAmount: bigint,

    swapSlippage: number,

    signer: Signer,
  ): Promise<void> {
    await this.refreshLeveragePrice(tx, [collateralCoinType, borrowCoinType]);

    // Request leverage: returns flash loan and borrowed coins
    const [flashLoan, borrowed] = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::borrow_swap::request_leverage`,
      arguments: [
        tx.object(lendingMarketId),
        tx.object(this.leverageConfig.leverageMarketId),
        typeof obligationOwnerCapId === 'string' ? tx.object(obligationOwnerCapId) : obligationOwnerCapId,

        tx.object(coinObjectId),
        tx.pure.u64(totalLeveragedAmount),
        tx.pure.u64(totalDebtAmount),

        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.xOracleId),
      ],
      typeArguments: [lendingMarketType, collateralCoinType, borrowCoinType],
    });

    // Swap borrowed coins to collateral type
    const targetCoin = await this.swapIn(tx, borrowCoinType, collateralCoinType, totalDebtAmount, borrowed, swapSlippage);

    // Complete the leverage operation using leverage_repay::complete_leverage
    const refundCoin = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::leverage_repay::complete_leverage`,
      arguments: [
        tx.object(lendingMarketId),
        tx.object(this.leverageConfig.leverageMarketId),
        tx.object(obligationOwnerCapId),
        flashLoan,
        targetCoin,

        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
      ],
      typeArguments: [lendingMarketType, collateralCoinType],
    });

    // Transfer any refunded coins back to the signer
    tx.transferObjects([refundCoin], getSender(signer));
  }

  private async swapThenBorrow(
    tx: Transaction,

    lendingMarketId: string,
    lendingMarketType: TypeName,

    obligationOwnerCapId: string | TransactionObjectArgument,

    collateralCoinType: TypeName,
    borrowCoinType: TypeName,

    coinObjectId: TransactionObjectInput,

    totalLeveragedDebt: bigint,

    swapSlippage: number,

    signer: Signer,
  ): Promise<void> {
    await this.refreshLeveragePrice(tx, [collateralCoinType, borrowCoinType]);

    // Request leverage for swap_borrow: returns flash loan and total debt coins
    const [hotPotato, debtCoins] = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::swap_borrow::request_leverage`,
      arguments: [
        tx.object(lendingMarketId),
        tx.object(this.leverageConfig.leverageMarketId),
        typeof obligationOwnerCapId === 'string' ? tx.object(obligationOwnerCapId) : obligationOwnerCapId,

        tx.object(coinObjectId), // initial debt coin
        tx.pure.u64(totalLeveragedDebt),

      ],
      typeArguments: [lendingMarketType, collateralCoinType, borrowCoinType],
    });

    // Swap debt coins to collateral type
    const collateralCoins = await this.swapIn(tx, borrowCoinType, collateralCoinType, totalLeveragedDebt, debtCoins, swapSlippage);

    // Complete the leverage operation using swap_borrow::complete_leverage
    const refundCoin = tx.moveCall({
      target: `${this.leverageConfig.leveragePackageId}::swap_borrow::complete_leverage`,
      arguments: [
        tx.object(lendingMarketId),
        tx.object(this.leverageConfig.leverageMarketId),
        typeof obligationOwnerCapId === 'string' ? tx.object(obligationOwnerCapId) : obligationOwnerCapId,

        hotPotato,
        collateralCoins,

        tx.object(this.pebbleClient.config.coinDecimalsRegistryId),
        tx.object(this.pebbleClient.config.xOracleId),
        tx.object('0x6'), // Clock object
      ],
      typeArguments: [lendingMarketType, collateralCoinType, borrowCoinType],
    });

    // Transfer any refunded coins back to the signer
    tx.transferObjects([refundCoin], getSender(signer));
  }

  private async swapIn(
    tx: Transaction,
    inCoinType: TypeName,
    outCoinType: TypeName,
    amountIn: bigint,
    coinId: string | TransactionObjectArgument,
    slippage: number
  ): Promise<TransactionObjectArgument>{
    const router = await this.swapAggregator.findRouters({
      from: inCoinType,
      target: outCoinType,
      amount: amountIn,
      byAmountIn: true, // true means fix input amount, false means fix output amount
    })

    if (router === null) {
      throw new Error("no possible router");
    }

    const targetCoin = await this.swapAggregator.routerSwap({
      router,
      txb: tx,
      inputCoin: typeof coinId === 'string' ? tx.object(coinId) : coinId,
      slippage,
    });

    return targetCoin;
  }

  private ensureCanFlashLoan(amount: bigint, flashLoanFee: Decimal, decimalMulti: Decimal): bigint {
    const min = Decimal.one().divDecimal(decimalMulti);

    const fee = flashLoanFee.mulBigInt(amount).divDecimal(decimalMulti);
    if (fee.lessThan(min)) {
      return Decimal.one().divDecimal(flashLoanFee).asBigInt();
    }
    return amount;
  }

  private async swapOut(
    tx: Transaction,
    inCoinType: TypeName,
    outCoinType: TypeName,
    amountOut: bigint,
    coinId: string | TransactionObjectArgument,
    slippage: number
  ): Promise<TransactionObjectArgument>{
    const router = await this.swapAggregator.findRouters({
      from: inCoinType,
      target: outCoinType,
      amount: amountOut,
      byAmountIn: false, // true means fix input amount, false means fix output amount
    })

    if (router === null) {
      throw new Error("no possible router");
    }

    const targetCoin = await this.swapAggregator.routerSwap({
      router,
      txb: tx,
      inputCoin: typeof coinId === 'string' ? tx.object(coinId) : coinId,
      slippage,
    });

    return targetCoin;
  }

  private async refreshLeveragePrice(
    tx: Transaction,
    operationAssets: TypeName[],
  ): Promise<void> {
    let assets = [...operationAssets];
    await this.pebbleClient.refreshPythOracle(tx, assets);
  }
}

function parseLeverageOperationTypeFromStr(variant: any): LeverageOperationType {
  if (variant === "SwapBorrow") {
    return LeverageOperationType.SwapBorrow;
  }
  
  if (variant === "BorrowSwap") {
    return LeverageOperationType.BorrowSwap;
  }

  throw new Error(`unknown leverage operation type variant ${variant}`);
}