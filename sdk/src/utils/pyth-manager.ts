import {
  SuiPythClient,
  SuiPriceServiceConnection,
} from '@pythnetwork/pyth-sui-js';
import { SuiClient } from '@mysten/sui/client';
import { Transaction } from '@mysten/sui/transactions';

export async function updatePythPrice(
  suiClient: SuiClient,
  stateIds: { pyth: string, wormhole: string },
  connection: SuiPriceServiceConnection,
  priceIDs: string[],
): Promise<[any[], Transaction]> {
  const priceUpdateData = await connection.getPriceFeedsUpdateData(priceIDs);
  
  const pythClient = new SuiPythClient(
    // @ts-ignore
    suiClient,
    stateIds.pyth,
    stateIds.wormhole,
  );
  
  const transaction = new Transaction();
  
  /// By calling the updatePriceFeeds function, the SuiPythClient adds the necessary
  /// transactions to the transaction block to update the price feeds.
  /// This should return transaction result objects, not object IDs
  const priceInfoObjects = await pythClient.updatePriceFeeds(
    // @ts-ignore
    transaction,
    priceUpdateData,
    priceIDs,
  );
  
  if (!priceInfoObjects[0]) {
    throw new Error('priceInfoObjects[0] is undefined');
  }

  return [priceInfoObjects, transaction];
}

export async function updatePythPriceByTxn(
  transaction: Transaction,
  suiClient: SuiClient,
  stateIds: { pyth: string, wormhole: string },
  connection: SuiPriceServiceConnection,
  priceID: string,
): Promise<any> {
  const priceUpdateData = await connection.getPriceFeedsUpdateData([priceID]);
  
  const pythClient = new SuiPythClient(
    // @ts-ignore
    suiClient,
    stateIds.pyth,
    stateIds.wormhole,
  );

  /// By calling the updatePriceFeeds function, the SuiPythClient adds the necessary
  /// transactions to the transaction block to update the price feeds.
  /// This should return transaction result objects, not object IDs
  const priceInfoObjects = await pythClient.updatePriceFeeds(
    // @ts-ignore
    transaction,
    priceUpdateData,
    [priceID],
  );
  
  if (!priceInfoObjects[0]) {
    throw new Error('priceInfoObjects[0] is undefined');
  }

  return priceInfoObjects[0];
}