
// Pyth price feed configuration interface
export interface PythFeedConfig {
  priceFeedId: string;
  tolerance: number;
}

// Pyth network configuration interface
export interface PythConfig {
  pythStateId: string;
  wormholeStateId: string;
  hermesEndpoint: string;
  priceFeeds: Record<string, PythFeedConfig>;
}

// Get Pyth configuration based on network
export function getPythConfig(network: string): PythConfig {  
  if (network === 'testnet') {
    return {
      pythStateId: '0x243759059f4c3111179da5878c12f68d612c21a8d54d85edc86164bb18be1c7c',
      wormholeStateId: '0x31358d198147da50db32eda2562951d53973a0c0ad5ed738e9b17d88b213d790',
      hermesEndpoint: 'https://hermes-beta.pyth.network',
      priceFeeds: {
        // Testnet price feed IDs with tolerance settings
        'ETH': {
          priceFeedId: '0xca80ba6dc32e08d06f1aa886011eed1d77c77be9eb761cc10d72b7d0a2fd57a6',
          tolerance: 100,  // 1% (100/10000)
        },
        'BTC': {
          priceFeedId: '0xf9c0172ba10dfa4d19088d94f5bf61d3b54d5bd7483a322a982e1373ee8ea31b',
          tolerance: 100,  // 1%
        },
        'USDC': {
          priceFeedId: '0x41f3625971ca2ed2263e78573fe5ce23e13d2558ed3f2e47ab0f84fb9e7ae722',
          tolerance: 10,   // 0.1%
        },
        'USDT': {
          priceFeedId: '0x1fc18861232290221461220bd4e2acd1dcdfbc89c84092c93c18bdc7756c1588',
          tolerance: 10,   // 0.1%
        },
      },
    };
  } else if (network === 'mainnet') {
    return {
      pythStateId: "0x1f9310238ee9298fb703c3419030b35b22bb1cc37113e3bb5007c99aec79e5b8",
      wormholeStateId: "0xaeab97f96cf9877fee2883315d459552b2b921edc16d7ceac6eab944dd88919c", 
      hermesEndpoint: "https://hermes.pyth.network",
      priceFeeds: {
        // Mainnet price feed IDs with tolerance settings
        "ETH": {
          priceFeedId: "0xff61491a931112ddf1bd8147cd1b641375f79f5825126d665480874634fd0ace",
          tolerance: 300   // 3%
        },
        "BTC": {
          priceFeedId: "0xe62df6c8b4a85fe1a67db44dc12de5db330f7ac66b72dc658afedf0f4a415b43",
          tolerance: 300   // 3%
        },
        "SUI": {
          priceFeedId: "0x23d7315113f5b1d3ba7a83604c44b94d79f4fd69af77f804fc7f920a6dc65744",
          tolerance: 100   // 1%
        },
        "USDC": {
          priceFeedId: "0xeaa020c61cc479712813461ce153894a96a6c00b21ed0cfc2798d1f9a9e9c94a",
          tolerance: 50   // 0.5%
        },
        // this is actually usdt
        "COIN": {
          priceFeedId: "0x2b89b9dc8fdf9f34709a5b106b472f0f39bb6ca9ce04b0fd7f2e971688e2e53b",
          tolerance: 50   // 0.5%
        },
        "HASUI": {
          priceFeedId: "0x6120ffcf96395c70aa77e72dcb900bf9d40dccab228efca59a17b90ce423d5e8",
          tolerance: 100   // 1%
        },
        "AFSUI": {
          priceFeedId: "0x17cd845b16e874485b2684f8b8d1517d744105dbb904eec30222717f4bc9ee0d",
          tolerance: 100   // 1%
        },
        "STSUI": {
          priceFeedId: "0x0b3eae8cb6e221e7388a435290e0f2211172563f94769077b7f4c4c6a11eea76",
          tolerance: 100   // 1%
        },
      }
    };
  } else {
    throw new Error(`Unsupported network: ${network}`);
  }
}