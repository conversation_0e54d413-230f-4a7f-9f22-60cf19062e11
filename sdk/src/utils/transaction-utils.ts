import { SuiClient } from '@mysten/sui/client';
import { Signer } from '../market-types';
import { Transaction } from '@mysten/sui/dist/cjs/transactions';

const potentialSuiTypes = new Set([
  '0x2::sui::SUI',
  "0000000000000000000000000000000000000000000000000000000000000002::sui::SUI",
  "0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI",
]);

export function getSender(signer: Signer): string {
  return 'getPublicKey' in signer ? signer.getPublicKey().toSuiAddress() : signer.toSuiAddress();
}

/**
 * Wait for transaction confirmation
 */
export async function waitForTransaction(
  client: SuiClient,
  digest: string,
  timeoutMs: number = 30000
): Promise<void> {
  const startTime = Date.now();

  while (Date.now() - startTime < timeoutMs) {
    try {
      await client.waitForTransaction({ digest });
      return;
    } catch (error) {
      // Continue waiting if transaction is not found yet
      if (Date.now() - startTime >= timeoutMs) {
        throw new Error(
          `Transaction confirmation timeout: ${digest}`
        );
      }
      // Wait a bit before retrying
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }
  }
}

/**
 * Parse events from transaction result
 */
export function parseEvents(events: any[]): Record<string, any[]> {
  const parsedEvents: Record<string, any[]> = {};

  for (const event of events) {
    const eventType = event.type;
    if (!parsedEvents[eventType]) {
      parsedEvents[eventType] = [];
    }
    parsedEvents[eventType].push(event.parsedJson || event);
  }

  return parsedEvents;
}

async function getCoinsForAmount(
  client: SuiClient,
  address: string,
  coinType: string,
  amount: bigint
) {
  const coins = await client.getCoins({
    owner: address,
    coinType: coinType
  });

  let totalAmount = 0n;
  const selectedCoins = [];

  for (const coin of coins.data) {
    selectedCoins.push(coin);
    totalAmount += BigInt(coin.balance);

    if (totalAmount >= amount) break;
  }

  return { coins: selectedCoins, totalAmount };
}

export function isSuiCoinType(coinType: string): boolean {
  return potentialSuiTypes.has(coinType)
}

export async function prepareCoinsForAmount(
  txb: Transaction,
  client: SuiClient,
  address: string,
  coinType: string,
  requiredAmount: bigint
) {
  // Special handling for SUI to avoid gas issues
  if (isSuiCoinType(coinType)) {
    // When dealing with SUI, split from the gas object
    // This assumes gas budget was set appropriately in the caller
    // console.log(`Splitting ${requiredAmount} SUI from gas coin`);
    const [exactCoin] = txb.splitCoins(txb.gas, [requiredAmount]);
    return exactCoin;
  }

  // For non-SUI coins, proceed with normal logic
  const { coins, totalAmount } = await getCoinsForAmount(
    client, address, coinType, requiredAmount
  );

  if (totalAmount < requiredAmount) {
    throw new Error('Insufficient balance');
  }

  // If we have exactly one coin with enough balance
  if (coins.length === 1 && BigInt(coins[0].balance) >= requiredAmount) {
    if (BigInt(coins[0].balance) === requiredAmount) {
      return coins[0].coinObjectId;
    } else {
      // Split the coin to get exact amount
      const [exactCoin] = txb.splitCoins(coins[0].coinObjectId, [requiredAmount]);
      return exactCoin;
    }
  }

  // If we need to merge multiple coins
  const primaryCoin = coins[0].coinObjectId;
  const coinsToMerge = coins.slice(1).map(c => c.coinObjectId);

  if (coinsToMerge.length > 0) {
    txb.mergeCoins(primaryCoin, coinsToMerge);
  }

  // Split to get exact amount if needed
  if (totalAmount > requiredAmount) {
    const [exactCoin] = txb.splitCoins(primaryCoin, [requiredAmount]);
    return exactCoin;
  }

  return primaryCoin;
}

export async function estimateGasForTransaction(
  tx: Transaction,
  client: SuiClient,
): Promise<bigint> {
  const dryRun = await client.dryRunTransactionBlock({
    transactionBlock: await tx.build({ client }),
  });

  const gasUsed = BigInt(dryRun.effects.gasUsed.computationCost) +
    BigInt(dryRun.effects.gasUsed.storageCost) -
    BigInt(dryRun.effects.gasUsed.storageRebate || 0);

  // Add 50% buffer for safety
  return (gasUsed * 150n) / 100n;
}