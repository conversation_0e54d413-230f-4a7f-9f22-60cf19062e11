import {AssetData, Decimal} from '../market-types';

export class MarketDisplay {
  token: string;
  totalSupply: number;
  totalBorrow: number;
  liqLTV: number;
  supplyAPY: number;
  borrowAPY: number;
  liqAvailable: number;
  utilization: number;
  borrowCap: number;
  supplyCap: number;
  tokenPrice: string;

  constructor(
    token: string,
    totalSupply: number,
    totalBorrow: number,
    liqLTV: number,
    supplyAPY: number,
    borrowAPY: number,
    liqAvailable: number,
    utilization: number,
    borrowCap: number,
    supplyCap: number,
    tokenPrice: string
  ) {
    this.token = token;
    this.totalSupply = totalSupply;
    this.totalBorrow = totalBorrow;
    this.liqLTV = liqLTV;
    this.supplyAPY = supplyAPY;
    this.borrowAPY = borrowAPY;
    this.liqAvailable = liqAvailable;
    this.utilization = utilization;
    this.borrowCap = borrowCap;
    this.supplyCap = supplyCap;
    this.tokenPrice = tokenPrice;
  }
}

export function createMarketDisplay(
  currentAssetData: AssetData,
  pastExchangeRate?: Decimal,
  pastBorrowIndex?: Decimal,
  pastTimestamp?: number
): MarketDisplay {
  // Basic fields
  // Extract token symbol from coinType (format: package::module::SYMBOL)
  const token = currentAssetData.coinType.split('::').pop() || currentAssetData.coinType;
  const totalSupply = Number(currentAssetData.depositUsage.amount());
  const totalBorrow = Number(currentAssetData.borrowUsage.amount());
  const liqLTV = currentAssetData.collateralSetting?.collateralFactor.asNumber() || 0;
  const liqAvailable = totalSupply - totalBorrow;
  const utilization = currentAssetData.utilizationRate.asNumber();
  const borrowCap = Number(currentAssetData.assetSetting.maxBorrowAmount);
  const supplyCap = Number(currentAssetData.assetSetting.maxDepositAmount);
  const tokenPrice = currentAssetData.depositUsage.price().toString();


  // Calculate APYs if past data is provided
  let supplyAPY = 0;
  let borrowAPY = 0;

  if (pastExchangeRate && pastBorrowIndex && pastTimestamp) {
    // Calculate supply APY using exchange rate
    supplyAPY = currentAssetData.depositUsage.apy(pastExchangeRate, pastTimestamp
    ).asNumber();

    // Calculate borrow APY using borrow index
    borrowAPY = currentAssetData.borrowUsage.apy(pastBorrowIndex, pastTimestamp
    ).asNumber();
  }

  return new MarketDisplay(
    token,
    totalSupply,
    totalBorrow,
    liqLTV,
    supplyAPY,
    borrowAPY,
    liqAvailable,
    utilization,
    borrowCap,
    supplyCap,
    tokenPrice
  );
}