import { SuiClient } from '@mysten/sui/client';
import { TypeName } from '../market-types/assets';
import { Decimal } from '../market-types';

export interface CoinMetadata {
  id: string;
  decimals: number;
  name: string;
  symbol: string;
  description: string;
  iconUrl?: string;
}

/**
 * Get coin metadata for a given coin type
 * @param client - SuiClient instance
 * @param coinType - The type name of the coin (e.g., "0x2::sui::SUI")
 * @returns Promise<CoinMetadata | null> - Coin metadata or null if not found
 */
export async function getCoinMetadata(
  client: SuiClient,
  coinType: TypeName,
): Promise<CoinMetadata | null> {
  if (!coinType.startsWith("0x")) {
    coinType = `0x${coinType}`;
  }

  const metadata = await client.getCoinMetadata({ coinType });

  if (!metadata) {
    return null;
  }

  return {
    id: metadata.id!,
    decimals: metadata.decimals,
    name: metadata.name || '',
    symbol: metadata.symbol || '',
    description: metadata.description || '',
    iconUrl: metadata.iconUrl || undefined,
  };
}

/**
 * Get multiple coin metadata in parallel
 * @param client - SuiClient instance
 * @param coinTypes - Array of coin type names
 * @returns Promise<Map<TypeName, CoinMetadata>> - Map of coin type to metadata
 */
export async function getMultipleCoinMetadata(
  client: SuiClient,
  coinTypes: TypeName[],
): Promise<Map<TypeName, CoinMetadata>> {
  const results = new Map<TypeName, CoinMetadata>();
  
  try {
    const promises = coinTypes.map(async (coinType) => {
      const metadata = await getCoinMetadata(client, coinType);
      return { coinType, metadata };
    });

    const responses = await Promise.allSettled(promises);
    
    responses.forEach((response, index) => {
      if (response.status === 'fulfilled' && response.value.metadata) {
        results.set(response.value.coinType, response.value.metadata);
      } else if (response.status === 'rejected') {
        console.error(`Failed to fetch metadata for ${coinTypes[index]}:`, response.reason);
      }
    });
  } catch (error) {
    console.error('Failed to fetch multiple coin metadata:', error);
  }

  return results;
}

/**
 * Format coin amount with proper decimals and symbol
 * @param amount - Raw coin amount (bigint)
 * @param metadata - Coin metadata containing decimals and symbol
 * @returns Formatted string (e.g., "1.5 SUI")
 */
export function formatCoinAmount(amount: bigint, metadata: CoinMetadata): string {
  const divisor = BigInt(10 ** metadata.decimals);
  const wholePart = amount / divisor;
  const fractionalPart = amount % divisor;
  
  if (fractionalPart === 0n) {
    return `${wholePart} ${metadata.symbol}`;
  }
  
  const fractionalStr = fractionalPart.toString().padStart(metadata.decimals, '0');
  const trimmedFractional = fractionalStr.replace(/0+$/, '');
  
  if (trimmedFractional === '') {
    return `${wholePart} ${metadata.symbol}`;
  }
  
  return `${wholePart}.${trimmedFractional} ${metadata.symbol}`;
}

/**
 * Parse formatted coin amount back to raw bigint
 * @param formattedAmount - Formatted amount string (e.g., "1.5")
 * @param decimals - Number of decimals for the coin
 * @returns Raw coin amount as bigint
 */
export function parseCoinAmount(formattedAmount: string, decimals: number): bigint {
  const parts = formattedAmount.split('.');
  const wholePart = BigInt(parts[0] || '0');
  
  if (parts.length === 1) {
    return wholePart * BigInt(10 ** decimals);
  }
  
  const fractionalPart = parts[1].padEnd(decimals, '0').slice(0, decimals);
  const fractionalBigInt = BigInt(fractionalPart);
  
  return wholePart * BigInt(10 ** decimals) + fractionalBigInt;
}

export function parseCoinDecimals(decimals: number): Decimal {
  const numstr = "1" + "0".repeat(decimals);
  return Decimal.fromString(numstr);
}