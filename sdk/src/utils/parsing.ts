import { Decimal } from '../market-types/decimal';
import { AssetBorrow, AssetDeposit, AssetValuation } from '../market-types/assets';
import { AssetData, BorrowConfig, CollateralSetting, InterestModel } from '../market-types/market';

export function parseDecimal(value: string | number | bigint): Decimal {
  // Sui Move decimals are typically represented as fixed-point integers
  if (typeof value === 'bigint') {
    return new Decimal(value);
  } else if (typeof value === 'string') {
    return Decimal.fromString(value);
  } else {
    return Decimal.fromNumber(value);
  }
}

export function parseAssetValuation(data: any): AssetValuation {
  return {
    coinType: data.coin_type || data.coinType,
    amount: BigInt(data.amount),
    usd: parseDecimal(data.usd),
    price: parseDecimal(data.price),
  };
}

export function parseAssetBorrow(data: any): AssetBorrow {
  const valuation = parseAssetValuation(data.valuation);
  const borrowIndex = parseDecimal(data.borrow_index || data.borrowIndex);
  
  return new AssetBorrow(valuation, borrowIndex);
}

export function parseAssetDeposit(data: any): AssetDeposit {
  const isCollateral = data.is_collateral || data.isCollateral;
  const valuation = parseAssetValuation(data.valuation);
  const ctokenAmount = BigInt(data.ctoken_amount || data.ctokenAmount || 0);
  const exchangeRate = parseDecimal(data.exchange_rate || data.exchangeRate);
  
  return new AssetDeposit(isCollateral, valuation, ctokenAmount, exchangeRate);
}

export function parseInterestModel(data: any): InterestModel {
  return {
    type: data.type || 'unknown',
    params: data.params || {},
  };
}

export function parseBorrowConfig(data: any): BorrowConfig {
  return {
    minBorrowAmount: BigInt(data.min_borrow_amount || data.minBorrowAmount || 0),
    maxBorrowAmount: BigInt(data.max_borrow_amount || data.maxBorrowAmount || 0),
    maxDepositAmount: BigInt(data.max_deposit_amount || data.maxDepositAmount || 0),
    flashLoanFeeRate: parseDecimal(data.flash_loan_fee_rate || data.flashLoanFeeRate),
    repayFeeRate: parseDecimal(data.repay_fee_rate || data.repayFeeRate),
  };
}

export function parseCollateralSetting(data: any): CollateralSetting {
  return {
    collateralFactor: parseDecimal(data.collateral_factor || data.collateralFactor),
    liquidationIncentive: parseDecimal(data.liquidation_incentive || data.liquidationIncentive),
    liquidationRevenueFactor: parseDecimal(data.liquidation_revenue_factor || data.liquidationRevenueFactor),
  };
}
