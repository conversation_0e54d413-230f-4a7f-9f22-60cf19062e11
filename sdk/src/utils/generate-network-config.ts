import * as fs from 'fs';
import * as path from 'path';
import { NetworkConfig, Market, LeverageMarket } from '../config/networks';

interface DeploymentCache {
  network: string;
  packages: {
    coin_decimals_registry?: {
      packageId: string;
      digest: string;
      timestamp: string;
    };
    x_oracle?: {
      packageId: string;
      digest: string;
      timestamp: string;
    };
    protocol?: {
      packageId: string;
      digest: string;
      timestamp: string;
    };
    protocol_query?: {
      packageId: string;
      digest: string;
      timestamp: string;
    };
    market_type?: {
      packageId: string;
      digest: string;
      timestamp: string;
    };
    leverage?: {
      packageId: string;
      digest: string;
      timestamp: string;
    };
  };
  objects: {
    coin_decimals_registry_id?: string;
    x_oracle_id?: string;
    protocol_app_id?: string;
    markets?: {
      [marketName: string]: string;
    };
    leverage?: {
      [marketName: string]: string;
    };
  };
  lastUpdated: string;
}

/**
 * Generates a NetworkConfig object from a deployment cache JSON file
 * @param network - The network name (e.g., 'testnet', 'mainnet')
 * @param deploymentCachePath - Optional path to the deployment cache file. If not provided, will look for it in the default location
 * @returns NetworkConfig object that can be used in NETWORK_CONFIGS
 */
export function generateNetworkConfigFromCache(
  cachePath: string,
): NetworkConfig {
  // Check if file exists
  if (!fs.existsSync(cachePath)) {
    throw new Error(`Deployment cache file not found at: ${cachePath}`);
  }
  
  // Read and parse the deployment cache
  const cacheContent = fs.readFileSync(cachePath, 'utf-8');
  const deploymentCache: DeploymentCache = JSON.parse(cacheContent);
  
  // Validate required fields
  if (!deploymentCache.packages?.protocol?.packageId) {
    throw new Error('Missing protocol package ID in deployment cache');
  }
  if (!deploymentCache.packages?.protocol_query?.packageId) {
    throw new Error('Missing protocol_query package ID in deployment cache');
  }
  if (!deploymentCache.packages?.x_oracle?.packageId) {
    throw new Error('Missing x_oracle package ID in deployment cache');
  }
  if (!deploymentCache.objects?.coin_decimals_registry_id) {
    throw new Error('Missing coin_decimals_registry_id in deployment cache');
  }
  if (!deploymentCache.objects?.x_oracle_id) {
    throw new Error('Missing x_oracle_id in deployment cache');
  }
  
  // Build markets array
  const markets: Market[] = [];
  if (deploymentCache.objects.markets) {
    const marketTypePackageId = deploymentCache.packages.market_type?.packageId;
    if (!marketTypePackageId) {
      throw new Error('Missing market_type package ID but markets are defined');
    }
    
    for (const [marketName, marketObjectId] of Object.entries(deploymentCache.objects.markets)) {
      markets.push({
        name: marketName,
        type: `${marketTypePackageId}::market_type::${marketName}`,
        objectId: marketObjectId,
      });
    }
  }
  
  // Build leverageMarkets array
  const leverageMarkets: LeverageMarket[] = [];
  const leveragePackageId = deploymentCache.packages.leverage?.packageId;
  const marketTypePackageId = deploymentCache.packages.market_type?.packageId;
  if (!leveragePackageId) {
    throw new Error('Missing leverage package ID but leverage markets are defined');
  }
  if (!marketTypePackageId) {
    throw new Error('Missing market_type package ID but leverage markets are defined');
  }
  
  for (const [marketName, marketObjectId] of Object.entries(deploymentCache.objects.leverage!)) {
    // Find the corresponding lending market to get its type and ID
    const lendingMarket = markets.find(m => m.name === marketName);
    if (!lendingMarket) {
      throw new Error(`Leverage market '${marketName}' does not have a corresponding lending market`);
    }
    
    leverageMarkets.push({
      lendingMarketType: lendingMarket.type,
      lendingMarketId: lendingMarket.objectId,
      lendingMarketName: marketName,
      objectId: marketObjectId,
    });
  }
  
  // Construct the NetworkConfig
  const networkConfig: NetworkConfig = {
    protocolQueryPackageId: deploymentCache.packages.protocol_query.packageId,
    protocolPackageId: deploymentCache.packages.protocol.packageId,
    xOraclePackageId: deploymentCache.packages.x_oracle.packageId,
    coinDecimalsRegistryId: deploymentCache.objects.coin_decimals_registry_id,
    xOracleId: deploymentCache.objects.x_oracle_id,
    markets,
    leverageMarkets: leverageMarkets,
    leveragePackageId,
  };
  
  // Add optional protocolAppId if present
  if (deploymentCache.objects.protocol_app_id) {
    networkConfig.protocolAppId = deploymentCache.objects.protocol_app_id;
  }
  
  return networkConfig;
}

/**
 * CLI utility to generate network config from deployment cache
 * Usage: npx ts-node generate-network-config.ts <network>
 */
if (import.meta.url.startsWith('file:')) {
  const cacheFilePath = process.argv[2];
  
  if (!cacheFilePath) {
    console.error('Usage: npx ts-node generate-network-config.ts <cacheFilePath>');
    process.exit(1);
  }
  
  const config = generateNetworkConfigFromCache(cacheFilePath);
  console.log(`\n// Network configuration for ${cacheFilePath}:`);
  console.log(JSON.stringify(config, null, 2));
}