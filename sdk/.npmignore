# Source files
src/
examples/
*.ts
!dist/**/*.d.ts

# Exclude examples from dist
dist/examples/

# Development files
tsconfig.json
jest.config.js
eslint.config.js
.eslintrc*

# Test files
**/*.test.ts
**/*.spec.ts
__tests__/

# Development dependencies
node_modules/
.env
.env.local
.env.*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git files
.git/
.gitignore

# CI/CD
.github/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Build artifacts (keep dist/)
# dist/ is NOT ignored - we want to include it