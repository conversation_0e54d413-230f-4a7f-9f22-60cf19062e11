# dependencies
node_modules/
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

#ide
.idea

# production
/**/build
/**/dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# logs
*.log*

# generate files
object-ids.json
object-ids.*.json

*.coverage_map.mvcov
*.trace

pebble-sdk/examples/.env