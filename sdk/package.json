{"name": "@pebble-protocol/pebble-sdk", "version": "1.0.1", "description": "TypeScript SDK for Pebble Protocol", "main": "dist/src/index.js", "module": "dist/src/index.js", "types": "dist/src/index.d.ts", "exports": {".": {"import": "./dist/src/index.js", "require": "./dist/src/index.js", "types": "./dist/src/index.d.ts"}}, "type": "module", "files": ["dist/src/**/*", "README.md"], "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "generate-network-config": "tsx src/utils/generate-network-config.ts"}, "keywords": ["sui", "defi", "lending", "pebble"], "author": "", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/pebble-protocol/pebble-ui.git", "directory": "sdk"}, "dependencies": {"@cetusprotocol/aggregator-sdk": "^1.1.4", "@mysten/sui": "^1.36.1", "@pythnetwork/pyth-sui-js": "^2.2.0", "dotenv": "^16.0.3"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^18.16.3", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "jest": "^30.0.5", "ts-jest": "^29.4.0", "ts-node": "^10.9.1", "tsx": "^4.20.4", "typescript": "^5.0.0"}}