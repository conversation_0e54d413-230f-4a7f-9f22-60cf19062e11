{"name": "pebble-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "pnpm lint && tsc -b && vite build", "lint": "eslint . && tsc --noEmit", "lint:fix": "eslint . --fix", "format": "prettier --write .", "preview": "vite preview", "preinstall": "npx only-allow pnpm", "prepare": "simple-git-hooks"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{ts,tsx}": ["pnpm lint:fix", "pnpm format"], "*.{js,jsx,json,css,md}": ["pnpm format"]}, "dependencies": {"@cetusprotocol/aggregator-sdk": "^1.1.4", "@mysten/dapp-kit": "^0.16.15", "@mysten/sui": "^1.36.1", "@pebble-protocol/pebble-sdk": "workspace:*", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.81.5", "@tanstack/react-table": "^8.21.3", "axios": "^1.11.0", "bignumber.js": "^9.3.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "fancy-canvas": "^2.1.0", "framer-motion": "^12.23.6", "lightweight-charts": "^5.0.8", "lodash": "^4.17.21", "lucide-react": "^0.525.0", "nanoid": "^5.1.5", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-imask": "^7.6.1", "react-router": "^7.6.3", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "usehooks-ts": "^3.1.1", "utils": "link:@mysten/sui/utils", "vaul": "^1.1.2", "vite-plugin-svgr": "^4.3.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/lodash": "^4.17.20", "@types/node": "^24.0.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "lint-staged": "^16.1.2", "prettier": "3.6.2", "simple-git-hooks": "^2.13.0", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.3", "vite-plugin-node-polyfills": "^0.24.0"}}