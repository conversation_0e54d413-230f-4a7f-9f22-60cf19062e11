# Phantom Wallet Support Setup

## Overview

This document describes the setup and testing of Phantom wallet support in the Pebble frontend.

## Changes Made

### 1. Wallet Configuration (`src/config/wallet.tsx`)

- Added `Phantom` to `WalletEnum`
- Added Phantom wallet name, web URL, and explorer URL
- Added Phantom wallet icon

### 2. Wallet Hook (`src/hooks/use-pebble-wallet.ts`)

- Added Phantom wallet detection in `usePebbleWallet` hook
- Phantom wallet will be automatically detected if installed

### 3. Wallet Icon (`src/assets/icons/phantom.svg`)

- Created a custom Phantom wallet icon
- Added icon export in `src/assets/icons/index.tsx`

## How It Works

The Phantom wallet support works through the `@mysten/wallet-standard` which is included in `@mysten/dapp-kit`. When a user has the Phantom wallet extension installed in their browser, it will be automatically detected and available in the wallet connection dialog.

## Testing

### Prerequisites

1. Install Phantom wallet browser extension
2. Add Sui network to Phantom wallet
3. Have some test SUI tokens

### Steps to Test

1. Start the development server: `npm run dev`
2. Open the application in your browser
3. Click "Connect Wallet" button
4. Phantom wallet should appear in the list of available wallets
5. Click on Phantom wallet to connect
6. Approve the connection in Phantom wallet
7. Verify that the wallet is connected and shows the correct address

### Phantom Wallet Setup for Sui

1. Open Phantom wallet extension
2. Go to Settings > Networks
3. Add custom network:
   - Network Name: Sui Mainnet
   - RPC URL: https://sui-mainnet.blockvision.org
   - Chain ID: 0x1
   - Symbol: SUI
   - Explorer URL: https://suiscan.xyz/mainnet

## Troubleshooting

### Phantom Wallet Not Appearing

- Ensure Phantom wallet extension is installed
- Check that Phantom wallet supports Sui network
- Verify that the wallet extension is enabled

### Connection Issues

- Check browser console for errors
- Ensure Phantom wallet is unlocked
- Try refreshing the page

## Notes

- Phantom wallet support is automatic through `@mysten/wallet-standard`
- No additional dependencies are required
- The wallet will only appear if the Phantom extension is installed
- Users can still connect other supported wallets (OKX, Slush) if Phantom is not available
