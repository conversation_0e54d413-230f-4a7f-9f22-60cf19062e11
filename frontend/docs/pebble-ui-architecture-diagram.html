<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pebble UI 架构流程图</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        h1, h2 {
            color: #333;
            text-align: center;
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 40px;
            font-size: 1.2em;
        }
        .diagram-section {
            margin: 40px 0;
            padding: 20px;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            background: #fafafa;
        }
        .mermaid {
            text-align: center;
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .tech-item {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .tech-item h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.3em;
        }
        .tech-item ul {
            margin: 0;
            padding-left: 20px;
        }
        .tech-item li {
            margin: 5px 0;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏗️ Pebble UI 项目架构</h1>
        <p class="subtitle">基于 Sui 区块链的 DeFi 借贷协议前端应用</p>

        <div class="diagram-section">
            <h2>🎯 整体系统架构</h2>
            <div class="mermaid">
graph TB
    subgraph "前端应用 Frontend App"
        A[用户界面 UI Layer]
        B[状态管理 State Management]
        C[业务逻辑 Business Logic]
        D[数据访问 Data Access]
    end
    
    subgraph "核心SDK Core SDK"
        E[Pebble SDK]
        F[Sui SDK]
    end
    
    subgraph "区块链基础设施 Blockchain Infrastructure"
        G[Sui Network]
        H[Pebble Protocol]
        I[Pyth Oracle]
        J[Cetus DEX]
    end
    
    subgraph "外部服务 External Services"
        K[后端API Backend API]
        L[钱包 Wallet]
    end
    
    A --> B
    B --> C
    C --> D
    D --> E
    D --> F
    D --> K
    E --> G
    E --> H
    E --> I
    F --> G
    C --> L
    H --> I
    H --> J
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#ffebee
    style F fill:#f1f8e9
    style G fill:#e3f2fd
    style H fill:#fce4ec
    style I fill:#f9fbe7
    style J fill:#fff8e1
    style K fill:#e0f2f1
    style L fill:#fef7ff
            </div>
        </div>

        <div class="diagram-section">
            <h2>🔄 数据流架构</h2>
            <div class="mermaid">
flowchart LR
    subgraph "用户交互层 User Interaction"
        A1[React组件]
        A2[用户操作]
    end
    
    subgraph "状态管理层 State Management"
        B1[Zustand Store]
        B2[React Query Cache]
        B3[React Context]
    end
    
    subgraph "业务逻辑层 Business Logic"
        C1[Custom Hooks]
        C2[Services]
        C3[Queries]
    end
    
    subgraph "数据源 Data Sources"
        D1[Pebble SDK<br/>区块链数据]
        D2[Backend API<br/>聚合数据]
        D3[Pyth Oracle<br/>价格数据]
        D4[Wallet<br/>用户资产]
    end
    
    A2 --> A1
    A1 --> B1
    A1 --> B2
    B1 --> C1
    B2 --> C3
    C1 --> C2
    C2 --> D1
    C2 --> D2
    C3 --> D1
    C3 --> D3
    C1 --> D4
    
    D1 --> C2
    D2 --> C3
    D3 --> C2
    D4 --> C1
    
    style A1 fill:#e1f5fe
    style B1 fill:#f3e5f5
    style B2 fill:#f3e5f5
    style C1 fill:#e8f5e8
    style C2 fill:#e8f5e8
    style C3 fill:#e8f5e8
    style D1 fill:#ffebee
    style D2 fill:#fff3e0
    style D3 fill:#f9fbe7
    style D4 fill:#fef7ff
            </div>
        </div>

        <div class="diagram-section">
            <h2>🧩 核心功能模块</h2>
            <div class="mermaid">
graph TD
    subgraph "Pebble UI Application"
        A[应用入口 App Entry]
        
        subgraph "核心页面 Core Pages"
            B[Market 市场]
            C[Margin Trade 保证金交易]
            D[Multiply 杠杆]
            E[Portfolio 投资组合]
            F[LP Pools 流动性池]
        end
        
        subgraph "共享组件 Shared Components"
            G[UI组件库 UI Components]
            H[图表组件 Chart Components]
            I[对话框组件 Dialog Components]
            J[数据表格 Data Tables]
        end
        
        subgraph "业务Hooks Business Hooks"
            K[市场数据 Market Data]
            L[用户资产 User Assets]
            M[交易操作 Trading Operations]
            N[余额管理 Balance Management]
        end
        
        subgraph "核心服务 Core Services"
            O[Market Service]
            P[User Service]
            Q[Chart Service]
            R[Margin Trade Service]
        end
    end
    
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    
    B --> G
    C --> H
    D --> I
    E --> J
    
    B --> K
    C --> L
    D --> M
    E --> N
    
    K --> O
    L --> P
    M --> Q
    N --> R
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#ffebee
    style F fill:#f1f8e9
    style G fill:#e3f2fd
    style H fill:#fce4ec
    style I fill:#f9fbe7
    style J fill:#fff8e1
    style K fill:#e0f2f1
    style L fill:#fef7ff
    style M fill:#f3e5f5
    style N fill:#e8f5e8
    style O fill:#fff3e0
    style P fill:#ffebee
    style Q fill:#f1f8e9
    style R fill:#e3f2fd
            </div>
        </div>

        <div class="diagram-section">
            <h2>⚡ 用户操作流程</h2>
            <div class="mermaid">
sequenceDiagram
    participant U as 用户 User
    participant W as 钱包 Wallet
    participant UI as 前端 Frontend
    participant SDK as Pebble SDK
    participant SUI as Sui Network
    participant PYTH as Pyth Oracle
    
    U->>W: 连接钱包
    W->>UI: 返回钱包地址
    UI->>SDK: 初始化SDK客户端
    
    U->>UI: 查看市场数据
    UI->>SDK: 获取市场信息
    SDK->>SUI: 查询市场合约
    SDK->>PYTH: 获取价格数据
    PYTH-->>SDK: 返回价格信息
    SUI-->>SDK: 返回市场数据
    SDK-->>UI: 返回处理后数据
    UI-->>U: 展示市场信息
    
    U->>UI: 进行交易操作
    UI->>SDK: 构建交易
    SDK->>PYTH: 刷新预言机价格
    SDK->>SUI: 准备交易数据
    SDK-->>UI: 返回待签名交易
    UI->>W: 请求签名交易
    W->>U: 弹出确认界面
    U->>W: 确认签名
    W->>SUI: 提交已签名交易
    SUI-->>W: 返回交易结果
    W-->>UI: 返回交易状态
    UI-->>U: 显示交易完成
            </div>
        </div>

        <div class="tech-stack">
            <div class="tech-item">
                <h3>🎨 前端技术栈</h3>
                <ul>
                    <li>React 19 - 前端框架</li>
                    <li>TypeScript - 类型安全</li>
                    <li>Vite - 构建工具</li>
                    <li>Tailwind CSS 4 - 样式框架</li>
                    <li>React Router 7 - 路由管理</li>
                </ul>
            </div>
            
            <div class="tech-item">
                <h3>🔄 状态管理</h3>
                <ul>
                    <li>Zustand - 全局状态管理</li>
                    <li>TanStack Query - 服务器状态</li>
                    <li>React Context - 钱包状态</li>
                    <li>Local State - 组件状态</li>
                </ul>
            </div>
            
            <div class="tech-item">
                <h3>🧩 UI组件</h3>
                <ul>
                    <li>Radix UI - 无头UI组件</li>
                    <li>Lucide React - 图标库</li>
                    <li>Framer Motion - 动画效果</li>
                    <li>Lightweight Charts - 图表库</li>
                    <li>Sonner - 通知组件</li>
                </ul>
            </div>
            
            <div class="tech-item">
                <h3>⛓️ 区块链集成</h3>
                <ul>
                    <li>Sui SDK - Sui区块链客户端</li>
                    <li>Pebble SDK - 协议专用SDK</li>
                    <li>Pyth Network - 价格预言机</li>
                    <li>Cetus Protocol - DEX集成</li>
                    <li>钱包适配器 - 多钱包支持</li>
                </ul>
            </div>
            
            <div class="tech-item">
                <h3>🛠️ 开发工具</h3>
                <ul>
                    <li>PNPM Workspace - 包管理</li>
                    <li>ESLint - 代码检查</li>
                    <li>Prettier - 代码格式化</li>
                    <li>Jest - 单元测试</li>
                    <li>TypeScript - 类型检查</li>
                </ul>
            </div>
            
            <div class="tech-item">
                <h3>📊 核心功能</h3>
                <ul>
                    <li>Market - 借贷市场</li>
                    <li>Margin Trade - 保证金交易</li>
                    <li>Multiply - 杠杆交易</li>
                    <li>Portfolio - 投资组合</li>
                    <li>LP Pools - 流动性挖矿</li>
                </ul>
            </div>
        </div>

        <div class="diagram-section">
            <h2>🏗️ 项目结构架构</h2>
            <div class="mermaid">
graph TD
    subgraph "Monorepo 结构"
        A[pebble-ui/]
        
        subgraph "前端应用 Frontend"
            B[frontend/]
            B1[src/components/] 
            B2[src/pages/]
            B3[src/hooks/]
            B4[src/services/]
            B5[src/store/]
            B6[src/config/]
        end
        
        subgraph "核心SDK SDK"
            C[sdk/]
            C1[src/core/]
            C2[src/market-types/]
            C3[src/leverage-types/]
            C4[src/utils/]
            C5[src/config/]
        end
        
        subgraph "示例代码 Examples"
            D[examples/]
            D1[lending/]
            D2[leverage/]
        end
    end
    
    A --> B
    A --> C
    A --> D
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
    B --> B5
    B --> B6
    
    C --> C1
    C --> C2
    C --> C3
    C --> C4
    C --> C5
    
    D --> D1
    D --> D2
    
    B3 --> C1
    B4 --> C1
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style B1 fill:#ffebee
    style B2 fill:#f1f8e9
    style B3 fill:#e3f2fd
    style B4 fill:#fce4ec
    style B5 fill:#f9fbe7
    style B6 fill:#fff8e1
    style C1 fill:#e0f2f1
    style C2 fill:#fef7ff
    style C3 fill:#f3e5f5
    style C4 fill:#e8f5e8
    style C5 fill:#fff3e0
    style D1 fill:#ffebee
    style D2 fill:#f1f8e9
            </div>
        </div>

        <div class="diagram-section">
            <h2>🔗 SDK架构详解</h2>
            <div class="mermaid">
classDiagram
    class PebbleClient {
        +provider: SuiClient
        +config: NetworkConfig
        +getMarketDetail()
        +borrow()
        +deposit()
        +withdraw()
        +repay()
        +enterMarket()
        +executeTransaction()
    }
    
    class LeverageClient {
        +client: PebbleClient
        +getPosition()
        +openPosition()
        +closePosition()
        +adjustLeverage()
    }
    
    class Market {
        +assets: AssetData[]
        +getAsset()
        +calculateBorrowRate()
        +calculateSupplyRate()
    }
    
    class AssetData {
        +coinType: string
        +interestModel: InterestModel
        +utilizationRate: Decimal
        +depositUsage: AssetDeposit
        +borrowUsage: AssetBorrow
    }
    
    class Obligation {
        +deposits: AssetDeposit[]
        +borrows: AssetBorrow[]
        +getHealthFactor()
        +getLiquidationThreshold()
    }
    
    PebbleClient --> Market
    PebbleClient --> AssetData
    PebbleClient --> Obligation
    LeverageClient --> PebbleClient
    Market --> AssetData
    Obligation --> AssetData
            </div>
        </div>

        <div class="diagram-section">
            <h2>🚀 用户交易完整流程</h2>
            <div class="mermaid">
stateDiagram-v2
    [*] --> 钱包连接
    钱包连接 --> 用户认证
    用户认证 --> 主界面
    
    主界面 --> 市场浏览
    主界面 --> 投资组合查看
    主界面 --> 交易操作
    
    state 交易操作 {
        [*] --> 选择交易类型
        选择交易类型 --> 借贷操作
        选择交易类型 --> 保证金交易
        选择交易类型 --> 杠杆交易
        选择交易类型 --> 流动性提供
        
        借贷操作 --> 输入金额
        保证金交易 --> 设置参数
        杠杆交易 --> 选择倍数
        流动性提供 --> 添加流动性
        
        输入金额 --> 预览交易
        设置参数 --> 预览交易
        选择倍数 --> 预览交易
        添加流动性 --> 预览交易
        
        预览交易 --> 确认签名
        确认签名 --> 提交交易
        提交交易 --> 等待确认
        等待确认 --> 交易完成
        交易完成 --> [*]
    }
    
    市场浏览 --> 主界面
    投资组合查看 --> 主界面
    交易操作 --> 主界面
            </div>
        </div>

        <div class="diagram-section">
            <h2>📱 组件层次架构</h2>
            <div class="mermaid">
graph TD
    subgraph "Layout 布局层"
        A[DefaultLayout]
        A1[Navbar]
        A2[DeviceWarning]
        A3[Toaster]
        A4[Drawers]
    end
    
    subgraph "Page 页面层"
        B[Market Pages]
        C[Margin Trade Pages]
        D[Leverage Pages]
        E[Portfolio Pages]
        F[LP Pool Pages]
    end
    
    subgraph "Component 组件层"
        G[业务组件 Business Components]
        H[UI组件 UI Components]
        I[图表组件 Chart Components]
        J[表单组件 Form Components]
    end
    
    subgraph "Hook 逻辑层"
        K[数据获取 Data Hooks]
        L[业务逻辑 Business Hooks]
        M[UI状态 UI State Hooks]
    end
    
    A --> A1
    A --> A2
    A --> A3
    A --> A4
    A --> B
    A --> C
    A --> D
    A --> E
    A --> F
    
    B --> G
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> H
    G --> I
    G --> J
    
    H --> K
    I --> L
    J --> M
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style C fill:#e8f5e8
    style D fill:#fff3e0
    style E fill:#ffebee
    style F fill:#f1f8e9
    style G fill:#e3f2fd
    style H fill:#fce4ec
    style I fill:#f9fbe7
    style J fill:#fff8e1
    style K fill:#e0f2f1
    style L fill:#fef7ff
    style M fill:#f3e5f5
            </div>
        </div>

        <script>
            mermaid.initialize({ 
                startOnLoad: true,
                theme: 'base',
                themeVariables: {
                    primaryColor: '#667eea',
                    primaryTextColor: '#333',
                    primaryBorderColor: '#764ba2',
                    lineColor: '#666',
                    secondaryColor: '#f8f9fa',
                    tertiaryColor: '#e9ecef'
                },
                flowchart: {
                    useMaxWidth: true,
                    htmlLabels: true
                },
                sequence: {
                    useMaxWidth: true,
                    wrap: true
                }
            });
        </script>
    </div>
</body>
</html>