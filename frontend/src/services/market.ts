import axiosInstance from '@/config/axios'
import type { IPagination, IResponse } from '@/types/data'
import type { IMarketInfo } from '@/types/market'

interface IGetMarketListParams {
  marketType?: string
  borrowAPYSort?: 'asc' | 'desc' | null
  liqLTVSort?: 'asc' | 'desc' | null
  supplyAPTSort?: 'asc' | 'desc' | null
  totalBorrowSort?: 'asc' | 'desc' | null
  totalSupplySort?: 'asc' | 'desc' | null
  size?: number
  page?: number
}

interface IGetMarketInfoParams {
  marketName: string
  assetToken: string
}

const MarketService = {
  getMarketList: {
    key: 'market.getMarketList',
    call: (params: IGetMarketListParams) =>
      axiosInstance
        .get<
          IResponse<IPagination<IMarketInfo>>
        >('/market/getMarketList', { params: params })
        .then((resp) => resp.data)
  },
  getMarketInfo: {
    key: 'market.getMarketInfo',
    call: (params: IGetMarketInfoParams) =>
      axiosInstance
        .get<
          IResponse<IMarketInfo>
        >('/market/getMarketInfo', { params: params })
        .then((resp) => resp.data)
  }
}

export default MarketService
