import axiosInstance from '@/config/axios'
import type { IResponse } from '@/types/data'
import type { IMarginTradeItem } from '@/types/margin-trade'

const MarginTradeService = {
  getMarginTradeList: {
    key: 'marginTrade.getMarginTradeList',
    call: () =>
      axiosInstance
        .get<IResponse<IMarginTradeItem[]>>('/marginTrade/getMarginTradeList')
        .then((resp) => resp.data)
  }
}

export default MarginTradeService
