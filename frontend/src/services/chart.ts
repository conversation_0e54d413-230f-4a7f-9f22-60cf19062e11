import axiosInstance from '@/config/axios'
import type { IResponse } from '@/types/data'
import type {
  IBorrowAPYHistoryChartItem,
  IBorrowHistoryChartItem,
  ISupplyAPYHistoryChartItem,
  ISupplyHistoryChartItem,
  IUtilizationHistoryChartItem,
  ITokenPriceHistoryChartItem,
  IChartParams
} from '@/types/chart'

const ChartService = {
  getBorrowAPYHistoryChart: {
    key: 'chart.getBorrowAPYHistoryChart',
    call: (params: IChartParams) =>
      axiosInstance
        .get<IResponse<IBorrowAPYHistoryChartItem[]>>(
          '/chart/getBorrowAPYHistoryChart',
          {
            params
          }
        )
        .then((resp) => resp.data)
  },
  getBorrowHistoryChart: {
    key: 'chart.getBorrowHistoryChart',
    call: (params: IChartParams) =>
      axiosInstance
        .get<IResponse<IBorrowHistoryChartItem[]>>(
          '/chart/getBorrowHistoryChart',
          {
            params
          }
        )
        .then((resp) => resp.data)
  },
  getSupplyAPYHistoryChart: {
    key: 'chart.getSupplyAPYHistoryChart',
    call: (params: IChartParams) =>
      axiosInstance
        .get<IResponse<ISupplyAPYHistoryChartItem[]>>(
          '/chart/getSupplyAPYHistoryChart',
          {
            params
          }
        )
        .then((resp) => resp.data)
  },
  getSupplyHistoryChart: {
    key: 'chart.getSupplyHistoryChart',
    call: (params: IChartParams) =>
      axiosInstance
        .get<IResponse<ISupplyHistoryChartItem[]>>(
          '/chart/getSupplyHistoryChart',
          {
            params
          }
        )
        .then((resp) => resp.data)
  },
  getUtilizationHistoryChart: {
    key: 'chart.getUtilizationHistoryChart',
    call: (params: IChartParams) =>
      axiosInstance
        .get<IResponse<IUtilizationHistoryChartItem[]>>(
          '/chart/getUtilizationHistoryChart',
          {
            params
          }
        )
        .then((resp) => resp.data)
  },
  getTokenPriceHistoryChart: {
    key: 'chart.getTokenPriceHistoryChart',
    call: (params: IChartParams) =>
      axiosInstance
        .get<IResponse<ITokenPriceHistoryChartItem[]>>(
          '/chart/getTokenPriceHistoryChart',
          {
            params
          }
        )
        .then((resp) => resp.data)
  }
}

export default ChartService
