import axiosInstance from '@/config/axios'
import type { IPagination, IResponse } from '@/types/data'
import type {
  IObligationInfo,
  ITransactionHistory,
  PebblePointsConfig,
  PebblePointsData,
  TransactionProductEnum,
  IObligationSupplyResponse
} from '@/types/user'

export interface IGetTransactionHistoryParams {
  userAddress: string
  product: TransactionProductEnum
  amountSort?: 'asc' | 'desc' | null
  chainTimestampMSSort?: 'asc' | 'desc' | null
  typeSort?: 'asc' | 'desc' | null
  page?: number
  size?: number
}

export type IGetAllTransactionHistoryParams = IGetTransactionHistoryParams & {
  productSort?: 'asc' | 'desc' | null
}

export type IGetMarketTransactionHistoryParams =
  IGetTransactionHistoryParams & {
    marketType?: string
  }

const UserService = {
  getObligationList: {
    key: 'user.getObligationList',
    call: (address: string) =>
      axiosInstance
        .get<IResponse<IObligationInfo[]>>(`/user/getObligationList/${address}`)
        .then((resp) => resp.data)
  },
  getAllTransactionHistory: {
    key: 'user.getAllTransactionHistory',
    call: (params: IGetAllTransactionHistoryParams) =>
      axiosInstance
        .get<
          IResponse<IPagination<ITransactionHistory>>
        >('/user/getAllTransactionHistory', { params: params })
        .then((resp) => resp.data)
  },
  getMarketTransactionHistory: {
    key: 'user.getMarketTransactionHistory',
    call: (params: IGetMarketTransactionHistoryParams) =>
      axiosInstance
        .get<
          IResponse<IPagination<ITransactionHistory>>
        >(`/user/getMarketTransactionHistory`, { params: params })
        .then((resp) => resp.data)
  },
  getPointsConfig: {
    key: 'user.getPointsConfig',
    call: () =>
      axiosInstance
        .get<IResponse<PebblePointsConfig>>('/user/getPointsConfig')
        .then((res) => res.data)
  },
  getPoints: {
    key: 'user.getPoints',
    call: (address: string) =>
      axiosInstance
        .get<IResponse<PebblePointsData>>(`/user/getPoints/${address}`)
        .then((res) => res.data)
  },
  getObligationSupply: {
    key: 'user.getObligationSupply',
    call: (userAddress: string) =>
      axiosInstance
        .get<
          IResponse<IObligationSupplyResponse>
        >(`/user/getObligationSupply/${userAddress}`)
        .then((resp) => resp.data)
  }
}

export default UserService
