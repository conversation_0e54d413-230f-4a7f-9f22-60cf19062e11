import axiosInstance from '@/config/axios'
import type { ITokenInfo, IWeb3Config, IMarketConfig } from '@/types/config'
import type { IResponse } from '@/types/data'

const ConfigService = {
  getWeb3Config: {
    key: 'config.getWeb3Config',
    call: () =>
      axiosInstance
        .get<IResponse<IWeb3Config>>('/pebbleWeb3Config/getConfig')
        .then((resp) => resp.data)
  },
  getAllTokenInfo: {
    key: 'config.getAllTokenInfo',
    call: () =>
      axiosInstance
        .get<IResponse<ITokenInfo[]>>('/pebbleWeb3Config/getAllTokenInfo')
        .then((resp) => resp.data)
  },
  getAllMarketConfig: {
    key: 'config.getAllMarketConfig',
    call: () =>
      axiosInstance
        .get<IResponse<IMarketConfig[]>>('/pebbleWeb3Config/getAllMarketConfig')
        .then((resp) => resp.data)
  }
}
export default ConfigService
