import type { <PERSON><PERSON><PERSON> } from '@/components/chart/types'
import axiosInstance from '@/config/axios'
import type { IResponse } from '@/types/data'
import type { IKLineInfo } from '@/types/kline'

export interface IGetKlineParams {
  address: string
  bar?: ResolutionKey
  fromTime: number
  toTime: number
  size?: number
}

const KLineService = {
  getMarginTradeKline: {
    key: 'kline.getMarginTradeKline',
    call: (params: IGetKlineParams) =>
      axiosInstance
        .get<
          IResponse<IKLineInfo[]>
        >('/kline/getMarginTradeKline', { params: params })
        .then((resp) => resp.data)
  }
}

export default KLineService
