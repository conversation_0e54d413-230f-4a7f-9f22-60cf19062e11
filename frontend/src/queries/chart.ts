import ChartService from '@/services/chart'
import type { QueryOptions } from '@/types/data'
import type {
  IBorrowAPYHistoryChartItem,
  IBorrowHistoryChartItem,
  ISupplyAPYHistoryChartItem,
  ISupplyHistoryChartItem,
  IUtilizationHistoryChartItem,
  ITokenPriceHistoryChartItem,
  IChartParams
} from '@/types/chart'
import { useQuery } from '@tanstack/react-query'

export const useGetBorrowAPYHistoryChart = (
  params: IChartParams,
  options?: QueryOptions<
    IBorrowAPYHistoryChartItem[],
    IBorrowAPYHistoryChartItem[]
  >
) => {
  return useQuery({
    queryKey: [ChartService.getBorrowAPYHistoryChart.key, params],
    queryFn: () => ChartService.getBorrowAPYHistoryChart.call(params),
    select: (data) => {
      return data?.data
    },
    enabled:
      !!params.from &&
      !!params.to &&
      !!params.marketType &&
      !!params.tokenAddress,
    ...options
  })
}

export const useGetBorrowHistoryChart = (
  params: IChartParams,
  options?: QueryOptions<IBorrowHistoryChartItem[], IBorrowHistoryChartItem[]>
) => {
  return useQuery({
    queryKey: [ChartService.getBorrowHistoryChart.key, params],
    queryFn: () => ChartService.getBorrowHistoryChart.call(params),
    select: (data) => {
      return data?.data
    },
    enabled:
      !!params.from &&
      !!params.to &&
      !!params.marketType &&
      !!params.tokenAddress,
    ...options
  })
}

export const useGetSupplyAPYHistoryChart = (
  params: IChartParams,
  options?: QueryOptions<
    ISupplyAPYHistoryChartItem[],
    ISupplyAPYHistoryChartItem[]
  >
) => {
  return useQuery({
    queryKey: [ChartService.getSupplyAPYHistoryChart.key, params],
    queryFn: () => ChartService.getSupplyAPYHistoryChart.call(params),
    select: (data) => {
      return data?.data
    },
    enabled:
      !!params.from &&
      !!params.to &&
      !!params.marketType &&
      !!params.tokenAddress,
    ...options
  })
}

export const useGetSupplyHistoryChart = (
  params: IChartParams,
  options?: QueryOptions<ISupplyHistoryChartItem[], ISupplyHistoryChartItem[]>
) => {
  return useQuery({
    queryKey: [ChartService.getSupplyHistoryChart.key, params],
    queryFn: () => ChartService.getSupplyHistoryChart.call(params),
    select: (data) => {
      return data?.data
    },
    enabled:
      !!params.from &&
      !!params.to &&
      !!params.marketType &&
      !!params.tokenAddress,
    ...options
  })
}

export const useGetUtilizationHistoryChart = (
  params: IChartParams,
  options?: QueryOptions<
    IUtilizationHistoryChartItem[],
    IUtilizationHistoryChartItem[]
  >
) => {
  return useQuery({
    queryKey: [ChartService.getUtilizationHistoryChart.key, params],
    queryFn: () => ChartService.getUtilizationHistoryChart.call(params),
    select: (data) => {
      return data?.data
    },
    enabled:
      !!params.from &&
      !!params.to &&
      !!params.marketType &&
      !!params.tokenAddress,
    ...options
  })
}

export const useGetTokenPriceHistoryChart = (
  params: IChartParams,
  options?: QueryOptions<
    ITokenPriceHistoryChartItem[],
    ITokenPriceHistoryChartItem[]
  >
) => {
  return useQuery({
    queryKey: [ChartService.getTokenPriceHistoryChart.key, params],
    queryFn: () => ChartService.getTokenPriceHistoryChart.call(params),
    select: (data) => {
      return data?.data
    },
    enabled:
      !!params.from &&
      !!params.to &&
      !!params.marketType &&
      !!params.tokenAddress,
    ...options
  })
}
