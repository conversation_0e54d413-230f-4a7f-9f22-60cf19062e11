import { useQuery } from '@tanstack/react-query'
import KLineService, { type IGetKlineParams } from '@/services/kline'
import type { IKLineInfo } from '@/types/kline'
import type { QueryOptions } from '@/types/data'

export const useGetMarginTradeKline = (
  params: IGetKlineParams,
  options?: QueryOptions<IKLineInfo[]>
) => {
  return useQuery({
    queryKey: [KLineService.getMarginTradeKline.key, params],
    queryFn: () => KLineService.getMarginTradeKline.call(params),
    ...options
  })
}
