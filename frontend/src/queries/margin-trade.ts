import { useQuery } from '@tanstack/react-query'
import type { QueryOptions } from '@/types/data'
import MarginTradeService from '@/services/margin-trade'
import type { IMarginTradeItem } from '@/types/margin-trade'

export const useGetMarginTradeList = (
  options?: QueryOptions<IMarginTradeItem[]>
) => {
  return useQuery({
    queryKey: [MarginTradeService.getMarginTradeList.key],
    queryFn: () => MarginTradeService.getMarginTradeList.call(),
    enabled: options?.enabled !== false,
    ...options
  })
}
