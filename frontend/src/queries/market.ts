import { useQuery } from '@tanstack/react-query'
import MarketService from '@/services/market'
import type { QueryOptions } from '@/types/data'
import type { IPagination } from '@/types/data'
import type { IMarketInfo } from '@/types/market'

export const useGetMarketList = (
  marketType: string,
  options?: QueryOptions<IPagination<IMarketInfo>, IMarketInfo[]>
) => {
  const params = {
    marketType,
    page: 1,
    size: 20
  }
  return useQuery({
    queryKey: [MarketService.getMarketList.key, marketType],
    queryFn: () => MarketService.getMarketList.call(params),
    select: (data) => {
      return data?.data?.content ?? []
    },
    enabled: !!marketType && options?.enabled !== false,
    ...options
  })
}
export const useGetMarketInfo = (
  marketName: string,
  assetToken: string,
  options?: QueryOptions<IMarketInfo, IMarketInfo>
) => {
  return useQuery({
    queryKey: [MarketService.getMarketInfo.key, marketName, assetToken],
    queryFn: () => MarketService.getMarketInfo.call({ marketName, assetToken }),
    select: (data) => {
      return data?.data ?? null
    },
    enabled: !!marketName && !!assetToken && options?.enabled !== false,
    ...options
  })
}
