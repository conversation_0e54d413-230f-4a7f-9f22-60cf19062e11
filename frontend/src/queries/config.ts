import ConfigService from '@/services/config'
import type { IWeb3Config, IMarketConfig } from '@/types/config'
import { useQuery } from '@tanstack/react-query'
import type { QueryOptions } from '@/types/data'
import type { ITokenInfo } from '@/types/config'
import { useMemo } from 'react'

export const useGetWeb3Config = (
  options?: QueryOptions<IWeb3Config, IWeb3Config>
) => {
  const { data, ...rest } = useQuery({
    queryKey: [ConfigService.getWeb3Config.key],
    queryFn: () => ConfigService.getWeb3Config.call(),
    select: (data) => {
      return data?.data
    },
    staleTime: 1000 * 60 * 60,
    gcTime: 1000 * 60 * 60 * 2,
    ...options
  })
  const suiNetworkConfig = useMemo(() => {
    return data?.web3Config?.sui?.[0]
  }, [data?.web3Config?.sui])

  return {
    suiNetworkConfig,
    ...rest
  }
}

export const useGetAllTokenInfo = (
  options?: QueryOptions<ITokenInfo[], ITokenInfo[]>
) => {
  return useQuery({
    queryKey: [ConfigService.getAllTokenInfo.key],
    queryFn: ConfigService.getAllTokenInfo.call,
    select: (data) => {
      return data?.data
    },
    staleTime: 1000 * 60 * 60,
    gcTime: 1000 * 60 * 60 * 2,
    ...options
  })
}

export const useGetAllMarketConfig = (
  options?: QueryOptions<IMarketConfig[], IMarketConfig[]>
) => {
  return useQuery({
    queryKey: [ConfigService.getAllMarketConfig.key],
    queryFn: ConfigService.getAllMarketConfig.call,
    select: (data) => {
      return data?.data
    },
    staleTime: 1000 * 60 * 60,
    gcTime: 1000 * 60 * 60 * 2,
    ...options
  })
}
