import UserService from '@/services/user'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { TransactionProductEnum } from '@/types/user'
import { useMemo } from 'react'
import { useInfinitePebbleQuery } from '@/hooks/use-infinite-pebble-query'
export const useGetAllTransactionHistory = (
  product: TransactionProductEnum,
  options?: {
    initialQuery?: {
      chainTimestampMSSort?: 'asc' | 'desc' | null
      typeSort?: 'asc' | 'desc' | null
      amountSort?: 'asc' | 'desc' | null
    }
  }
) => {
  const { userAddress } = usePebbleWallet()

  const initialQuery = useMemo(
    () => ({
      userAddress,
      product,
      ...options?.initialQuery
    }),
    [userAddress, product, options?.initialQuery]
  )

  return useInfinitePebbleQuery({
    queryKey: [
      UserService.getAllTransactionHistory.key,
      userAddress,
      product,
      options?.initialQuery
    ],
    queryFn: (params) => UserService.getAllTransactionHistory.call(params),
    initialQuery,
    enabled: !!userAddress
  })
}

export const useGetMarketTransactionHistory = (
  marketType: string,
  options?: {
    initialQuery?: {
      chainTimestampMSSort?: 'asc' | 'desc' | null
      typeSort?: 'asc' | 'desc' | null
      amountSort?: 'asc' | 'desc' | null
    }
  }
) => {
  const { userAddress } = usePebbleWallet()

  const initialQuery = useMemo(
    () => ({
      userAddress,
      marketType,
      product: TransactionProductEnum.Market,
      ...options?.initialQuery
    }),
    [userAddress, marketType, options?.initialQuery]
  )

  return useInfinitePebbleQuery({
    queryKey: [
      UserService.getMarketTransactionHistory.key,
      userAddress,
      marketType,
      options?.initialQuery
    ],
    queryFn: (params) => UserService.getMarketTransactionHistory.call(params),
    initialQuery,
    enabled: !!userAddress && !!marketType
  })
}
