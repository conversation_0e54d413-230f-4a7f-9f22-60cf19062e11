import { useQuery } from '@tanstack/react-query'
import type { QueryOptions } from '@/types/data'
import UserService from '@/services/user'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import {
  type IObligationInfo,
  type PebblePointsConfig,
  type PebblePointsData,
  type IObligationSupplyResponse
} from '@/types/user'
import { useMemo } from 'react'

export const useGetObligationList = (
  options?: QueryOptions<IObligationInfo[]>
) => {
  const { userAddress } = usePebbleWallet()
  return useQuery({
    queryKey: [UserService.getObligationList.key, userAddress.toLowerCase()],
    queryFn: () => UserService.getObligationList.call(userAddress),
    enabled: !!userAddress && options?.enabled !== false,
    ...options
  })
}

export const useGetPointsConfig = (
  options?: QueryOptions<PebblePointsConfig>
) => {
  return useQuery({
    queryKey: [UserService.getPointsConfig.key],
    queryFn: UserService.getPointsConfig.call,
    ...options
  })
}

export const useGetPoints = (options?: QueryOptions<PebblePointsData>) => {
  const { userAddress } = usePebbleWallet()
  const { data, ...rest } = useQuery({
    queryKey: [UserService.getPoints.key, userAddress.toLowerCase()],
    queryFn: () => UserService.getPoints.call(userAddress),
    enabled: !!userAddress && options?.enabled !== false,
    ...options
  })

  const nowSeason = useMemo(
    () => data?.data?.nowSeason ?? '-',
    [data?.data?.nowSeason]
  )

  const totalPoints = useMemo(
    () => data?.data?.totalPoints ?? 0,
    [data?.data?.totalPoints]
  )

  return {
    data,
    nowSeason,
    totalPoints,
    ...rest
  }
}

export const useGetObligationSupply = (
  options?: QueryOptions<IObligationSupplyResponse>
) => {
  const { userAddress } = usePebbleWallet()
  return useQuery({
    queryKey: [UserService.getObligationSupply.key, userAddress.toLowerCase()],
    queryFn: () => UserService.getObligationSupply.call(userAddress),
    enabled: !!userAddress && options?.enabled !== false,
    ...options
  })
}
