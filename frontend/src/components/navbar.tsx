import { Link, useLocation, useNavigate } from 'react-router'
import { cn } from '@/lib/utils'
import { getIllustrationUrl } from '@/lib/assets'
import { Button } from '@/components/ui/button'
import { PebblePintsNavButton } from './pebble-points'
import { ConnectWalletNavButton } from './connect-wallet'
import { routes, getRouteChildren } from '@/config/routes'

export const Navbar = () => {
  const location = useLocation()
  const navigate = useNavigate()

  const getIsRouteActive = (path: string) => {
    const hasChildren = !!getRouteChildren(path)?.length
    if (hasChildren) return location.pathname.startsWith(path)
    return location.pathname === path
  }

  return (
    <div className="sticky top-0 left-0 right-0 z-[100] border-b border-border-8 flex items-center justify-between backdrop-blur-[20px] h-[70px]">
      <div className="flex items-center h-full">
        <Button
          variant="ghost"
          onClick={() => navigate('/market')}
          className="h-full px-9 flex items-center justify-center rounded-none active:scale-100">
          <img
            width={93}
            height={16}
            className="shrink-0 no-drag"
            src={getIllustrationUrl('pebble-title-logo')}
            alt="logo"
          />
        </Button>
        <div className="flex h-full">
          {routes
            .filter((route) => route.position === 'left')
            .map((route) => {
              const isActive = getIsRouteActive(route.path)
              return (
                <Link
                  to={route.path}
                  key={route.path}
                  className="h-full border-l border-border-5 last:border-r">
                  <Button
                    variant="ghost"
                    className={cn(
                      'h-full w-[150px] rounded-none flex items-center justify-center active:scale-100',
                      isActive
                        ? 'font-medium bg-nav-active-gradient'
                        : 'opacity-40'
                    )}>
                    {route.label}
                  </Button>
                </Link>
              )
            })}
        </div>
      </div>
      <div className="flex items-center h-full">
        <div className="flex h-full">
          {routes
            .filter((route) => route.position === 'right')
            .map((route) => {
              const isActive = getIsRouteActive(route.path)
              return (
                <Link
                  to={route.path}
                  key={route.path}
                  className="h-full border-l border-border-10">
                  <Button
                    variant="ghost"
                    className={cn(
                      'h-full px-12 rounded-none flex items-center justify-center active:scale-100',
                      isActive ? 'font-medium bg-nav-active-gradient' : ''
                    )}>
                    <span className="flex items-center justify-center text-primary">
                      {route.label}
                    </span>
                  </Button>
                </Link>
              )
            })}
        </div>
        <PebblePintsNavButton />
        <ConnectWalletNavButton />
      </div>
    </div>
  )
}
