import React from 'react'
import { formatNumberWithSuperscriptZeros } from '@/lib/number'

type NumberCellProps = {
  value: number | string | bigint | null | undefined
  options?: {
    minimumFractionDigitsGreater1?: number
    maximumFractionDigitsGreater1?: number
    minimumFractionDigitsLess1?: number
    maximumFractionDigitsLess1?: number
    compact?: boolean
  }
  className?: string
  invalidDisplay?: React.ReactNode
  prefix?: string
  suffix?: string
}

export const NumberCell: React.FC<NumberCellProps> = ({
  value,
  options,
  className,
  invalidDisplay = '-',
  prefix,
  suffix
}) => {
  const numValue = Number(value)
  const isValidNumber =
    value !== null && value !== undefined && value !== '' && !isNaN(numValue)

  return (
    <span className={className}>
      {prefix && <span>{prefix}</span>}
      {isValidNumber
        ? formatNumberWithSuperscriptZeros(value, options)
        : invalidDisplay}
      {suffix && <span>{suffix}</span>}
    </span>
  )
}
