import { ChevronDownIcon } from '@/assets/icons'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { useState } from 'react'

interface MultiSelectProps<TData> {
  children: React.ReactElement
  selectAllLabel: React.ReactNode
  items: { label: React.ReactNode; value: TData }[]
  values?: TData[]
  className?: string
  onValueChange?: (v: TData[]) => void
}

export function MultiSelect<TData>({
  selectAllLabel,
  children,
  values,
  items,
  className,
  onValueChange
}: MultiSelectProps<TData>) {
  const [isOpen, setIsOpen] = useState(false)

  const handleSelectAll = () => {
    if (values?.length !== 0) {
      onValueChange?.([])
    } else {
      onValueChange?.(items.map((i) => i.value))
    }
  }

  const handleSelectItem = (v: TData) => {
    if (values?.includes(v)) {
      onValueChange?.(values.filter((i) => i !== v))
    } else {
      onValueChange?.([v, ...(values ?? [])])
    }
  }

  return (
    <div className={className}>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger
          onClick={() => setIsOpen((pre) => !pre)}
          className="hover:bg-border-8 data-[state=open]:bg-border-8 rounded-md px-3 py-1.5 w-[134px] data-[state=open]:[&_svg]:rotate-0 data-[state=closed]:[&_svg]:rotate-0 transition-none hover:opacity-100 hover:text-foreground">
          <div className="flex items-center gap-x-1.5">
            {children}
            <ChevronDownIcon />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent sideOffset={1.5}>
          <DropdownMenuItem
            onSelect={(e) => {
              e.preventDefault()
              handleSelectAll()
            }}
            className="w-[134px] p-0 h-7">
            {selectAllLabel}
          </DropdownMenuItem>
          {items?.map((item) => (
            <DropdownMenuItem
              key={String(item.value)}
              onSelect={(e) => {
                e.preventDefault()
                handleSelectItem(item.value)
              }}
              className="w-[134px] justify-start cursor-pointer py-1.5 px-3">
              <div className="flex items-center gap-x-2.5">
                <Checkbox checked={!!values?.includes(item.value)} />
                <span>{item.label}</span>
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}
