import { ChevronLeftIcon } from '@/assets/icons'
import { Button } from '@/components/ui/button'
import { useNavigate } from 'react-router'

export const BackButton: React.FC<{ className?: string }> = ({ className }) => {
  const navigate = useNavigate()

  return (
    <Button variant="icon" onClick={() => navigate(-1)} className={className}>
      <ChevronLeftIcon className="h-[31px]! w-[31px]!" />
    </Button>
  )
}
