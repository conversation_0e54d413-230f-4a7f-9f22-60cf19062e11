import { useState, useEffect, useId } from 'react'
import { IMaskInput } from 'react-imask'

import { cn } from '@/lib/utils'
import { isScientificNotation, NUMBER_INPUT_SCALE } from '@/lib/number'

export type NumberInputProps = {
  value?: string
  scale?: number
  placeholder?: string
  disabled?: boolean
  onInputChange?: (v?: string) => void
  min?: number
  max?: number
  inputClassName?: string
  wrapperClassName?: string
  onFocus?: () => void
  onBlur?: () => void
}

export const NumberInput: React.FC<NumberInputProps> = ({
  value,
  disabled,
  placeholder = '0.00',
  scale = NUMBER_INPUT_SCALE,
  onInputChange,
  inputClassName,
  wrapperClassName,
  min = 0,
  max,
  onFocus,
  onBlur
}) => {
  const id = useId()
  const [internalValue, setInternalValue] = useState<string>('')

  useEffect(() => {
    if (!value) {
      setInternalValue('')

      return
    }

    const num = Number(value)

    if (isNaN(num)) {
      setInternalValue('')

      return
    }

    if (isScientificNotation(num)) {
      setInternalValue(num.toFixed(scale).replace(/\.?0+$/, ''))
    } else {
      setInternalValue(value)
    }
  }, [value, scale])

  const handleAccept = (val: string) => {
    if (!val) {
      setInternalValue('')
      onInputChange?.(undefined)

      return
    }

    const parsed = parseFloat(val)

    if (isNaN(parsed)) return

    if (
      (min !== undefined && parsed < min) ||
      (max !== undefined && parsed > max)
    ) {
      return
    }

    setInternalValue(val)
    onInputChange?.(val)
  }

  return (
    <div className={wrapperClassName}>
      <IMaskInput
        id={id}
        className={cn(
          'text-2xl text-end size-full border-0 p-0 text-foreground placeholder:opacity-40 focus-within:placeholder:opacity-60 bg-transparent outline-none',
          disabled && 'cursor-not-allowed',
          inputClassName
        )}
        disabled={disabled}
        inputMode="decimal"
        lazy={false}
        mask={Number}
        max={max}
        min={min}
        overwrite={false}
        placeholder={placeholder}
        prepare={(str) => str.replace(/[^0-9.-]/g, '')}
        radix="."
        scale={scale}
        thousandsSeparator=","
        unmask="typed"
        value={internalValue}
        onAccept={handleAccept}
        onBlur={onBlur}
        onFocus={onFocus}
      />
    </div>
  )
}
