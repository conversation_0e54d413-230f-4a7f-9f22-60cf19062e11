import { useMemo } from 'react'
import { cn } from '@/lib/utils'
import {
  <PERSON>er,
  <PERSON>er<PERSON><PERSON><PERSON>,
  DrawerHeader,
  DrawerTitle
} from '@/components/ui/drawer'
import {
  MarginTradeIcon,
  MarketIcon,
  MultiplyIcon,
  PebblePointsIcon
} from '@/assets/icons'
import { PositionOverviewMenuEnum } from '@/pages/portfolio/types'
import { useDrawerStore } from '@/store/drawer-store'
import { useGetPoints, useGetPointsConfig } from '@/queries/user'
import groupBy from 'lodash/groupBy'

const POINTS_SOURCE_TO_KEY: { [key: number]: PositionOverviewMenuEnum } = {
  1: PositionOverviewMenuEnum.Market,
  2: PositionOverviewMenuEnum.Multiply,
  3: PositionOverviewMenuEnum.MarginTrade
}

const MENUKEY_TO_ICON = {
  [PositionOverviewMenuEnum.Market]: ({
    className
  }: {
    className?: string
  }) => <MarketIcon className={className} />,
  [PositionOverviewMenuEnum.Multiply]: ({
    className
  }: {
    className?: string
  }) => <MultiplyIcon className={className} />,
  [PositionOverviewMenuEnum.MarginTrade]: ({
    className
  }: {
    className?: string
  }) => <MarginTradeIcon className={className} />
}

export function PebblePintsNavButton() {
  const { isOpen, open } = useDrawerStore((state) => state.points)
  const { totalPoints } = useGetPoints()
  return (
    <div
      onClick={open}
      className={cn(
        'cursor-pointer w-[80px] h-full border-l border-border-10 flex hover:bg-border-5 rounded-none active:scale-100 p-0',
        isOpen && 'bg-border-5'
      )}>
      <div className="w-full flex items-center justify-center gap-x-2.5">
        <PebblePointsIcon />
        <span
          style={{
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}
          className="text-sm bg-gradient-1">
          {totalPoints}
        </span>
      </div>
    </div>
  )
}

export function PebblePointsDrawer() {
  const { isOpen, onOpenChange } = useDrawerStore((state) => state.points)

  const { data: pointsConfigData } = useGetPointsConfig()
  const { data: pointsData, nowSeason } = useGetPoints()

  const pointsBySeason = useMemo(() => {
    const allPointsData = pointsData?.data?.pointsDetails.map((detail) => {
      const key = POINTS_SOURCE_TO_KEY[detail.pointsSource]
      const point = detail.points
      const season = detail.season
      const leverage =
        season !== nowSeason ? '-' : (pointsConfigData?.data?.[key] ?? '-')
      return {
        key: key,
        label: key,
        point,
        season,
        leverage,
        Icon: MENUKEY_TO_ICON[key]
      }
    })
    return groupBy(allPointsData ?? [], 'season')
  }, [nowSeason, pointsConfigData?.data, pointsData?.data?.pointsDetails])

  const pointsOverview = useMemo(() => {
    const pointsBySource = groupBy(
      pointsData?.data?.pointsDetails ?? [],
      'pointsSource'
    )
    return Object.entries(pointsBySource).map(([source, points]) => ({
      key: POINTS_SOURCE_TO_KEY[+source],
      label: POINTS_SOURCE_TO_KEY[+source],
      Icon: MENUKEY_TO_ICON[POINTS_SOURCE_TO_KEY[+source]],
      point: points.reduce((acc, cur) => acc + (cur.points ?? 0), 0)
    }))
  }, [pointsData?.data?.pointsDetails])

  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange}>
      <DrawerContent>
        <DrawerHeader className="p-0 mb-9">
          <DrawerTitle className="flex items-center gap-x-1.5 font-normal text-base">
            <PebblePointsIcon />
            <span>Pebble Points: Season {nowSeason}</span>
          </DrawerTitle>
        </DrawerHeader>

        <div className="mb-9">
          {pointsOverview.map((data) => (
            <div
              key={data.key}
              className="p-3 border-b border-border-8 text-xs flex items-center justify-between">
              <div className="flex items-center gap-x-3">
                <data.Icon />
                <span>{data.label} Points</span>
              </div>
              <span>{data.point}</span>
            </div>
          ))}
        </div>

        <div className="flex flex-col gap-y-3">
          {Object.entries(pointsBySeason).map(([season]) => (
            <div
              key={season}
              className="bg-border-5 p-1.5 rounded-md flex flex-col gap-y-1.5">
              <div className="flex items-center justify-center gap-x-1.5">
                <PebblePointsIcon className="size-[11px]" />
                <div
                  style={{
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}
                  className="text-xs bg-gradient-1">
                  Pebble Points: Season {season}
                </div>
              </div>

              <a className="underline text-xs opacity-60 text-center">
                How does it work?
              </a>
            </div>
          ))}
        </div>
      </DrawerContent>
    </Drawer>
  )
}
