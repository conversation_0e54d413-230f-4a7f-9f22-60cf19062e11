import React from 'react'
import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'
import { Tabs as TabsPrimitive } from 'radix-ui'
import { useIsMounted } from 'usehooks-ts'

function Tabs({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Root>) {
  return (
    <TabsPrimitive.Root
      data-slot="tabs"
      className={cn('flex flex-col gap-2', className)}
      {...props}
    />
  )
}

function TabsList({
  className,
  children,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>) {
  const [indicatorStyle, setIndicatorStyle] = React.useState({
    left: 0,
    width: 0,
    height: 0
  })

  const tabsListRef = React.useRef<HTMLDivElement>(null)

  const getIsMounted = useIsMounted()

  const updateIndicator = React.useCallback(
    (activeElement: HTMLElement | null) => {
      if (activeElement) {
        const { offsetLeft, clientWidth, clientHeight } = activeElement
        setIndicatorStyle({
          left: offsetLeft,
          width: clientWidth,
          height: clientHeight
        })
      }
    },
    []
  )

  React.useEffect(() => {
    const tabsList = tabsListRef.current
    if (!tabsList) return

    const updateActiveTab = () => {
      const activeTab = tabsList.querySelector(
        '[data-state="active"]'
      ) as HTMLElement
      if (activeTab) {
        updateIndicator(activeTab)
      }
    }

    updateActiveTab()

    const observer = new MutationObserver(() => {
      updateActiveTab()
    })

    observer.observe(tabsList, {
      attributes: true,
      subtree: true,
      attributeFilter: ['data-state']
    })

    return () => observer.disconnect()
  }, [updateIndicator])

  return (
    <TabsPrimitive.List
      ref={tabsListRef}
      data-slot="tabs-list"
      className={cn(
        'text-muted-foreground/70 inline-flex w-full items-center justify-start gap-x-6 pl-6 border-b border-border-8 relative overflow-hidden',
        className
      )}
      {...props}>
      {children}
      {getIsMounted() && (
        <motion.div
          animate={{ left: indicatorStyle.left, width: indicatorStyle.width }}
          style={{ width: indicatorStyle.width, height: indicatorStyle.height }}
          className="absolute bottom-0 left-0 bg-active-tab-bg border-b border-b-border-40"
          transition={{
            type: 'tween',
            duration: 0.2,
            ease: 'easeInOut'
          }}
        />
      )}
    </TabsPrimitive.List>
  )
}

function TabsTrigger({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {
  return (
    <TabsPrimitive.Trigger
      data-slot="tabs-trigger"
      className={cn(
        'text-foreground hover:text-foreground/60 inline-flex items-center justify-center py-3 px-1 text-sm whitespace-nowrap transition-colors outline-none cursor-pointer relative data-[state=active]:text-primary',
        className
      )}
      {...props}
    />
  )
}

function TabsContent({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>) {
  return (
    <TabsPrimitive.Content
      data-slot="tabs-content"
      className={cn('flex-1 outline-none', className)}
      {...props}
    />
  )
}

export { Tabs, TabsContent, TabsList, TabsTrigger }
