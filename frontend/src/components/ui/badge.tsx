import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/lib/utils'

const badgeVariants = cva(
  'flex items-center justify-center rounded-sm px-1.5 py-1 text-xs w-[122px] whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none overflow-hidden',
  {
    variants: {
      variant: {
        default: 'bg-border-8 text-foreground',
        green: 'bg-[rgba(64,255,159,0.1)] text-green',
        yellow: 'bg-[rgba(229,188,91,0.1)] text-primary',
        blue: 'bg-[rgba(0,139,213,0.1)] text-blue',
        red: 'bg-[rgba(249,62,65,0.1)] text-red'
      }
    },
    defaultVariants: {
      variant: 'default'
    }
  }
)

function Badge({
  className,
  variant,
  asChild = false,
  ...props
}: React.ComponentProps<'span'> &
  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {
  const Comp = asChild ? Slot : 'span'

  return (
    <Comp
      data-slot="badge"
      className={cn(badgeVariants({ variant }), className)}
      {...props}
    />
  )
}

export { Badge, badgeVariants }
