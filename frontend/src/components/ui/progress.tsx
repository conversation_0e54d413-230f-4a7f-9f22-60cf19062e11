import { cn } from '@/lib/utils'
import { Progress as ProgressPrimitive } from 'radix-ui'

function Progress({
  className,
  ...props
}: React.ComponentProps<typeof ProgressPrimitive.Root>) {
  return (
    <ProgressPrimitive.Root
      data-slot="progress"
      className={cn('h-2 bg-white/10 rounded', className)}
      {...props}
    />
  )
}

function ProgressIndicator({
  className,
  ...props
}: React.ComponentProps<typeof ProgressPrimitive.Indicator>) {
  return (
    <ProgressPrimitive.Indicator
      data-slot="progress-indicator"
      className={cn(
        'h-2 rounded bg-[linear-gradient(90deg,_#E5BC5B_0%,_#235B88_100%)]',
        className
      )}
      {...props}
    />
  )
}

export { Progress, ProgressIndicator }
