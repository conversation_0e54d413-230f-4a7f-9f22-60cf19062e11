import * as React from 'react'
import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import { cn } from '@/lib/utils'
import { useRipple } from '@/hooks/use-ripple'

const buttonVariants = cva(
  'inline-flex items-center cursor-pointer justify-center whitespace-nowrap transition-all disabled:pointer-events-none disabled:bg-border-2 disabled:text-foreground/40 disabled:border-none shrink-0 [&_svg]:shrink-0 outline-none active:scale-[0.97] duration-200',
  {
    variants: {
      variant: {
        primary: 'bg-border-5 text-foreground hover:bg-border-8',
        secondary: cn(
          'bg-border-20 text-foreground hover:bg-border-12',
          'border border-transparent hover:border-border-20'
        ),
        outline:
          'border border-border-40 text-foreground bg-border-5 backdrop-blur-[10px] hover:bg-border-12',
        ghost: 'bg-transparent border-none text-foreground hover:bg-border-8',
        link: 'bg-transparent text-foreground border-none hover:bg-border-5 hover:underline',
        destructive:
          'text-foreground bg-red hover:bg-gradient-to-t hover:from-white/10 hover:to-white/10',
        icon: 'text-foreground bg-transparent hover:opacity-80 p-0!'
      },
      size: {
        sm: 'h-auto py-3 px-6 rounded-md text-sm/4'
      }
    },
    defaultVariants: {
      variant: 'primary',
      size: 'sm'
    }
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  disableRipple = false,
  ...props
}: React.ComponentProps<'button'> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  } & { disableRipple?: boolean }) {
  const Comp = asChild ? Slot : 'button'
  const { createRipple } = useRipple()
  return (
    <Comp
      data-slot="button"
      onMouseUp={disableRipple ? undefined : createRipple}
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
