import {
  ErrorIcon,
  SpinIcon,
  SuccessIcon,
  ToastCloseIcon
} from '@/assets/icons'
import { Toaster as Son<PERSON>, toast as sonnerToast } from 'sonner'
import { Button } from '@/components/ui/button'
import { useCurrentWallet } from '@mysten/dapp-kit'
import { WALLET_EXPLORER_URL } from '@/config/wallet'

const Toaster = () => {
  return <Sonner />
}

interface ToastProps {
  id: string | number
  hash?: string
  message?: string
  type: 'success' | 'error' | 'loading'
}

const Toast: React.FC<ToastProps> = ({ id, type, hash, message }) => {
  const wallet = useCurrentWallet()
  const icon = {
    error: <ErrorIcon />,
    success: <SuccessIcon />,
    loading: <SpinIcon className="animate-spin" />
  }
  const title = {
    error: hash ? 'Transaction Failed' : 'Error',
    success: hash ? 'Transaction Succeeded' : 'Succeeded',
    loading: hash ? 'Transaction Pending' : 'Pending'
  }
  const explorerUrl =
    !wallet.currentWallet || !wallet.currentWallet.name
      ? ''
      : WALLET_EXPLORER_URL[
          wallet.currentWallet.name as keyof typeof WALLET_EXPLORER_URL
        ]
  const hashLink = explorerUrl && hash ? `${explorerUrl}/${hash}` : ''
  return (
    <div className="border border-border-20 bg-border-5 backdrop-blur-[25px] size-full px-3 py-2 rounded-md overflow-hidden flex items-center gap-3 relative w-[281px]">
      {icon[type]}
      <div className="flex flex-col items-start">
        <div className="leading-none">{title[type]}</div>
        {hashLink ? (
          <a
            className="opacity-60 text-xs underline"
            href={hashLink}
            target="_blank">
            Check it onchain here
          </a>
        ) : (
          <span className="opacity-60 text-xs line-clamp-1 pr-5">
            {message}
          </span>
        )}
      </div>
      <div className="absolute bg-none top-1/2 -translate-y-1/2 right-0 px-3">
        <Button onClick={() => sonnerToast.dismiss(id)} variant="icon">
          <ToastCloseIcon className="w-[9px] h-[9px]" />
        </Button>
      </div>
    </div>
  )
}

interface ToastOptions {
  id?: string | number
  message?: string
  hash?: string
}

const toastInternal = (
  type: 'success' | 'error' | 'loading',
  { id, message, hash }: ToastOptions
) => {
  return sonnerToast.custom(
    (sonnerId) => (
      <Toast id={id || sonnerId} type={type} message={message} hash={hash} />
    ),
    { id: id, duration: type === 'loading' ? Infinity : undefined }
  )
}

const toast = {
  success: (options: ToastOptions) => toastInternal('success', options),
  error: (options: ToastOptions) => toastInternal('error', options),
  loading: (options: ToastOptions) => toastInternal('loading', options)
}

export { toast, Toaster }
