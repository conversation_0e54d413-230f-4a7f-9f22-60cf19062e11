import * as React from 'react'
import * as SliderPrimitive from '@radix-ui/react-slider'

import { cn } from '@/lib/utils'
import { SliderThumbIcon } from '@/assets/icons'

function Slider({
  className,
  defaultValue,
  value,
  min = 0,
  max = 100,
  suffix = 'x',
  showValue = true,
  ...props
}: React.ComponentProps<typeof SliderPrimitive.Root> & {
  suffix?: string
  showValue?: boolean
}) {
  const _values = React.useMemo(
    () =>
      Array.isArray(value)
        ? value
        : Array.isArray(defaultValue)
          ? defaultValue
          : [min, max],
    [value, defaultValue, min, max]
  )

  return (
    <SliderPrimitive.Root
      data-slot="slider"
      defaultValue={defaultValue}
      value={value}
      min={min}
      max={max}
      className={cn(
        'relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col',
        className
      )}
      {...props}>
      <SliderPrimitive.Track
        data-slot="slider-track"
        className={cn(
          'bg-[rgba(227,224,215,0.20)] relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-2 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5'
        )}>
        <SliderPrimitive.Range
          data-slot="slider-range"
          className={cn(
            'bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full'
          )}
        />
      </SliderPrimitive.Track>
      {Array.from({ length: _values.length }, (_, index) => (
        <SliderPrimitive.Thumb
          data-slot="slider-thumb"
          key={index}
          className="cursor-pointer relative disabled:pointer-events-none disabled:opacity-50 outline-none">
          <SliderThumbIcon className="size-5" />
          {showValue &&
            !!value?.[0] &&
            value?.[0] !== min &&
            value?.[0] !== max && (
              <span className="absolute left-1/2 -translate-x-1/2 top-[26px] text-xs">
                {value?.[0]}
                {suffix}
              </span>
            )}
        </SliderPrimitive.Thumb>
      ))}
    </SliderPrimitive.Root>
  )
}

export { Slider }
