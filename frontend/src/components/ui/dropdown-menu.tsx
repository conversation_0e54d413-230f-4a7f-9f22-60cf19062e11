import * as React from 'react'
import { DropdownMenu as DropdownMenuPrimitive } from 'radix-ui'
import { cn } from '@/lib/utils'

export const DropdownMenu = DropdownMenuPrimitive.Root

export const DropdownMenuTrigger = ({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) => {
  return (
    <DropdownMenuPrimitive.Trigger
      className={cn(
        'hover:opacity-80 data-[placeholder]:text-muted-foreground cursor-pointer flex w-fit items-center justify-between gap-1.5 bg-transparent text-2xl whitespace-nowrap transition-[color,box-shadow] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-8 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-1.5 [&_svg]:pointer-events-none [&_svg]:shrink-0 hover:text-foreground/80 outline-none ring-0 focus-visible:outline-none focus-visible:ring-0 focus:outline-none focus:ring-0',
        'data-[state=open]:[&_svg]:rotate-90 data-[state=closed]:[&_svg]:rotate-0 transition-all duration-200 ease-in-out',
        className
      )}
      {...props}>
      {children}
    </DropdownMenuPrimitive.Trigger>
  )
}

export function DropdownMenuContent({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {
  return (
    <DropdownMenuPrimitive.Portal>
      <DropdownMenuPrimitive.Content
        className={cn(
          'bg-select-content-bg text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 overflow-y-auto rounded-md border border-border-5 shadow-select-content-shadow',
          className
        )}
        sideOffset={10}
        {...props}>
        {children}
      </DropdownMenuPrimitive.Content>
    </DropdownMenuPrimitive.Portal>
  )
}

export function DropdownMenuItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Item>) {
  return (
    <DropdownMenuPrimitive.Item
      className={cn(
        "bg-border-8 backdrop-blur-[10px] hover:bg-border-12 focus:bg-accent cursor-pointer focus:text-accent-foreground font-normal border-b border-border-5 hover:border-border-8 last:border-b-0 text-nowrap [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full items-center justify-center p-3 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",
        'w-[165px] gap-2.5',
        className
      )}
      {...props}>
      {children}
    </DropdownMenuPrimitive.Item>
  )
}

export function DropdownMenuSeparator({
  className,
  children,
  ...props
}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {
  return (
    <DropdownMenuPrimitive.Separator
      className={cn('self-stretch h-px bg-border-12', className)}
      {...props}>
      {children}
    </DropdownMenuPrimitive.Separator>
  )
}

export const DropdownMenuLabel = DropdownMenuPrimitive.Label
export const DropdownMenuGroup = DropdownMenuPrimitive.Group
export const DropdownMenuArrow = DropdownMenuPrimitive.Arrow

export const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup
