import { getIllustrationUrl } from '@/lib/assets'
import { cn } from '@/lib/utils'

export function GradientPageTitle({
  className,
  title,
  description
}: {
  className?: string
  title: string
  description: string
}) {
  return (
    <div
      className={cn(
        'flex relative flex-col gap-y-1.5 items-center',
        className
      )}>
      <img
        alt="gradient-page-title-bg"
        src={getIllustrationUrl('leverage-title-bg', 'svg')}
        className="absolute shrink-0 top-[-100px] left-1/2 -translate-x-1/2 no-drag z-0"
      />
      <div
        className="p-2.5 bg-[rgba(21,15,8,0.10)]"
        style={{ backdropFilter: 'blur(5px)' }}>
        <span
          style={{
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}
          className="text-[28px] font-medium bg-gradient-2">
          {title}
        </span>
      </div>
      <p className="opacity-60 text-sm">{description}</p>
    </div>
  )
}
