import { useState } from 'react'
import { Chart } from '@/pages/margin-trade/components/chart'

// 🧪 Test component: Verify SSE connection management during coin pair switching
export function ChartSSETest() {
  const [currentCoinType, setCurrentCoinType] = useState('SUIUSDC')

  const testCoinTypes = ['S<PERSON>US<PERSON>', 'BTCUSDC', 'ETHUSDC', 'SOLUSDC']

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-4">
        📊 K-line Chart SSE Connection Test
      </h2>

      {/* Coin pair switching controls */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">
          Select Coin Pair for Testing:
        </h3>
        <div className="flex gap-2">
          {testCoinTypes.map((coinType) => (
            <button
              key={coinType}
              onClick={() => {
                console.log('🔄 Switching coin pair:', coinType)
                setCurrentCoinType(coinType)
              }}
              className={`px-4 py-2 rounded border ${
                currentCoinType === coinType
                  ? 'bg-blue-500 text-white border-blue-500'
                  : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
              }`}>
              {coinType}
            </button>
          ))}
        </div>
      </div>

      {/* Test instructions */}
      <div className="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
        <h4 className="font-semibold text-yellow-800">🔍 Test Points:</h4>
        <ul className="text-sm text-yellow-700 mt-2 space-y-1">
          <li>• Click different coin pairs and observe console logs</li>
          <li>• Confirm old SSE connections are closed during switching</li>
          <li>
            • Confirm new coin pair establishes SSE after initial data load
          </li>
          <li>• Observe the connection status indicator in upper right</li>
          <li>• Check for no memory leaks or multiple connections</li>
        </ul>
      </div>

      {/* Current test status */}
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded">
        <p className="text-blue-800">
          <strong>Current Coin Pair:</strong> {currentCoinType}
        </p>
        <p className="text-blue-600 text-sm mt-1">
          Please observe browser console logs to verify SSE connection
          management logic
        </p>
      </div>

      {/* Chart component */}
      <div className="border-2 border-dashed border-gray-300 p-4 rounded">
        <Chart
          coinType={currentCoinType}
          realtimeEnabled={true}
          useSSE={true}
        />
      </div>

      {/* Usage instructions */}
      <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded">
        <h4 className="font-semibold text-gray-800 mb-2">
          📋 Expected Behavior:
        </h4>
        <ol className="text-sm text-gray-700 space-y-1">
          <li>1. Page loads with default coin pair (SUIUSDC) chart</li>
          <li>
            2. After initial data loads, upper right shows "Connecting..."
          </li>
          <li>
            3. After SSE connects, status becomes "Real-time Data" (green
            indicator)
          </li>
          <li>
            4. When switching pairs, old SSE connection immediately disconnects
          </li>
          <li>5. New pair re-establishes SSE after data loads</li>
          <li>6. Console should show complete connection management logs</li>
        </ol>
      </div>
    </div>
  )
}
