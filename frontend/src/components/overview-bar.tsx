import { cn } from '@/lib/utils'
import type { FC, ReactNode } from 'react'
import { Button } from './ui/button'
import { WebIcon } from '@/assets/icons'

interface OverviewBarProps {
  data: {
    icon?: ReactNode
    label: string
    value?: string | number | ReactNode
    className?: string
    valueClassName?: string
    hasLink?: boolean
  }[]
  className?: string
  cellClassName?: string
}

export const OverviewBar: FC<OverviewBarProps> = ({
  data,
  className,
  cellClassName
}) => {
  return (
    <div
      className={cn(
        'flex text-sm border border-border-8 rounded-xl items-center justify-center bg-overview-gradient py-3',
        className
      )}>
      {data.map((v, index) => (
        <div
          key={index}
          className={cn(
            'flex-1 flex flex-col gap-3 py-3 items-center justify-center border-r-1 border-border-8 last:border-r-0 h-full',
            cellClassName
          )}>
          <div className="flex gap-1.5">
            {v.icon}
            <span className={cn(v.value && 'opacity-60', v.className)}>
              {v.label}
            </span>
          </div>
          {v.value && (
            <div className={cn('flex items-center gap-1.5', v.valueClassName)}>
              {v.value}{' '}
              {v.hasLink && (
                <Button
                  onClick={() => {
                    window.open('', '_blank')
                  }}
                  variant="icon"
                  className="h-[14px] w-[14px]">
                  <WebIcon className="size-[14px]" />
                </Button>
              )}
            </div>
          )}
        </div>
      ))}
    </div>
  )
}
