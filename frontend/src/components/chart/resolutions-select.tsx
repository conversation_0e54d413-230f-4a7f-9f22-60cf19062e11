import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { type FC, useState } from 'react'
import { ResolutionKey, resolutionMap } from './types'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '../ui/collapsible'
import { ChevronDownIcon } from '@/assets/icons'

const ResolutionsCollapsibleItems: FC<{
  type: string
  keys: ResolutionKey[]
}> = ({ type, keys }) => {
  const [open, setOpen] = useState(true)
  return (
    <Collapsible
      open={open}
      onOpenChange={setOpen}
      className="border-none w-full">
      <SelectLabel className="bg-border-12 text-center w-full text-foreground">
        <CollapsibleTrigger className="flex items-center w-full justify-center gap-1.5">
          <span>{type}</span>
          <ChevronDownIcon
            className={cn(
              'size-[5px] hover:opacity-60 cursor-pointer transition-all',
              open ? 'rotate-180' : 'rotate-0'
            )}
          />
        </CollapsibleTrigger>
      </SelectLabel>
      <CollapsibleContent>
        {keys.map((key) => (
          <SelectItem value={key} key={key} className="py-1.5 px-3 text-xs">
            {resolutionMap[key]}
          </SelectItem>
        ))}
      </CollapsibleContent>
    </Collapsible>
  )
}
export const ResolutionsSelect: FC<{
  value: ResolutionKey
  onValueChange?: (key: ResolutionKey) => void
}> = ({ value, onValueChange }) => {
  const resolutionGroups = [
    {
      type: 'Minutes',
      keys: [
        ResolutionKey.Min1,
        ResolutionKey.Min3,
        ResolutionKey.Min5,
        ResolutionKey.Min15,
        ResolutionKey.Min30
      ]
    },
    {
      type: 'Hours',
      keys: [ResolutionKey.Hour1, ResolutionKey.Hour5, ResolutionKey.Hour12]
    },
    {
      type: 'Days',
      keys: [ResolutionKey.Day1, ResolutionKey.Week1]
    }
  ]
  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className="text-sm rounded-md bg-border-8 py-1.5 px-3">
        <SelectValue>{resolutionMap[value]}</SelectValue>
      </SelectTrigger>
      <SelectContent className="rounded-md w-[165px]">
        {resolutionGroups.map((c) => (
          <SelectGroup key={c.type}>
            <ResolutionsCollapsibleItems type={c.type} keys={c.keys} />
          </SelectGroup>
        ))}
      </SelectContent>
    </Select>
  )
}
