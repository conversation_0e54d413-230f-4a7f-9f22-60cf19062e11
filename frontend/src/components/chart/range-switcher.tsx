import { cn } from '@/lib/utils'
import { useState, type FC } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger } from '../ui/select'
import { ToggleGroup, ToggleGroupItem } from '../ui/toggle-group'
import { ChartRangeEnum } from './types'

interface RangeSwitcherProps {
  value: ChartRangeEnum
  onRangeChange: (value: ChartRangeEnum) => void
}

export const RangeSwitcher: FC<RangeSwitcherProps> = ({
  value,
  onRangeChange
}) => {
  const [currentValue, setCurrentValue] = useState(value)
  const [currentSelectValue, setCurrentSelectValue] = useState(
    ChartRangeEnum.Month3
  )
  return (
    <div className="flex items-center">
      <ToggleGroup
        type="single"
        value={currentValue}
        onValueChange={(value) => {
          setCurrentValue(value as ChartRangeEnum)
          onRangeChange(value as ChartRangeEnum)
        }}>
        <ToggleGroupItem value={ChartRangeEnum.Day7}>
          {ChartRangeEnum.Day7}
        </ToggleGroupItem>
        <ToggleGroupItem value={ChartRangeEnum.Day30}>
          {ChartRangeEnum.Day30}
        </ToggleGroupItem>
      </ToggleGroup>
      <Select
        value={currentValue}
        onValueChange={(value) => {
          const val = value as ChartRangeEnum
          setCurrentSelectValue(val)
          setCurrentValue(val)
          onRangeChange(val)
        }}>
        <SelectTrigger className="pr-3">
          <div
            className={cn(
              'py-1.5 px-3 text-xs',
              currentValue === currentSelectValue && 'bg-border-8 rounded-md'
            )}>
            {currentSelectValue}
          </div>
        </SelectTrigger>
        <SelectContent className="rounded-md">
          <SelectItem value={ChartRangeEnum.Month3} className="p-3">
            {ChartRangeEnum.Month3}
          </SelectItem>
          <SelectItem value={ChartRangeEnum.Month6} className="p-3">
            {ChartRangeEnum.Month6}
          </SelectItem>
          <SelectItem value={ChartRangeEnum.Year1} className="p-3">
            {ChartRangeEnum.Year1}
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  )
}
