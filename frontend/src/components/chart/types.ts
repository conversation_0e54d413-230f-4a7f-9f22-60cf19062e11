export enum ResolutionKey {
  Min1 = '1m',
  Min3 = '3m',
  Min5 = '5m',
  Min15 = '15m',
  Min30 = '30m',
  Hour1 = '1h',
  Hour5 = '5h',
  Hour12 = '12h',
  Day1 = '1d',
  Week1 = '1w',
  Month1 = 'M'
}
export const resolutionMap: Record<ResolutionKey, string> = {
  [ResolutionKey.Min1]: '1 Minute',
  [ResolutionKey.Min3]: '3 Minutes',
  [ResolutionKey.Min5]: '5 Minutes',
  [ResolutionKey.Min15]: '15 Minutes',
  [ResolutionKey.Min30]: '30 Minutes',
  [ResolutionKey.Hour1]: '1 Hour',
  [ResolutionKey.Hour5]: '5 Hours',
  [ResolutionKey.Hour12]: '12 Hours',
  [ResolutionKey.Day1]: '1 Day',
  [ResolutionKey.Week1]: '1 Week',
  [ResolutionKey.Month1]: '1 Month'
}

export const BAR_DURATION_MAP: Record<ResolutionKey, number> = {
  [ResolutionKey.Min1]: 60 * 1000,
  [ResolutionKey.Min3]: 3 * 60 * 1000,
  [ResolutionKey.Min5]: 5 * 60 * 1000,
  [ResolutionKey.Min15]: 15 * 60 * 1000,
  [ResolutionKey.Min30]: 30 * 60 * 1000,
  [ResolutionKey.Hour1]: 60 * 60 * 1000,
  [ResolutionKey.Hour5]: 5 * 60 * 60 * 1000,
  [ResolutionKey.Hour12]: 12 * 60 * 60 * 1000,
  [ResolutionKey.Day1]: 24 * 60 * 60 * 1000,
  [ResolutionKey.Week1]: 7 * 24 * 60 * 60 * 1000,
  [ResolutionKey.Month1]: 30 * 24 * 60 * 60 * 1000
}

export enum ChartRangeEnum {
  Day7 = '7D',
  Day30 = '30D',
  Month3 = '3M',
  Month6 = '6M',
  Year1 = '1Y'
}

export const RANGE_RESOLUTION_MAP: Record<ChartRangeEnum, ResolutionKey> = {
  [ChartRangeEnum.Day7]: ResolutionKey.Hour1,
  [ChartRangeEnum.Day30]: ResolutionKey.Hour5,
  [ChartRangeEnum.Month3]: ResolutionKey.Hour12,
  [ChartRangeEnum.Month6]: ResolutionKey.Day1,
  [ChartRangeEnum.Year1]: ResolutionKey.Day1
}

export enum ChartCurrencyEnum {
  Usd = 'USD',
  Sui = 'SUI'
}

export enum BaseChartType {
  Line,
  Area
}

export enum PriceFormat {
  Percentage,
  Number,
  Usd
}

export enum TimeFormat {
  Time,
  Number,
  Percentage
}

export enum VerticalLineType {
  Solid,
  Dashed
}
