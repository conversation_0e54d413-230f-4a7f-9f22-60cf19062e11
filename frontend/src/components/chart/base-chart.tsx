import { useEffect, useRef, useState, type FC } from 'react'
import {
  AreaSeries,
  createChart,
  LineSeries,
  type UTCTimestamp
} from 'lightweight-charts'
import { cn } from '@/lib/utils'
import {
  BaseChartType,
  PriceFormat,
  TimeFormat,
  VerticalLineType
} from './types'
import { formatNumberWithSuperscriptZeros } from '@/lib/number'
import { VertLine } from './vertical-line'

export interface ChartData {
  time: string | UTCTimestamp
  value: number
}
export interface ChartSeriesData {
  data: ChartData[]
  title?: string
  options?: {
    lineColor?: string
    topColor?: string
    bottomColor?: string
  }
  priceLineDisabled?: boolean
  priceLine?: {
    value: number
    title: string
  }
  vertLine?: {
    time: UTCTimestamp
    label?: string
    type?: VerticalLineType
  }
}
interface BaseChartProps {
  seriesData: ChartSeriesData[]
  type?: BaseChartType
  priceFormat?: PriceFormat
  timeFormat?: TimeFormat
  className?: string
  toolTipTimePrefix?: string
  handleScroll?: boolean
  handleScale?: boolean
}

export const BaseChart: FC<BaseChartProps> = ({
  seriesData,
  type = BaseChartType.Area,
  priceFormat = PriceFormat.Number,
  timeFormat = TimeFormat.Time,
  className,
  toolTipTimePrefix = '',
  handleScroll = false,
  handleScale = false
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const [tooltipData, setTooltipData] = useState<{
    time: string
    data: {
      title: string
      value: number
      color: string
    }[]
    visible: boolean
    x: number
    y: number
  } | null>(null)

  useEffect(() => {
    if (!ref.current) return

    const chart = createChart(ref.current, {
      layout: {
        background: { color: 'transparent' },
        textColor: '#DDD'
      },
      grid: {
        vertLines: { visible: false },
        horzLines: { visible: false }
      },
      handleScroll,
      handleScale,
      timeScale: {
        tickMarkFormatter: (time: UTCTimestamp | string) => {
          if (typeof time === 'string') {
            return time
          }
          if (timeFormat === TimeFormat.Number) {
            return time.toFixed(2)
          }
          if (timeFormat === TimeFormat.Percentage) {
            return `${(time * 100).toFixed(0)}%`
          }
          const d = new Date(time * 1000)
          const m = String(d.getMonth() + 1).padStart(2, '0')
          const day = String(d.getDate()).padStart(2, '0')
          return `${day}/${m}`
        }
      },
      crosshair: {
        vertLine: {
          labelVisible: false
        },
        horzLine: {
          visible: false,
          labelVisible: false
        }
      },
      localization: {
        timeFormatter: (time: UTCTimestamp | string) => {
          if (typeof time === 'string') {
            return time
          }
          if (timeFormat === TimeFormat.Number) {
            return time.toFixed(2)
          }
          if (timeFormat === TimeFormat.Percentage) {
            return `${(time * 100).toFixed(0)}%`
          }
          const d = new Date(time * 1000)
          const day = d.getDate()
          const month = d.toLocaleDateString('en-US', { month: 'short' })
          const year = String(d.getFullYear()).slice(-2)
          const h = String(d.getHours()).padStart(2, '0')
          const min = String(d.getMinutes()).padStart(2, '0')
          return `${day} ${month} '${year} ${h}:${min}`
        },
        priceFormatter: (price: number) => {
          return priceFormat === PriceFormat.Percentage
            ? `${price.toFixed(2)}%`
            : priceFormat === PriceFormat.Usd
              ? `$${formatNumberWithSuperscriptZeros(price, {
                  disableSuperscriptZeros: true
                })}`
              : formatNumberWithSuperscriptZeros(price, {
                  disableSuperscriptZeros: true
                })
        }
      }
    })

    const seriesList: {
      series: ReturnType<typeof chart.addSeries>
      color: string
      index: number
      title: string
    }[] = []

    seriesData.forEach((s, index) => {
      const series =
        type === BaseChartType.Line
          ? chart.addSeries(LineSeries, {
              color: s.options?.lineColor ?? '#E7E4DA',
              lineWidth: 1
            })
          : chart.addSeries(AreaSeries, {
              lineColor: s.options?.lineColor ?? '#E7E4DA',
              topColor: s.options?.topColor ?? '#2962FF',
              bottomColor: s.options?.bottomColor ?? 'rgba(41, 98, 255, 0.28)',
              lineWidth: 1
            })
      if (
        timeFormat === TimeFormat.Number ||
        timeFormat === TimeFormat.Percentage
      ) {
        const convertedData = s.data.map((item) => ({
          time: Number(item.time) as UTCTimestamp,
          value: item.value
        }))
        series.setData(convertedData)
      } else {
        series.setData(s.data)
      }
      if (s.priceLineDisabled) {
        series.applyOptions({
          lastValueVisible: false,
          priceLineVisible: false
        })
      }
      if (s.priceLine) {
        series.createPriceLine({
          price: s.priceLine.value,
          color: s.options?.lineColor ?? '#E7E4DA',
          lineWidth: 2,
          lineStyle: 0,
          axisLabelVisible: true,
          title: s.priceLine.title
        })
      }

      // Store series reference, color, and title
      seriesList.push({
        series,
        color: s.options?.lineColor ?? '#E7E4DA',
        index,
        title: s.title || `Series ${index + 1}`
      })

      if (s.vertLine && index === 0) {
        const vertLine = new VertLine(chart, series, s.vertLine.time, {
          showLabel: true,
          labelText: s.vertLine.label ?? '',
          type: s.vertLine.type ?? VerticalLineType.Dashed
        })
        series.attachPrimitive(vertLine)
      }
    })

    chart.timeScale().fitContent()

    // Subscribe to crosshair movement for tooltip
    chart.subscribeCrosshairMove((param) => {
      if (param.time && param.seriesData.size > 0) {
        const tooltipDataList: {
          title: string
          value: number
          color: string
        }[] = []

        // Iterate through all series to get their data
        seriesList.forEach(({ series, color, title }) => {
          const seriesData = param.seriesData.get(series) as
            | ChartData
            | undefined
          if (seriesData) {
            tooltipDataList.push({
              title,
              value: seriesData.value,
              color
            })
          }
        })

        if (tooltipDataList.length > 0) {
          let timeStr = ''
          if (typeof param.time === 'string') {
            timeStr = param.time
          } else {
            if (timeFormat === TimeFormat.Number) {
              timeStr = (param.time as UTCTimestamp).toFixed(2)
            } else if (timeFormat === TimeFormat.Percentage) {
              timeStr = `${((param.time as UTCTimestamp) * 100).toFixed(2)}%`
            } else {
              const d = new Date((param.time as UTCTimestamp) * 1000)
              const day = d.getDate()
              const month = d.toLocaleDateString('en-US', { month: 'short' })
              const year = String(d.getFullYear())
              const h = String(d.getHours()).padStart(2, '0')
              const min = String(d.getMinutes()).padStart(2, '0')
              timeStr = `${day} ${month} ${year} ${h}:${min}`
            }
          }

          setTooltipData({
            time: `${toolTipTimePrefix}${timeStr}`,
            data: tooltipDataList,
            visible: true,
            x: param.point?.x || 0,
            y: param.point?.y || 0
          })
        }
      } else {
        setTooltipData(null)
      }
    })

    const handleResize = () => {
      chart.applyOptions({
        width: ref.current?.clientWidth,
        height: ref.current?.clientHeight
      })
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chart.remove()
    }
  }, [
    seriesData,
    type,
    priceFormat,
    timeFormat,
    toolTipTimePrefix,
    handleScroll,
    handleScale
  ])

  return (
    <div className={cn('w-full h-full relative', className)}>
      <div ref={ref} className="w-full h-full" />
      {tooltipData && (
        <div
          className="absolute pointer-events-none z-10"
          style={{
            left: Math.max(
              10,
              Math.min(
                tooltipData.x + 10,
                (ref.current?.clientWidth || 0) - 200
              )
            ),
            top: Math.max(tooltipData.y - 40, 10),
            transform: 'translateY(-50%)'
          }}>
          <div className="bg-tooltip-gradient border border-border-8 rounded-md p-2 text-xs shadow-[0_0_24px_6px_rgba(23,16,9,0.2)] min-w-[150px]">
            <div className="text-foreground font-medium">
              {tooltipData.time}
            </div>
            {tooltipData.data.map((item, index) => (
              <div
                key={index}
                className="flex items-center justify-between gap-2 mt-1">
                <div className="flex items-center gap-1">
                  <div
                    className="w-2 h-2 rounded-full"
                    style={{ backgroundColor: item.color }}
                  />
                  <span className="text-foreground">{item.title}</span>
                </div>
                <span className="text-foreground">
                  {priceFormat === PriceFormat.Percentage
                    ? `${item.value.toFixed(2)}%`
                    : priceFormat === PriceFormat.Usd
                      ? `$${formatNumberWithSuperscriptZeros(item.value, {
                          disableSuperscriptZeros: true
                        })}`
                      : formatNumberWithSuperscriptZeros(item.value, {
                          disableSuperscriptZeros: true
                        })}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
