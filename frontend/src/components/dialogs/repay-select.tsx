import { type FC } from 'react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select'
import { DebtActionEnum } from '@/pages/market/types'
export const RepaySelect: FC<{
  value: string
  onValueChange: (value: string) => void
}> = ({ value, onValueChange }) => {
  return (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger className="text-sm text-white rounded-md bg-border-8 py-1.5 px-3">
        <SelectValue>
          {value === DebtActionEnum.RepayFromWallet
            ? 'From Wallet'
            : 'With Collateral'}
        </SelectValue>
      </SelectTrigger>
      <SelectContent className="rounded-md">
        <SelectItem value={DebtActionEnum.RepayFromWallet} className="py-1.5">
          From Wallet
        </SelectItem>
        <SelectItem
          value={DebtActionEnum.RepayWithCollateral}
          className="py-1.5">
          With Collateral
        </SelectItem>
      </SelectContent>
    </Select>
  )
}
