import { SettingIcon } from '@/assets/icons'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { Input } from '../ui/input'
import { cn } from '@/lib/utils'
import { useEffect, useState } from 'react'

const SLIPPAGE_OPTIONS = [
  {
    label: '0.5%',
    value: 0.005
  },
  {
    label: '1%',
    value: 0.01
  },
  {
    label: '3%',
    value: 0.03
  }
]

export const SlippageSetting: React.FC<{
  slippage: number
  onConfirm: (slippage: number) => void
}> = ({ slippage, onConfirm }) => {
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [selectedSlippage, setSelectedSlippage] = useState<number>(
    SLIPPAGE_OPTIONS[0].value
  )
  const [customSlippage, setCustomSlippage] = useState<number>()

  useEffect(() => {
    if (
      slippage === SLIPPAGE_OPTIONS[0].value ||
      slippage === SLIPPAGE_OPTIONS[1].value ||
      slippage === SLIPPAGE_OPTIONS[2].value
    ) {
      setSelectedSlippage(slippage)
      setCustomSlippage(0)
    } else {
      setSelectedSlippage(0)
      setCustomSlippage(slippage * 100)
    }
  }, [slippage])

  const handleConfirm = () => {
    if (customSlippage) {
      onConfirm(customSlippage / 100)
    } else {
      onConfirm(selectedSlippage)
    }
    setIsOpen(false)
  }

  return (
    <>
      <Button
        variant="icon"
        className="h-4! text-xs flex items-center gap-x-0.5"
        onClick={() => setIsOpen(true)}>
        {slippage * 100}%
        <SettingIcon className="size-[18px]" />
      </Button>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent
          showCloseButton={false}
          className="p-3 w-[353px] gap-y-3">
          <DialogTitle className="p-0 border-b-0 text-base">
            Slippage Setting
          </DialogTitle>

          <div className="flex items-center gap-x-3 text-xs">
            <div className="grid grid-cols-3">
              {SLIPPAGE_OPTIONS.map((option) => (
                <Button
                  key={option.value}
                  variant="ghost"
                  onClick={() => {
                    setSelectedSlippage(option.value)
                    setCustomSlippage(0)
                  }}
                  className={cn(
                    'w-full h-[27px]',
                    option.value === selectedSlippage &&
                      !customSlippage &&
                      'bg-border-8'
                  )}>
                  {option.label}
                </Button>
              ))}
            </div>

            <div className="flex items-center gap-x-1.5">
              <span className="p-1.5">Custom</span>
              <Input
                className="w-12 h-[27px] px-1 py-1"
                type="number"
                min={0}
                value={customSlippage || ''}
                onChange={(e) => setCustomSlippage(Number(e.target.value))}
              />
              <span className="p-1.5">%</span>
            </div>
          </div>

          <Button
            variant="secondary"
            className="w-full"
            onClick={handleConfirm}>
            OK
          </Button>
        </DialogContent>
      </Dialog>
    </>
  )
}
