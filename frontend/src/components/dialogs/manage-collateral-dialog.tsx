import { ChevronRightIcon, SettingIcon, ViewMarketIcon } from '@/assets/icons'
import { CollateralActionEnum } from '../../pages/market/types'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { BalanceInput } from '@/components/balance-input'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useEffect, useMemo, useState } from 'react'
import TransationDetail from '@/components/dialogs/transaction-detail'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { useNavigate } from 'react-router'
import { useDialogStore } from '@/store/dialog-store'
import { useSupply } from '@/hooks/use-supply'
import { useWithdraw } from '@/hooks/use-withdraw'
import { OperationName } from '@pebble-protocol/pebble-sdk'
import { useMarketOperationInfo } from '@/pages/market/hooks/use-market-operation-info'

export const ManageCollateralButton: React.FC<{
  id: string
  marketName?: string
  marketType?: string
  coinType: string
  fromAssets?: boolean
  disabled?: boolean
  isDropdownMenuOpen?: boolean
  onDropdownMenuChange?: (value: boolean) => void
}> = ({
  id,
  marketName = '',
  marketType = '',
  coinType,
  fromAssets,
  disabled,
  isDropdownMenuOpen,
  onDropdownMenuChange
}) => {
  const openModal = useDialogStore((state) => state.collateral.openModal)
  const navigate = useNavigate()
  return (
    <div className="flex justify-end">
      {fromAssets ? (
        <DropdownMenu
          open={isDropdownMenuOpen}
          onOpenChange={onDropdownMenuChange}>
          <DropdownMenuTrigger disabled={disabled}>
            <ChevronRightIcon aria-hidden="true" />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              onClick={() =>
                openModal(id, marketName, coinType, CollateralActionEnum.Supply)
              }>
              Supply
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() =>
                openModal(
                  id,
                  marketName,
                  coinType,
                  CollateralActionEnum.Withdraw
                )
              }>
              Withdraw
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => navigate(`/market/${marketType}/${coinType}`)}>
              <ViewMarketIcon />
              <span>View Market</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <Button
          className="w-[100px]"
          disabled={disabled}
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            openModal(id, marketName, coinType, CollateralActionEnum.Supply)
          }}>
          Manage
        </Button>
      )}
    </div>
  )
}
const ManageCollateralDialog = () => {
  const {
    marketName,
    coinType: assetCoinType,
    isOpen,
    action,
    closeModal
  } = useDialogStore((state) => state.collateral)
  const [currentAction, setCurrentAction] = useState(action)
  const [amount, setAmount] = useState('')
  const {
    tokens,
    coinType,
    marketInfo,
    isFetching: isFetchingMarketInfo
  } = useMarketOperationInfo(marketName, assetCoinType)
  const { supply, isFetching } = useSupply(
    marketName,
    assetCoinType,
    marketInfo
  )
  const { withdraw, maxWithdrawAmount } = useWithdraw(marketName, marketInfo)
  const handleClick = () => {
    if (!marketInfo) return
    if (currentAction === CollateralActionEnum.Supply) {
      supply.mutate(
        {
          amount,
          coinType,
          decimals: Number(marketInfo.tokenInfo.decimals)
        },
        {
          onSuccess: () => {
            closeModal()
            setAmount('')
          }
        }
      )
    } else {
      withdraw.mutate(
        {
          amount,
          coinType,
          decimals: Number(marketInfo.tokenInfo.decimals)
        },
        {
          onSuccess: () => {
            closeModal()
            setAmount('')
          }
        }
      )
    }
  }

  const isAmountValid = useMemo(() => Number(amount) > 0, [amount])

  useEffect(() => {
    setCurrentAction(action)
  }, [action])

  useEffect(() => {
    if (isOpen) {
      setAmount('')
    }
  }, [isOpen])
  return (
    <>
      <Dialog open={isOpen} onOpenChange={closeModal}>
        <DialogContent className="w-[377px] py-0">
          <div className="relative pb-6">
            <DialogTitle className="border-none pb-0">
              <Tabs
                value={currentAction}
                orientation="vertical"
                onValueChange={(value) => {
                  setCurrentAction(value as CollateralActionEnum)
                  setAmount('')
                }}>
                <TabsList aria-label="collateral tabs" className="pl-0 gap-0">
                  <TabsTrigger
                    value={CollateralActionEnum.Supply}
                    className="w-[146px]">
                    {CollateralActionEnum.Supply}
                  </TabsTrigger>
                  <TabsTrigger
                    value={CollateralActionEnum.Withdraw}
                    className="w-[146px]">
                    {CollateralActionEnum.Withdraw}
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </DialogTitle>
            <div className="flex flex-col gap-6 mt-6 px-6 relative">
              <BalanceInput
                value={amount}
                onChange={(v) => setAmount(v ?? '')}
                title={`${currentAction} asset`}
                tokens={tokens}
                price={
                  marketInfo?.tokenInfo.price
                    ? Number(marketInfo.tokenInfo.price)
                    : undefined
                }
                balanceTitle={
                  currentAction === CollateralActionEnum.Withdraw
                    ? 'Available'
                    : undefined
                }
                available={
                  currentAction === CollateralActionEnum.Withdraw
                    ? Number(maxWithdrawAmount)
                    : undefined
                }
              />
              <Button
                variant="secondary"
                onClick={handleClick}
                disabled={isFetching || isFetchingMarketInfo || !isAmountValid}>
                {currentAction}
              </Button>
              <div className="flex flex-col text-xs gap-3">
                <div className="flex justify-between items-center">
                  <span>Transation Settings</span>
                  <Button variant="icon" className="h-4!">
                    <SettingIcon className="size-[18px]" />
                  </Button>
                </div>
                <div className="flex justify-between items-center">
                  <span>Supply APY</span>
                  <span className="text-green">
                    {marketInfo && marketInfo?.supplyAPY !== null
                      ? `${(Number(marketInfo.supplyAPY) * 100).toFixed(2)}%`
                      : '-'}
                  </span>
                </div>
              </div>
            </div>
            <TransationDetail
              marketName={marketName}
              operation={
                currentAction === CollateralActionEnum.Supply
                  ? OperationName.Deposit
                  : OperationName.Withdraw
              }
              assetType={coinType}
              amount={amount}
              decimals={Number(marketInfo?.tokenInfo.decimals)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
export default ManageCollateralDialog
