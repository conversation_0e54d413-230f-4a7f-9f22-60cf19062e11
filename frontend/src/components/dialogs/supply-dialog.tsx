import { BalanceInput } from '@/components/balance-input'
import { Button } from '@/components/ui/button'
import { <PERSON>alog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { memo, useEffect, useMemo, useState, type FC } from 'react'
import { SettingIcon } from '@/assets/icons'
import { useDialogStore } from '@/store/dialog-store'
import { useSupply } from '@/hooks/use-supply'
import { useMarketOperationInfo } from '@/pages/market/hooks/use-market-operation-info'

export const SupplyButton: FC<{
  id: string
  marketName: string
  coinType: string
  disabled?: boolean
  isLp?: boolean
}> = memo(({ id, marketName = '', coinType, disabled, isLp }) => {
  const openModal = useDialogStore((state) => state.supply.openModal)

  return (
    <Button
      className="w-[100px]"
      disabled={disabled}
      onClick={(e) => {
        e.preventDefault()
        e.stopPropagation()
        openModal(id, marketName, coinType, !!isLp)
      }}>
      Supply
    </Button>
  )
})

const SupplyDialog = () => {
  const {
    marketName,
    coinType: assetCoinType,
    isOpen,
    closeModal,
    isLp
  } = useDialogStore((state) => state.supply)
  const [amount, setAmount] = useState('')
  const {
    tokens,
    coinType,
    marketInfo,
    isFetching: isFetchingMarketInfo
  } = useMarketOperationInfo(marketName, assetCoinType)
  const { supply, isFetching } = useSupply(
    marketName,
    assetCoinType,
    marketInfo
  )

  const handleSupply = () => {
    if (!marketInfo) return
    supply.mutate(
      {
        amount,
        coinType,
        decimals: Number(marketInfo.tokenInfo.decimals)
      },
      {
        onSuccess: () => {
          closeModal()
          setAmount('')
        }
      }
    )
  }

  const isAmountValid = useMemo(() => Number(amount) > 0, [amount])

  useEffect(() => {
    if (isOpen) {
      setAmount('')
    }
  }, [isOpen])

  return (
    <>
      <Dialog open={isOpen} onOpenChange={closeModal}>
        <DialogContent className="w-[377px] py-0">
          <div className="relative pb-6">
            <DialogTitle>Supply</DialogTitle>
            <div className="flex flex-col gap-6 mt-6 px-6 relative">
              <BalanceInput
                value={amount}
                onChange={(v) => setAmount(v ?? '')}
                title="Supply asset"
                tokens={tokens}
                price={
                  marketInfo?.tokenInfo.price
                    ? Number(marketInfo.tokenInfo.price)
                    : undefined
                }
              />
              <Button
                variant="secondary"
                onClick={handleSupply}
                disabled={isFetching || isFetchingMarketInfo || !isAmountValid}>
                {isLp ? 'Supply LP Asset' : 'Supply'}
              </Button>
              <div className="flex flex-col text-xs gap-3">
                <div className="flex justify-between items-center">
                  <span>Transation Settings</span>
                  <Button variant="icon" className="h-4!">
                    <SettingIcon className="size-[18px]" />
                  </Button>
                </div>
                <div className="flex justify-between items-center">
                  <span>Supply APY</span>
                  <span className="text-green">
                    {marketInfo && marketInfo?.supplyAPY !== null
                      ? `${(Number(marketInfo.supplyAPY) * 100).toFixed(2)}%`
                      : '-'}
                  </span>
                </div>
                {isLp && (
                  <div className="flex justify-between items-center">
                    <span>CETUS APY</span>
                    <span className="text-green">4.5%</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
export default SupplyDialog
