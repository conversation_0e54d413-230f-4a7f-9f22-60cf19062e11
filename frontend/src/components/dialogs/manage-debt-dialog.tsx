import { ChevronRightIcon, SettingIcon, ViewMarketIcon } from '@/assets/icons'
import { DebtActionEnum } from '../../pages/market/types'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { BalanceInput } from '@/components/balance-input'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useEffect, useMemo, useState } from 'react'
import TransationDetail from './transaction-detail'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu'
import { useNavigate } from 'react-router'
import { useDialogStore } from '@/store/dialog-store'
import { useBorrow } from '@/hooks/use-borrow'
import { useRepay } from '@/hooks/use-repay'
import { OperationName } from '@pebble-protocol/pebble-sdk'
import { useMarketOperationInfo } from '@/pages/market/hooks/use-market-operation-info'
import { NumberCell } from '../number-cell'

export const ManageDebtButton: React.FC<{
  id: string
  marketName?: string
  marketType: string
  coinType: string
  fromAssets?: boolean
  disabled?: boolean
  isDropdownMenuOpen?: boolean
  onDropdownMenuChange?: (value: boolean) => void
}> = ({
  id,
  marketName = '',
  marketType,
  coinType,
  fromAssets,
  disabled,
  isDropdownMenuOpen,
  onDropdownMenuChange
}) => {
  const openModal = useDialogStore((state) => state.debt.openModal)
  const navigate = useNavigate()
  return (
    <div className="flex justify-end">
      {fromAssets ? (
        <DropdownMenu
          open={isDropdownMenuOpen}
          onOpenChange={onDropdownMenuChange}>
          <DropdownMenuTrigger disabled={disabled}>
            <ChevronRightIcon aria-hidden="true" />
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem
              onClick={() =>
                openModal(
                  id,
                  marketName,
                  coinType,
                  DebtActionEnum.RepayFromWallet
                )
              }>
              Repay
            </DropdownMenuItem>
            {/* <DropdownMenuItem
              onClick={() => openModal(id, DebtActionEnum.RepayWithCollateral)}>
              Repay With Collateral
            </DropdownMenuItem> */}
            <DropdownMenuItem
              onClick={() =>
                openModal(id, marketName, coinType, DebtActionEnum.Borrow)
              }>
              Borrow
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => navigate(`/market/${marketType}/${coinType}`)}>
              <ViewMarketIcon />
              <span>View Market</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ) : (
        <Button
          className="w-[100px]"
          disabled={disabled}
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            openModal(id, marketName, coinType, DebtActionEnum.Borrow)
          }}>
          Manage
        </Button>
      )}
    </div>
  )
}

const ManageDebtDialog = () => {
  const {
    marketName,
    coinType: assetCoinType,
    isOpen,
    action,
    closeModal
  } = useDialogStore((state) => state.debt)
  const [repayWith, setRepayWith] = useState(DebtActionEnum.RepayFromWallet)
  const [amount, setAmount] = useState('')
  const {
    tokens,
    coinType,
    marketInfo,
    isFetching: isFetchingMarketInfo,
    borrowedAmount
  } = useMarketOperationInfo(marketName, assetCoinType)
  const { borrow, isFetching, maxBorrowAmount, liqAvailableUSD } = useBorrow(
    marketName,
    assetCoinType,
    marketInfo
  )
  const { repay } = useRepay(marketName)

  const maxRepayAmount = useMemo(() => {
    if (!borrowedAmount || !marketInfo || !marketInfo.tokenInfo.price) return 0
    const maxRepay = borrowedAmount + 1 / Number(marketInfo.tokenInfo.price)
    return Number(maxRepay.toFixed(6))
  }, [borrowedAmount, marketInfo])

  const [currentTab, setCurrentTab] = useState(
    action === DebtActionEnum.Borrow
      ? DebtActionEnum.Borrow.toString()
      : 'Repay'
  )
  const balanceTitle = useMemo(() => {
    if (currentTab === DebtActionEnum.Borrow.toString()) return 'Borrow asset'
    else return 'Repay asset'
    // return (
    //   <RepaySelect
    //     value={repayWith}
    //     onValueChange={(value) => setRepayWith(value as DebtActionEnum)}
    //   />
    // )
  }, [currentTab])

  const handleClick = () => {
    if (!marketInfo) return
    if (currentTab === DebtActionEnum.Borrow) {
      borrow.mutate(
        {
          amount,
          coinType,
          decimals: Number(marketInfo.tokenInfo.decimals)
        },
        {
          onSuccess: () => {
            closeModal()
            setAmount('')
          }
        }
      )
    } else {
      repay.mutate(
        {
          amount,
          coinType,
          decimals: Number(marketInfo.tokenInfo.decimals)
        },
        {
          onSuccess: () => {
            closeModal()
            setAmount('')
          }
        }
      )
    }
  }

  const isAmountValid = useMemo(() => Number(amount) > 0, [amount])

  useEffect(() => {
    setCurrentTab(
      action === DebtActionEnum.Borrow
        ? DebtActionEnum.Borrow.toString()
        : 'Repay'
    )
    setRepayWith(
      action !== DebtActionEnum.Borrow ? action : DebtActionEnum.RepayFromWallet
    )
  }, [action])

  useEffect(() => {
    if (isOpen) {
      setAmount('')
    }
  }, [isOpen])

  return (
    <>
      <Dialog open={isOpen} onOpenChange={closeModal}>
        <DialogContent className="w-[377px] py-0">
          <div className="relative pb-6">
            <DialogTitle className="border-none pb-0">
              <Tabs
                value={currentTab}
                orientation="vertical"
                onValueChange={(value) => {
                  setCurrentTab(value)
                  setAmount('')
                }}>
                <TabsList aria-label="collateral tabs" className="pl-0 gap-0">
                  <TabsTrigger
                    value={DebtActionEnum.Borrow}
                    className="w-[146px]">
                    {DebtActionEnum.Borrow}
                  </TabsTrigger>
                  <TabsTrigger value="Repay" className="w-[146px]">
                    Repay
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </DialogTitle>
            <div className="flex flex-col gap-6 mt-6 px-6 relative">
              <BalanceInput
                value={amount}
                onChange={(v) => setAmount(v ?? '')}
                title={balanceTitle}
                tokens={tokens}
                price={
                  marketInfo?.tokenInfo.price
                    ? Number(marketInfo.tokenInfo.price)
                    : undefined
                }
                balanceTitle="Available"
                available={currentTab === 'Repay' ? undefined : maxBorrowAmount}
                maxAmount={currentTab === 'Repay' ? maxRepayAmount : undefined}
              />
              {repayWith === DebtActionEnum.RepayWithCollateral && (
                <BalanceInput title="Repay With" tokens={tokens} />
              )}
              <Button
                variant="secondary"
                disabled={isFetching || isFetchingMarketInfo || !isAmountValid}
                onClick={handleClick}>
                {currentTab}
              </Button>
              <div className="flex flex-col text-xs gap-3">
                <div className="flex justify-between items-center">
                  <span>Transation Settings</span>
                  <Button variant="icon" className="h-4!">
                    <SettingIcon className="size-[18px]" />
                  </Button>
                </div>
                <div className="flex justify-between items-center">
                  <span>Liquidity available</span>
                  <NumberCell value={liqAvailableUSD} prefix="$" />
                </div>
                <div className="flex justify-between items-center">
                  <span>Borrow APY</span>
                  <span className="text-primary">
                    {marketInfo && marketInfo?.borrowAPY !== null
                      ? `${(Number(marketInfo.borrowAPY) * 100).toFixed(2)}%`
                      : '-'}
                  </span>
                </div>
              </div>
            </div>
            <TransationDetail
              marketName={marketName}
              operation={
                currentTab === DebtActionEnum.Borrow
                  ? OperationName.Borrow
                  : OperationName.Repay
              }
              assetType={coinType}
              amount={amount}
              decimals={Number(marketInfo?.tokenInfo.decimals)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
export default ManageDebtDialog
