import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { getIconUrl } from '@/lib/assets'
import { useDialogStore } from '@/store/dialog-store'
import { Avatar } from '../ui/avatar'

export const ClaimRewardsButton: React.FC<{
  id: string
}> = ({ id }) => {
  const openModal = useDialogStore((state) => state.claim.openModal)

  return (
    <Button
      className="w-[100px]"
      onClick={(e) => {
        e.preventDefault()
        e.stopPropagation()
        openModal(id)
      }}>
      Claim
    </Button>
  )
}

const ClaimRewardsDiablog = () => {
  const { isOpen, closeModal } = useDialogStore((state) => state.claim)

  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent className="w-[377px] py-0">
        <DialogTitle>Claim Rewards</DialogTitle>
        <div className="p-6 pt-2 flex flex-col gap-y-6">
          <div className="rounded-md p-3 border border-border-8 bg-border-2 flex flex-col gap-y-3">
            <div className="text-xs opacity-60">Asset</div>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-x-1.5">
                  <div className="relative mr-3">
                    <Avatar
                      src={getIconUrl('stSui')}
                      fallback="stSui"
                      alt="sui-logo"
                      size={18}
                    />
                    <Avatar
                      src={getIconUrl('usdc', 'svg')}
                      fallback="usdc"
                      alt="usdc-logo"
                      size={18}
                      className="absolute left-3 top-0"
                    />
                  </div>
                  <div>suiUSDT-USDC</div>
                </div>
                <div className="flex items-center gap-x-1.5 text-[10px] text-green opacity-60">
                  <span>CETUS</span>
                  <span>0.01%</span>
                </div>
              </div>
              <div className="opacity-60">
                ~<span className="underline">$54.28</span>
              </div>
            </div>
            <div className="w-full h-[1px] bg-border-8" />
            <div>
              <div className="text-xs opacity-60 mb-3">Rewards</div>
              <div className="mb-1.5 flex items-center justify-between">
                <span className="flex items-center gap-x-1.5">
                  <Avatar
                    src={getIconUrl('cetus')}
                    fallback="cetus"
                    alt="cetus-logo"
                    size={18}
                  />
                  <span>CETUS</span>
                </span>
                <span className="text-[10px] flex items-center gap-x-1.5">
                  <span>3.293</span>
                  <span className="opacity-60">($29.23)</span>
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="flex items-center gap-x-1.5">
                  <Avatar
                    src={getIconUrl('sui')}
                    fallback="sui"
                    alt="sui-logo"
                    size={18}
                  />
                  <span>SUI</span>
                </span>
                <span className="text-[10px] flex items-center gap-x-1.5">
                  <span>43.293</span>
                  <span className="opacity-60">($29.23)</span>
                </span>
              </div>
            </div>
          </div>
          <Button variant="secondary">Collect Rewards</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
export default ClaimRewardsDiablog
