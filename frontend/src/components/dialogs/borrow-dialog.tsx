import { BalanceInput } from '@/components/balance-input'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { memo, useEffect, useMemo, useState, type FC } from 'react'
import { SettingIcon } from '@/assets/icons'
import TransationDetail from '@/components/dialogs/transaction-detail'
import { useDialogStore } from '@/store/dialog-store'
import { useBorrow } from '@/hooks/use-borrow'
import { OperationName } from '@pebble-protocol/pebble-sdk'
import { useMarketOperationInfo } from '@/pages/market/hooks/use-market-operation-info'
import { NumberCell } from '../number-cell'

export const BorrowButton: FC<{
  id: string
  marketName: string
  coinType: string
  disabled?: boolean
}> = memo(({ id, marketName, coinType, disabled }) => {
  const openModal = useDialogStore((state) => state.borrow.openModal)

  return (
    <Button
      className="w-[100px]"
      disabled={disabled}
      onClick={(e) => {
        e.preventDefault()
        e.stopPropagation()
        openModal(id, marketName, coinType)
      }}>
      Borrow
    </Button>
  )
})

const BorrowDialog = () => {
  const {
    marketName,
    coinType: assetCoinType,
    isOpen,
    closeModal
  } = useDialogStore((state) => state.borrow)
  const [amount, setAmount] = useState('')
  const {
    tokens,
    coinType,
    marketInfo,
    isFetching: isFetchingMarketInfo
  } = useMarketOperationInfo(marketName, assetCoinType)
  const { borrow, isFetching, maxBorrowAmount, liqAvailableUSD } = useBorrow(
    marketName,
    assetCoinType,
    marketInfo
  )

  const handleBorrow = () => {
    if (!marketInfo) return
    borrow.mutate(
      {
        amount,
        coinType,
        decimals: Number(marketInfo?.tokenInfo.decimals)
      },
      {
        onSuccess: () => {
          closeModal()
          setAmount('')
        }
      }
    )
  }
  const isAmountValid = useMemo(() => Number(amount) > 0, [amount])

  useEffect(() => {
    if (isOpen) {
      setAmount('')
    }
  }, [isOpen])
  return (
    <>
      <Dialog open={isOpen} onOpenChange={closeModal}>
        <DialogContent className="w-[377px] py-0">
          <div className="relative pb-6">
            <DialogTitle>Borrow</DialogTitle>
            <div className="flex flex-col gap-6 mt-6 px-6 relative">
              <BalanceInput
                value={amount}
                onChange={(v) => setAmount(v ?? '')}
                title="Borrow asset"
                tokens={tokens}
                price={
                  marketInfo?.tokenInfo.price
                    ? Number(marketInfo.tokenInfo.price)
                    : undefined
                }
                available={maxBorrowAmount}
                balanceTitle="Available"
              />
              <Button
                variant="secondary"
                onClick={handleBorrow}
                disabled={isFetching || isFetchingMarketInfo || !isAmountValid}>
                Borrow
              </Button>
              <div className="flex flex-col text-xs gap-3">
                <div className="flex justify-between items-center">
                  <span>Transation Settings</span>
                  <Button variant="icon" className="h-4!">
                    <SettingIcon className="size-[18px]" />
                  </Button>
                </div>
                <div className="flex justify-between items-center">
                  <span>Liquidity available</span>
                  <NumberCell value={liqAvailableUSD} prefix="$" />
                </div>
                <div className="flex justify-between items-center">
                  <span>Borrow APY</span>
                  <span className="text-primary">
                    {marketInfo && marketInfo?.borrowAPY !== null
                      ? `${(Number(marketInfo.borrowAPY) * 100).toFixed(2)}%`
                      : '-'}
                  </span>
                </div>
              </div>
            </div>
            <TransationDetail
              marketName={marketName}
              operation={OperationName.Borrow}
              assetType={coinType}
              amount={amount}
              decimals={Number(marketInfo?.tokenInfo.decimals)}
            />
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
export default BorrowDialog
