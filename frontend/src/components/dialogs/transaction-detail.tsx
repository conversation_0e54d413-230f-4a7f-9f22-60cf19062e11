import { ChevronDownIcon, ChevronUpIcon } from '@/assets/icons'
import { Avatar } from '@/components/ui/avatar'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible'
import { getIconUrl } from '@/lib/assets'
import { useState } from 'react'
import { OperationName } from '@pebble-protocol/pebble-sdk'
import { useTransactionDetail } from '@/hooks/use-transaction-detail'
import { getCoinSymbol } from '@/lib/coin'
import { NumberCell } from '../number-cell'
import { getMarketName } from '@/lib/utils'

export const TransactionValues = ({
  from,
  to,
  className,
  isPercentage,
  isValidInput = true,
  isUsdValue = true
}: {
  from?: number
  to?: number
  className?: string
  isPercentage?: boolean
  isValidInput?: boolean
  isUsdValue?: boolean
}) => {
  const hasTo =
    to !== undefined &&
    to !== null &&
    from !== undefined &&
    from !== null &&
    to !== from &&
    isValidInput
  return (
    <span className={className}>
      {isPercentage ? (
        <NumberCell value={from ?? 0} suffix="%" />
      ) : (
        <NumberCell value={from ?? 0} prefix={isUsdValue ? '$' : ''} />
      )}
      {hasTo && ` → `}
      {hasTo &&
        (isPercentage ? (
          <NumberCell value={to ?? 0} suffix="%" />
        ) : (
          <NumberCell value={to ?? 0} />
        ))}
    </span>
  )
}

const TransationDetail: React.FC<{
  marketName: string
  operation: OperationName
  assetType: string
  amount: string
  decimals?: number
}> = ({ marketName, operation, assetType, amount, decimals }) => {
  const [open, setOpen] = useState(false)
  const { data: transactionDetail } = useTransactionDetail(
    marketName,
    operation,
    assetType,
    amount,
    decimals
  )
  const validInput = !!amount && amount !== '' && Number(amount) > 0
  return (
    <div className="w-[276px] flex flex-col absolute top-0 left-[100%] ml-3 rounded-lg border border-border-10 bg-dialog-gradient py-3">
      <div className="border-b border-border-8 px-6 pb-3">
        Transaction Detail
      </div>
      <div className="flex flex-col mt-6 pb-3 px-6 text-xs">
        <div className="flex flex-col gap-3">
          <div className="flex justify-between items-center">
            <span>Market Name</span>
            <span>{getMarketName(marketName)}</span>
          </div>
          <div className="flex justify-between items-center">
            <span>Net Value</span>
            <TransactionValues
              from={transactionDetail?.netValue.from}
              to={transactionDetail?.netValue.to}
              isValidInput={validInput}
            />
          </div>
          <div className="flex justify-between items-center">
            <span>Net APY</span>
            <NumberCell
              className="text-green"
              value={
                transactionDetail?.netApy ? transactionDetail.netApy * 100 : 0
              }
              suffix="%"
            />
          </div>
          <Collapsible
            className="border-none"
            open={open}
            onOpenChange={setOpen}>
            <CollapsibleTrigger className="w-full flex justify-between items-center">
              <span>LTV</span>
              <div className="flex gap-1 items-center">
                <TransactionValues
                  className="text-green"
                  from={transactionDetail?.ltv.from}
                  to={transactionDetail?.ltv.to}
                  isValidInput={validInput}
                  isPercentage
                />
                {open ? (
                  <ChevronUpIcon className="size-[10px] hover:text-primary cursor-pointer transition-all" />
                ) : (
                  <ChevronDownIcon className="size-[10px] hover:text-primary cursor-pointer transition-all" />
                )}
              </div>
            </CollapsibleTrigger>

            <CollapsibleContent className="bg-border-5 rounded-sm flex flex-col p-3 gap-1.5 mt-1">
              <div className="flex justify-between items-center">
                <span>Max.LTV</span>
                <NumberCell
                  value={
                    transactionDetail?.maxLtv
                      ? transactionDetail.maxLtv * 100
                      : 0
                  }
                  suffix="%"
                />
              </div>
              <div className="flex justify-between items-center">
                <span>Liq.LTV</span>
                <NumberCell
                  value={
                    transactionDetail?.liqLtv
                      ? transactionDetail.liqLtv * 100
                      : 0
                  }
                  suffix="%"
                />
              </div>
            </CollapsibleContent>
          </Collapsible>
        </div>
        {transactionDetail?.collateralValue && (
          <div className="pt-3 pb-1 flex justify-between">
            <span>Colleteral</span>
            <TransactionValues
              from={transactionDetail?.collateralValue.from}
              to={transactionDetail?.collateralValue.to}
              isValidInput={validInput}
            />
          </div>
        )}
        {transactionDetail?.collateralAsset && (
          <div className="bg-border-5 rounded-sm flex justify-between items-center p-3 gap-1.5 mt-1">
            <div className="flex items-center gap-1.5">
              <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />
              {getCoinSymbol(assetType)}
            </div>
            <TransactionValues
              from={transactionDetail?.collateralAsset.from}
              to={transactionDetail?.collateralAsset.to}
              isValidInput={validInput}
            />
          </div>
        )}
        {transactionDetail?.debtValue && (
          <div className="pt-3 pb-1 flex justify-between">
            <span>Debt</span>
            <TransactionValues
              from={transactionDetail?.debtValue.from}
              to={transactionDetail?.debtValue.to}
              isValidInput={validInput}
            />
          </div>
        )}
        {transactionDetail?.debtAsset && (
          <div className="bg-border-5 rounded-sm flex justify-between items-center p-3 gap-1.5 mt-1">
            <div className="flex items-center gap-1.5">
              <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />
              {getCoinSymbol(assetType)}
            </div>
            <TransactionValues
              from={transactionDetail?.debtAsset.from}
              to={transactionDetail?.debtAsset.to}
              isValidInput={validInput}
            />
          </div>
        )}
      </div>
    </div>
  )
}

export default TransationDetail
