import { cn } from '@/lib/utils'
import React, { useEffect, useRef, useState, type ReactNode } from 'react'

interface AnchorMenuProps {
  menu: {
    id: string
    label: string
    icon: ReactNode
  }[]
  defaultActiveMenuId: string
  children: ReactNode
  offsetTop?: number
  className?: string
  menuClassName?: string
}

export const AnchorMenu: React.FC<AnchorMenuProps> = ({
  menu,
  defaultActiveMenuId,
  children,
  offsetTop = 0,
  className,
  menuClassName
}) => {
  const [activeId, setActiveId] = useState<string>(defaultActiveMenuId)
  const [fixed, setFixed] = useState(false)

  const menuRef = useRef<HTMLDivElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveId(entry.target.id)
          }
        })
      },
      {
        root: null,
        rootMargin: '-50% 0px -50% 0px',
        threshold: 0
      }
    )

    menu.forEach((m) => {
      const el = document.getElementById(m.id)
      if (el) observer.observe(el)
    })

    return () => observer.disconnect()
  }, [menu])

  // menu 固定逻辑
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return

      const containerTop = containerRef.current.getBoundingClientRect().top

      if (containerTop <= offsetTop) {
        setFixed(true)
      } else {
        setFixed(false)
      }
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [offsetTop])

  const handleClick = (id: string) => {
    const el = document.getElementById(id)
    if (el) {
      const scrollTo =
        el.getBoundingClientRect().top + window.scrollY - offsetTop
      window.scrollTo({ top: scrollTo, behavior: 'smooth' })
    }
  }

  return (
    <div ref={containerRef} className={cn('flex w-full relative', className)}>
      {/* 占位元素，防止右侧内容跑到左边 */}
      {fixed && <div className={menuClassName} />}

      <div
        ref={menuRef}
        className={cn(
          'transition-all flex flex-col gap-1.5',
          fixed && 'fixed',
          menuClassName
        )}
        style={
          fixed
            ? {
                top: `${offsetTop}px`,
                left: containerRef.current?.getBoundingClientRect().left || 0
              }
            : {}
        }>
        {menu.map((s) => (
          <div
            key={s.id}
            onClick={() => handleClick(s.id)}
            className={cn(
              'cursor-pointer p-1.5 rounded-md transition flex gap-1.5',
              activeId === s.id && 'bg-border-12'
            )}>
            <div className="size-6 flex items-center justify-center">
              {s.icon}
            </div>
            {s.label}
          </div>
        ))}
      </div>

      {children}
    </div>
  )
}
