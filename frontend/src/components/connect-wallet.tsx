import {
  WALLET_ICON,
  WALLET_NAME,
  WALLET_WEB_URL,
  WalletEnum
} from '@/config/wallet'
import { displayAddress } from '@/lib/formatter'
import { useRef, useState } from 'react'
import { cn } from '@/lib/utils'
import {
  <PERSON><PERSON>,
  <PERSON>er<PERSON>ontent,
  DrawerFooter,
  Drawer<PERSON>eader,
  DrawerTitle
} from '@/components/ui/drawer'
import { Loader2Icon } from 'lucide-react'
import { Button } from './ui/button'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { useOnClickOutside } from 'usehooks-ts'
import { motion, AnimatePresence } from 'framer-motion'
import { useDrawerStore } from '@/store/drawer-store'

export function ConnectWalletNavButton() {
  const { open } = useDrawerStore((state) => state.wallet)
  const [isDisconnectOpen, setIsDisconnectOpen] = useState(false)
  const disconnectButtonRef = useRef(null)
  const { disconnect, userAddress, isConnected, isLoading, currentWallet } =
    usePebbleWallet()

  const handleClickTrigger = () => {
    if (isLoading) return
    if (isConnected) {
      setIsDisconnectOpen(true)
    } else {
      open()
    }
  }
  const handleDisconnect = async () => {
    if (isLoading) return
    await disconnect()
    setIsDisconnectOpen(false)
  }

  const buttonClassName =
    'flex px-6 items-center gap-x-3 cursor-pointer text-sm text-nowrap h-full'

  useOnClickOutside(
    disconnectButtonRef as unknown as React.RefObject<HTMLElement>,
    () => {
      setIsDisconnectOpen(false)
    }
  )

  if (isConnected && !isLoading) {
    return (
      <div className="relative h-full border-l border-r border-border-10 w-[167px]">
        <Button
          variant="ghost"
          onClick={() => setIsDisconnectOpen((pre) => !pre)}
          className={cn(
            buttonClassName,
            'cursor-pointer rounded-none w-full flex items-center justify-center gap-x-1.5 active:scale-100'
          )}>
          {WALLET_ICON[currentWallet?.name as keyof typeof WALLET_ICON]}
          {displayAddress(userAddress)}
        </Button>

        <AnimatePresence>
          {isDisconnectOpen && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 44, opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ type: 'tween', duration: 0.2, ease: 'easeInOut' }}
              className="absolute left-[-1px] w-[167px] top-full border border-border-10 overflow-hidden"
              ref={disconnectButtonRef}
              style={{ willChange: 'height, opacity' }}>
              <Button
                variant="ghost"
                onClick={handleDisconnect}
                className={cn(
                  'cursor-pointer font-normal text-base rounded-none size-full active:scale-100 bg-disconnect-bg hover:bg-disconnect-hover-bg'
                )}>
                Disconnect
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    )
  }

  return (
    <div
      onClick={handleClickTrigger}
      className={cn(
        buttonClassName,
        'cursor-pointer font-medium w-[167px] flex border-l border-border-10 items-center justify-center gap-x-1.5 bg-gradient-1 text-black'
      )}>
      {isLoading ? (
        <>
          <Loader2Icon className="animate-spin" />
          Connecting...
        </>
      ) : (
        'Connect Wallet'
      )}
    </div>
  )
}

export function ConnectWalletDrawer() {
  const { isOpen, onOpenChange, close } = useDrawerStore(
    (state) => state.wallet
  )
  const { connect, supportedWallets } = usePebbleWallet()

  const handleConnect = async (w: WalletEnum) => {
    const wallet = supportedWallets[w]
    if (wallet) {
      await connect({ wallet: wallet })
      close()
    } else {
      window.open(WALLET_WEB_URL[w], '_blank')
    }
  }

  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange}>
      <DrawerContent>
        <DrawerHeader className="p-0 mb-6">
          <DrawerTitle>Connect Wallet</DrawerTitle>
        </DrawerHeader>

        <div className="flex flex-col gap-y-0.5">
          {Object.entries(supportedWallets).map(([wallet]) => (
            <Button
              className="p-3"
              key={wallet}
              onClick={() => handleConnect(wallet as WalletEnum)}>
              <div className="flex items-center gap-x-2.5 w-full">
                {WALLET_ICON[wallet as keyof typeof WALLET_ICON]}
                <span>{WALLET_NAME[wallet as WalletEnum]}</span>
              </div>
            </Button>
          ))}
        </div>

        <div className="flex items-center gap-x-4 flex-1">
          <div className="h-[1px] w-[52px] bg-foreground opacity-10" />
          <span className="opacity-30 text-center text-nowrap">
            More wallet coming soon
          </span>
          <div className="h-[1px] w-[52px] bg-foreground opacity-10" />
        </div>

        <DrawerFooter className="p-0">
          <span className="text-xs opacity-60">
            By connecting a wallet, you agree to Pebble’s{' '}
            <a className="underline">Terms and Conditions</a> and consent to its{' '}
            <a className="underline">Privacy Policy</a>
          </span>
        </DrawerFooter>
      </DrawerContent>
    </Drawer>
  )
}
