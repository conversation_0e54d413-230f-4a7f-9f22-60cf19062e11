import { useMemo, useState } from 'react'
import { cn } from '@/lib/utils'
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  type SortingState,
  type ColumnDef,
  type Row,
  type OnChangeFn
} from '@tanstack/react-table'
import {
  flexRender,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { ChevronDownIcon } from '@/assets/icons'
import { Skeleton } from '@/components/ui/skeleton'

interface DataTableProps<TData, TValue> {
  ref?: React.ForwardedRef<HTMLTableElement>
  // table data
  data: TData[]
  // table columns
  columns: ColumnDef<TData, TValue>[]
  // loading state
  isLoading: boolean
  /* table size
   * @default lg
   * sm: for assets list table
   * md: for history table
   * lg: for default table
   */
  size?: 'sm' | 'md' | 'lg'
  className?: string
  classNames?: {
    headerRow?: string
    bodyRow?: string
    sortableHeaderCell?: string
    headerCell?: string
    contentCell?: string
    skeletonCell?: string
  }
  onPressRow?: (row: Row<TData>) => void
  // server side sorting
  serverSideSorting?: boolean
  sorting?: SortingState
  onSortingChange?: OnChangeFn<SortingState>
}

export function DataTable<TData, TValue>({
  ref,
  data,
  columns,
  isLoading,
  size = 'lg',
  className,
  serverSideSorting = false,
  onSortingChange,
  classNames: classNamesProp,
  sorting: externalSorting,
  onPressRow
}: DataTableProps<TData, TValue>) {
  const [internalSorting, setInternalSorting] = useState<SortingState>([])

  const sorting = externalSorting || internalSorting
  const setSorting = onSortingChange || setInternalSorting

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: serverSideSorting ? undefined : getSortedRowModel(),
    onSortingChange: setSorting,
    state: { sorting },
    enableSortingRemoval: false,
    manualSorting: serverSideSorting
  })

  const classNames = useMemo(
    () => ({
      headerRow: cn(
        'hover:bg-transparent border-none',
        classNamesProp?.headerRow
      ),
      bodyRow: classNamesProp?.bodyRow,
      sortableHeaderCell: cn(
        'flex h-full cursor-pointer items-center gap-1.5 select-none opacity-60 hover:opacity-80',
        classNamesProp?.sortableHeaderCell
      ),
      headerCell: cn(
        'relative select-none font-normal text-xs',
        {
          'h-[54px] px-6': size === 'lg',
          'h-[40px] px-6': size === 'md',
          'h-[15px] px-6 py-1.5': size === 'sm'
        },
        classNamesProp?.headerCell
      ),
      contentCell: cn(
        'truncate text-sm',
        {
          'text-sm px-6 py-6': size === 'lg',
          'text-sm px-6 py-3': size === 'md',
          'text-xs px-6 py-1.5': size === 'sm'
        },
        classNamesProp?.contentCell
      ),
      skeletonCell: cn(
        'w-full',
        {
          'h-[30px]': size === 'sm',
          'h-[45px]': size === 'md',
          'h-[54px]': size === 'lg'
        },
        classNamesProp?.skeletonCell
      )
    }),
    [size, classNamesProp]
  )

  return (
    <Table ref={ref} className={className}>
      <TableHeader>
        {table.getHeaderGroups().map((headerGroup) => (
          <TableRow key={headerGroup.id} className={classNames.headerRow}>
            {headerGroup.headers.map((header) => {
              return (
                <TableHead
                  key={header.id}
                  className={classNames.headerCell}
                  aria-sort={
                    header.column.getIsSorted() === 'asc'
                      ? 'ascending'
                      : header.column.getIsSorted() === 'desc'
                        ? 'descending'
                        : 'none'
                  }
                  {...{
                    colSpan: header.colSpan,
                    style: {
                      width: header.getSize()
                    }
                  }}>
                  {header.isPlaceholder ? null : (
                    <div
                      className={cn(
                        header.column.getCanSort() &&
                          classNames.sortableHeaderCell,
                        header.column.getIsSorted() && 'opacity-100'
                      )}
                      onClick={header.column.getToggleSortingHandler()}
                      onKeyDown={(e) => {
                        // Enhanced keyboard handling for sorting
                        if (
                          header.column.getCanSort() &&
                          (e.key === 'Enter' || e.key === ' ')
                        ) {
                          e.preventDefault()
                          header.column.getToggleSortingHandler()?.(e)
                        }
                      }}
                      tabIndex={header.column.getCanSort() ? 0 : undefined}>
                      <span className="truncate">
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </span>
                      {header.column.getCanSort() && (
                        <ChevronDownIcon
                          className={cn(
                            'shrink-0 transition-transform duration-200',
                            {
                              'rotate-180':
                                header.column.getIsSorted() === 'asc'
                            }
                          )}
                          width={5}
                          height={5}
                          aria-hidden="true"
                        />
                      )}
                    </div>
                  )}
                </TableHead>
              )
            })}
          </TableRow>
        ))}
      </TableHeader>
      <TableBody>
        {isLoading ? (
          Array.from({ length: 1 }).map((_, index) => (
            <TableRow key={index}>
              {columns.map((column) => (
                <TableCell key={column.id}>
                  <Skeleton className={classNames.skeletonCell} />
                </TableCell>
              ))}
            </TableRow>
          ))
        ) : table.getRowModel().rows?.length ? (
          table.getRowModel().rows.map((row) => (
            <TableRow
              key={row.id}
              onClick={() => onPressRow?.(row)}
              className={cn(
                !!onPressRow && 'cursor-pointer',
                classNames.bodyRow
              )}>
              {row.getVisibleCells().map((cell) => (
                <TableCell key={cell.id} className={classNames.contentCell}>
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell
              colSpan={columns.length}
              className={cn(classNames.contentCell, 'text-center')}>
              No results.
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  )
}
