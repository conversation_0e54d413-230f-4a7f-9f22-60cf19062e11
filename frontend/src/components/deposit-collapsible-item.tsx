import { ChevronDownIcon } from '@/assets/icons'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger
} from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'
import { useState } from 'react'

interface DepositCollapsibleItemData {
  id: string
  label: string
  value: React.ReactNode
  details?: { id: string; label: string; value: React.ReactNode }[]
}

export const DepositCollapsibleItem: React.FC<{
  data: DepositCollapsibleItemData
}> = ({ data }) => {
  const [open, setOpen] = useState(false)
  return (
    <Collapsible open={open} onOpenChange={setOpen} className="border-none">
      <CollapsibleTrigger className="w-full">
        <div key={data.id} className="flex items-center w-full justify-between">
          <span className="opacity-60">{data.label}</span>
          <span className="flex items-center gap-x-1.5">
            {data.value}
            {!!data.details?.length && (
              <ChevronDownIcon
                className={cn(
                  'size-[10px] hover:opacity-60 cursor-pointer transition-all',
                  open ? 'rotate-180' : 'rotate-0'
                )}
              />
            )}
          </span>
        </div>
      </CollapsibleTrigger>
      {!!data.details?.length && (
        <CollapsibleContent className="p-3 bg-border-5 rounded-md mt-3 flex flex-col gap-y-1.5">
          {data.details?.map((detail) => (
            <div key={detail.id} className="flex items-center justify-between">
              <span className="opacity-60">{detail.label}</span>
              <span>{detail.value}</span>
            </div>
          ))}
        </CollapsibleContent>
      )}
    </Collapsible>
  )
}
