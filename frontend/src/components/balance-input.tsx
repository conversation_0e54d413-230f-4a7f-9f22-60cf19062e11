import { NumberInput } from '@/components/number-input'
import BigNumber from 'bignumber.js'
import { cn } from '@/lib/utils'
import { useTokenBalance } from '@/hooks/use-token-balance'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { useEffect, useMemo, useState, type ReactNode } from 'react'
import { Avatar } from '@/components/ui/avatar'
import { formatNumberWithSuperscriptZeros } from '@/lib/number'
import { SUI_COIN_TYPE, SUI_COIN_TYPE_LONG } from '@/lib/coin'
import { Slider } from './ui/slider'
import { AlertIcon } from '@/assets/icons'

const AMOUNT_SELECTION_TYPE = [25, 50, 75, 100]

export type Token = {
  icon: string
  symbol: string
  coinType: string
}

type BalanceInputProps = {
  title?: ReactNode
  value?: string
  price?: number
  available?: number
  balanceTitle?: string
  maxAmount?: number
  onChange?: (v?: string) => void
  className?: string
  tokens: {
    token0: Token
    token1?: Token
  }
  selectedToken?: Token
  onSelectedTokenChange?: (v: Token) => void
  showSlider?: boolean
}

export const BalanceInput: React.FC<BalanceInputProps> = ({
  title,
  value,
  price,
  available,
  balanceTitle,
  maxAmount,
  onChange,
  tokens,
  className,
  selectedToken,
  onSelectedTokenChange,
  showSlider = false
}) => {
  const [sliderValue, setSliderValue] = useState<number>(0)
  const { balance } = useTokenBalance(
    selectedToken ? selectedToken.coinType : tokens.token0.coinType
  )

  const isSuiBalance = useMemo(() => {
    return (
      !available &&
      (tokens.token0.coinType === SUI_COIN_TYPE ||
        tokens.token0.coinType === SUI_COIN_TYPE_LONG)
    )
  }, [available, tokens.token0.coinType])

  const max = useMemo(() => {
    return maxAmount ?? (available !== undefined ? available : (balance ?? 0))
  }, [maxAmount, available, balance])

  const onPressPercentageButton = (t: number) => {
    const isMax = t === 100
    let value = ''
    if (isMax) {
      if (isSuiBalance) {
        value = BigNumber(
          BigNumber(max).multipliedBy(99).dividedBy(100).toPrecision(4, 1)
        ).toString()
      } else {
        value = max.toString()
      }
    } else {
      value = BigNumber(
        BigNumber(max).multipliedBy(t).dividedBy(100).toPrecision(4, 1)
      ).toString()
    }
    onChange?.(value)
  }

  const formattedBalanceUsd = useMemo(() => {
    if (!price) return ''
    return formatNumberWithSuperscriptZeros(
      BigNumber(isNaN(Number(value)) ? 0 : value || 0)
        .multipliedBy(price)
        .toString()
    )
  }, [value, price])

  const balanceInsufficient = useMemo(() => {
    if (!maxAmount) return false
    return BigNumber(value ?? 0).isGreaterThan(balance)
  }, [maxAmount, value, balance])

  // const formattedAvailable = useMemo(() => {
  //   return formatNumberWithSuperscriptZeros(available ?? 0)
  // }, [available])

  useEffect(() => {
    const percentage = BigNumber(value ?? 0)
      .dividedBy(max)
      .toNumber()
    setSliderValue(percentage)
  }, [value, max])

  return (
    <div className="flex flex-col gap-y-1.5">
      <div
        className={cn(
          'flex flex-col overflow-hidden rounded-xl border border-border-8',
          'bg-border-2',
          className
        )}>
        <div className="relative flex flex-col gap-y-3 p-3">
          <div className="flex items-center justify-between opacity-60 text-xs">
            <span>{title ?? 'Deposit asset'}</span>
            <span>
              {balanceTitle ?? 'Balance'}:{' '}
              {available !== undefined ? available : balance}
            </span>
          </div>

          <div className="flex items-center justify-between">
            {tokens.token1 ? (
              <Select
                value={selectedToken?.coinType}
                onValueChange={(v) => {
                  onSelectedTokenChange?.(
                    [tokens.token0, tokens.token1].find(
                      (c) => c?.coinType === v
                    ) ?? tokens.token0
                  )
                }}>
                <SelectTrigger>
                  <SelectValue>
                    <Avatar
                      size={24}
                      src={selectedToken?.icon ?? ''}
                      alt={selectedToken?.symbol ?? ''}
                      fallback={selectedToken?.symbol ?? ''}
                    />
                    {selectedToken?.symbol}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {[tokens.token0, tokens.token1]
                    .filter((coin) => coin.coinType !== selectedToken?.coinType)
                    .map((coin) => (
                      <SelectItem
                        className="w-[165px] text-2xl gap-x-1.5"
                        key={coin.coinType}
                        value={coin.coinType}>
                        <Avatar
                          size={24}
                          src={coin.icon}
                          alt={coin.symbol}
                          fallback={coin.symbol}
                        />
                        {coin.symbol}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            ) : (
              <div className="flex items-center gap-1.5">
                <Avatar
                  size={24}
                  src={tokens.token0.icon}
                  alt={tokens.token0.symbol}
                  fallback={tokens.token0.symbol}
                />
                {tokens.token0.symbol}
              </div>
            )}
            <div className="flex flex-col items-end">
              <NumberInput
                value={value}
                onInputChange={onChange}
                inputClassName={balanceInsufficient ? 'text-red' : ''}
              />
            </div>
          </div>
          <div className="text-xs opacity-40 text-end">
            ~${formattedBalanceUsd}
          </div>
          {showSlider && (
            <div className="p-3">
              <Slider
                value={[sliderValue]}
                onValueChange={(value) => {
                  setSliderValue(value[0])
                  const amount =
                    value[0] === 1
                      ? max.toString()
                      : BigNumber(max).multipliedBy(value[0]).toFixed(6)
                  onChange?.(amount)
                }}
                min={0}
                max={1}
                step={0.1}
                showValue={false}
                className="w-full"
              />
            </div>
          )}
        </div>
        <div className="grid w-full grid-cols-4 items-center justify-between border-t border-t-border-5">
          {AMOUNT_SELECTION_TYPE.map((t) => (
            <Button
              key={t}
              className="rounded-none h-8! active:scale-100"
              onClick={() => onPressPercentageButton(t)}
              variant="ghost">
              <span className="text-xs opacity-60 font-normal">
                {t === AMOUNT_SELECTION_TYPE[AMOUNT_SELECTION_TYPE.length - 1]
                  ? 'Max'
                  : `${t}%`}
              </span>
            </Button>
          ))}
        </div>
      </div>
      {balanceInsufficient && (
        <div className="text-xs flex items-center gap-x-1.5 text-red">
          <AlertIcon className="size-[17.87px]" />
          Not enough balance
        </div>
      )}
    </div>
  )
}
