import { lazy } from 'react'
import { Navigate } from 'react-router'
import { DashboardIcon } from '@/assets/icons'
import MarginTradeDashboard from '@/pages/margin-trade/components/dashboard'

const Market = lazy(() => import('@/pages/market'))
const MarketDetail = lazy(() => import('@/pages/market/detail'))
const MarginTrade = lazy(() => import('@/pages/margin-trade'))
const Leverage = lazy(() => import('@/pages/leverage'))
const LeverageDetail = lazy(() => import('@/pages/leverage/detail'))
const LpPools = lazy(() => import('@/pages/lp-pools'))
const Dashboard = lazy(() => import('@/pages/dashboard'))
const Portfolio = lazy(() => import('@/pages/portfolio'))

export interface RouteConfig {
  path: string
  label: React.ReactNode
  element?: React.ReactNode
  position: 'left' | 'right'
}

export const routes: RouteConfig[] = [
  {
    path: '/market',
    label: 'Market',
    position: 'left'
  },
  {
    path: '/multiply',
    label: 'Multiply',
    position: 'left'
  },
  {
    path: '/margin-trade',
    label: 'Margin Trade',
    position: 'left'
  },
  {
    path: '/lp-pools',
    label: 'LP Pools',
    position: 'left'
  },
  {
    path: '/portfolio',
    label: (
      <span className="flex items-center gap-x-2.5">
        <DashboardIcon />
        Portfolio
      </span>
    ),
    position: 'right'
  }
]

export const getRouteChildren = (path: string) => {
  switch (path) {
    case '/market':
      return [
        { index: true, element: <Market /> },
        { path: ':marketType/:tokenAddress', element: <MarketDetail /> },
        { path: 'dashboard/:id', element: <Dashboard /> }
      ]
    case '/multiply':
      return [
        { index: true, element: <Leverage /> },
        { path: ':id', element: <LeverageDetail /> },
        { path: 'dashboard/:id', element: <Dashboard /> }
      ]
    case '/margin-trade':
      return [
        { index: true, element: <MarginTrade /> },
        {
          path: 'dashboard/:id/:leftToken/:rightToken',
          element: <MarginTradeDashboard />
        }
      ]
    default:
      return undefined
  }
}

export const getRouteElement = (path: string) => {
  switch (path) {
    case '/margin-trade':
      return undefined
    case '/lp-pools':
      return <LpPools />
    case '/portfolio':
      return <Portfolio />
    case '/market':
      return undefined
    case '/multiply':
      return undefined
    default:
      return <Navigate to="/market" />
  }
}
