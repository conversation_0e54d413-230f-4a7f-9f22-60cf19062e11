import { OkxWalletIcon, PhantomIcon } from '@/assets/icons'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl } from '@/lib/assets'

export enum WalletEnum {
  OKX = 'OKX Wallet',
  Slush = 'Slush',
  Phantom = 'Phantom'
}

export const WALLET_NAME = {
  [WalletEnum.OKX]: 'OKX Wallet',
  [WalletEnum.Slush]: 'Slush Wallet',
  [WalletEnum.Phantom]: 'Phantom Wallet'
} as const

export const WALLET_WEB_URL = {
  [WalletEnum.OKX]: 'https://www.okx.com/web3/wallet',
  [WalletEnum.Slush]: 'https://slush.app/',
  [WalletEnum.Phantom]: 'https://phantom.app/'
} as const

export const WALLET_EXPLORER_URL = {
  [WalletEnum.OKX]: 'https://web3.okx.com/explorer/sui/tx',
  [WalletEnum.Slush]: 'https://suiscan.xyz/testnet/tx',
  [WalletEnum.Phantom]: 'https://suiscan.xyz/mainnet/tx'
} as const

export const WALLET_ICON = {
  [WalletEnum.OKX]: <OkxWalletIcon />,
  [WalletEnum.Slush]: (
    <Avatar
      size={24}
      alt="slush-logo"
      fallback="Slush"
      src={getIconUrl('slush')}
    />
  ),
  [WalletEnum.Phantom]: <PhantomIcon />
} as const
