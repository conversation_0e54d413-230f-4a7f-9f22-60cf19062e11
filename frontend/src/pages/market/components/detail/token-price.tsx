import { BaseChart } from '@/components/chart/base-chart'
import { MarketOverviewMenu } from '../../types'
import { RangeSwitcher } from '@/components/chart/range-switcher'
import { ChartRangeEnum, PriceFormat } from '@/components/chart/types'
import { useMemo, useState } from 'react'
import type { UTCTimestamp } from 'lightweight-charts'
import { calculateTimeRange } from '@/lib/utils'
import { getShortName } from '@/utils/data'
import { useGetTokenPriceHistoryChart } from '@/queries/chart'

export function MarketDetailTokenPrice({
  marketType,
  tokenAddress
}: {
  marketType?: string
  tokenAddress?: string
}) {
  const tokenSymbol = getShortName(tokenAddress || '')
  const [currentRange, setCurrentRange] = useState(ChartRangeEnum.Day7)

  const timeRange = useMemo(
    () => calculateTimeRange(currentRange),
    [currentRange]
  )

  const { data: tokenPriceData } = useGetTokenPriceHistoryChart({
    from: timeRange.from,
    to: timeRange.to,
    marketType: marketType || '',
    tokenAddress: tokenAddress || ''
  })

  const data = useMemo(() => {
    return tokenPriceData?.map((v) => ({
      time: (v.t / 1000) as UTCTimestamp,
      value: Number(v.tokenPrice.toFixed(2))
    }))
  }, [tokenPriceData])

  return (
    <div id={MarketOverviewMenu.TokenPrice} className="flex flex-col">
      <div className="px-3 pb-3 rounded-xl border border-border-8 flex flex-col bg-overview-gradient">
        <div className="px-3 py-4.5 flex w-full gap-2 items-center justify-between border-b border-border-8 leading-none">
          {tokenSymbol} Price
          <RangeSwitcher
            value={currentRange}
            onRangeChange={(value) => {
              setCurrentRange(value)
            }}
          />
        </div>
        <BaseChart
          seriesData={[
            {
              title: tokenSymbol,
              data: data ?? [],
              options: {
                topColor: '#FFFFFF4D',
                bottomColor: '#235B8800'
              }
            }
          ]}
          priceFormat={PriceFormat.Usd}
          className="h-[380px]"
        />
      </div>
    </div>
  )
}
