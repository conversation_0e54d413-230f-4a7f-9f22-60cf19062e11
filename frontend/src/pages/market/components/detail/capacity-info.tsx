import { NumberCell } from '@/components/number-cell'
import { cn } from '@/lib/utils'
import type { FC } from 'react'

interface CapacityInfoItem {
  label: string
  value: number
  usdValue: number
}

interface CapacityInfoData {
  current: CapacityInfoItem
  available: CapacityInfoItem
  max: CapacityInfoItem
  token: string
}

interface CapacityInfoProps {
  data: CapacityInfoData
  className?: string
}

export const CapacityInfo: FC<CapacityInfoProps> = ({ data, className }) => {
  return (
    <div
      className={cn(
        'flex flex-col rounded-xl border border-border-8 p-6 text-xs gap-1.5 bg-overview-gradient',
        className
      )}>
      <div className="w-full h-3 flex gap-1.5 mb-6 mt-3">
        <div
          className="rounded bg-[linear-gradient(90deg,_#E5BC5B_0%,_#235B88_100%)]"
          style={{ width: `${(data.current.value * 100) / data.max.value}%` }}
        />
        <div className="rounded bg-[#E7E4DA]/40 flex-1" />
        <div className="rounded bg-primary w-1.5" />
      </div>
      <div className="flex justify-between">
        <div className="flex items-center gap-1">
          <div className="rounded-full size-[13px] bg-[#008BD5]" />{' '}
          {data.current.label}
        </div>
        <div>
          <NumberCell
            className="opacity-60"
            value={data.current.value}
            suffix={` ${data.token}`}
          />{' '}
          {/* <NumberCell
            className="opacity-60"
            value={data.current.usdValue}
            prefix="$"
          /> */}
        </div>
      </div>

      <div className="flex justify-between">
        <div className="flex items-center gap-1">
          <div className="rounded-full size-[13px] bg-[#E3E0D766]" />{' '}
          {data.available.label}
        </div>
        <div>
          <NumberCell
            className="opacity-60"
            value={data.available.value}
            suffix={` ${data.token}`}
          />{' '}
          {/* <NumberCell
            className="opacity-60"
            value={data.available.usdValue}
            prefix="$"
          /> */}
        </div>
      </div>

      <div className="flex justify-between">
        <div className="flex items-center gap-1">
          <div className="rounded-full size-[13px] bg-primary" />{' '}
          {data.max.label}
        </div>
        <div>
          <NumberCell
            className="opacity-60"
            value={data.max.value}
            suffix={` ${data.token}`}
          />{' '}
          {/* <NumberCell
            className="opacity-60"
            value={data.max.usdValue}
            prefix="$"
          /> */}
        </div>
      </div>
    </div>
  )
}
