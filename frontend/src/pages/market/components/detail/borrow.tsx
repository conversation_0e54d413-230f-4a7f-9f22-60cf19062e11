import { CURRENCY_USD, MarketOverviewMenu } from '../../types'
import { useState, useMemo } from 'react'
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { RangeSwitcher } from '@/components/chart/range-switcher'
import { BaseChart } from '@/components/chart/base-chart'
import { CapacityInfo } from './capacity-info'
import { OverviewBar } from '@/components/overview-bar'
import {
  useGetBorrowHistoryChart,
  useGetBorrowAPYHistoryChart
} from '@/queries/chart'
import { ChartRangeEnum, PriceFormat } from '@/components/chart/types'
import { calculateTimeRange } from '@/lib/utils'
import type { UTCTimestamp } from 'lightweight-charts'
import { formatUnits } from '@/lib/units'
import { getShortName } from '@/utils/data'
import TokenSwitcher from './token-switcher'
import BigNumber from 'bignumber.js'
import type { IMarketInfo } from '@/types/market'

export function MarketDetailBorrow({
  marketType,
  tokenAddress,
  tokenInfo
}: {
  marketType?: string
  tokenAddress?: string
  tokenInfo?: IMarketInfo | null
}) {
  const tokenSymbol = getShortName(tokenAddress || '')
  const currencies = [CURRENCY_USD, tokenSymbol]
  const [selectedCurrency, setSelectedCurrency] = useState(currencies[0])
  const [currentTab, setCurrentTab] = useState('0')
  const [currentRange, setCurrentRange] = useState(ChartRangeEnum.Day7)

  const timeRange = useMemo(
    () => calculateTimeRange(currentRange),
    [currentRange]
  )

  const params = useMemo(
    () => ({
      from: timeRange.from,
      to: timeRange.to,
      marketType: marketType || '',
      tokenAddress: tokenAddress || ''
    }),
    [timeRange, marketType, tokenAddress]
  )

  const { data: borrowHistoryData } = useGetBorrowHistoryChart(params)
  const { data: borrowAPYData } = useGetBorrowAPYHistoryChart(params)

  const data = useMemo(() => {
    return currentTab === '0'
      ? selectedCurrency === CURRENCY_USD
        ? borrowHistoryData
            ?.filter((v) => v.tokenPrice !== null)
            ?.map((v) => {
              const value = Number(
                BigNumber(
                  formatUnits(
                    v.totalBorrow,
                    Number(tokenInfo?.tokenInfo.decimals)
                  )
                )
                  .multipliedBy(v.tokenPrice)
                  .toNumber()
                  .toFixed(2)
              )
              return {
                time: (v.t / 1000) as UTCTimestamp,
                value: value
              }
            })
        : borrowHistoryData?.map((v) => ({
            time: (v.t / 1000) as UTCTimestamp,
            value: Number(
              formatUnits(v.totalBorrow, Number(tokenInfo?.tokenInfo.decimals))
            )
          }))
      : borrowAPYData?.map((v) => ({
          time: (v.t / 1000) as UTCTimestamp,
          value: Number(BigNumber(v.borrowAPY).multipliedBy(100).toFixed(2))
        }))
  }, [
    borrowHistoryData,
    borrowAPYData,
    currentTab,
    tokenInfo,
    selectedCurrency
  ])

  const totalBorrow = BigNumber(
    formatUnits(
      tokenInfo?.totalBorrow || 0n,
      Number(tokenInfo?.tokenInfo.decimals)
    )
  )
  const totalBorrowUSD = totalBorrow
    .multipliedBy(tokenInfo?.tokenInfo.price || 0)
    .toNumber()

  const borrowCap = BigNumber(
    formatUnits(
      tokenInfo?.borrowCap || 0n,
      Number(tokenInfo?.tokenInfo.decimals)
    )
  )
  const borrowCapUSD = borrowCap
    .multipliedBy(tokenInfo?.tokenInfo.price || 0)
    .toNumber()

  const borrowAvailable = borrowCap.minus(totalBorrow)
  const borrowAvailableUSD = borrowAvailable
    .multipliedBy(tokenInfo?.tokenInfo.price || 0)
    .toNumber()

  const borrowInfo = {
    current: {
      label: 'Total Borrowed',
      value: totalBorrow.toNumber(),
      usdValue: totalBorrowUSD
    },
    available: {
      label: 'Liquidity Available',
      value: borrowAvailable.toNumber(),
      usdValue: borrowAvailableUSD
    },
    max: {
      label: 'Borrow Cap',
      value: borrowCap.toNumber(),
      usdValue: borrowCapUSD
    },
    token: tokenInfo?.tokenInfo.symbol || ''
  }

  const borrowData1 = [
    {
      label: 'Utilization Ratio',
      value: `${tokenInfo ? (Number(tokenInfo.utilization) * 100).toFixed(2) : 0}%`
    },
    {
      label: 'Borrow Factor',
      value: `${tokenInfo ? tokenInfo.borrowFactor.toFixed(2) : 0}`
    },
    {
      label: 'Current Borrow APY',
      value: `${tokenInfo ? (Number(tokenInfo.borrowAPY) * 100).toFixed(2) : 0}%`,
      valueClassName: 'text-primary'
    }
  ]
  const borrowData2 = [
    {
      label: '30D Avg.APY',
      value: `${(tokenInfo?.avgBorrowAPY30D ?? 0).toFixed(2)}%`,
      valueClassName: 'text-primary'
    },
    {
      label: '90D Avg.APY',
      value: `${(tokenInfo?.avgBorrowAPY90D ?? 0).toFixed(2)}%`,
      valueClassName: 'text-primary'
    },
    {
      label: '180D Avg.APY',
      value: `${(tokenInfo?.avgBorrowAPY180D ?? 0).toFixed(2)}%`,
      valueClassName: 'text-primary'
    }
  ]

  return (
    <div id={MarketOverviewMenu.BorrowOverview} className="flex flex-col gap-6">
      <div className="pb-3 rounded-xl border border-border-8 flex flex-col bg-overview-gradient">
        <Tabs
          value={currentTab}
          orientation="vertical"
          onValueChange={(value) => {
            setCurrentTab(value)
          }}>
          <TabsList
            aria-label="collateral tabs"
            className="flex justify-between items-center px-3">
            <div className="flex gap-3">
              <TabsTrigger value="0" className="px-1 py-4.5">
                Total Borrowed
              </TabsTrigger>
              <TabsTrigger value="1" className="px-1 flex gap-2.5">
                <span>Borrow APY</span>
                <span className="text-primary rounded-sm bg-primary/10 p-1.5">
                  {tokenInfo
                    ? (Number(tokenInfo.borrowAPY) * 100).toFixed(2)
                    : 0}
                  % APY
                </span>
              </TabsTrigger>
            </div>
            <div className="flex gap-6">
              <TokenSwitcher
                tokens={currencies}
                value={selectedCurrency}
                onValueChange={(value) => {
                  setSelectedCurrency(value)
                }}
              />
              <RangeSwitcher
                value={currentRange}
                onRangeChange={(value) => {
                  setCurrentRange(value)
                }}
              />
            </div>
          </TabsList>
        </Tabs>
        <div className="px-3">
          <BaseChart
            seriesData={[
              {
                title: currentTab === '0' ? 'Total Borrowed' : 'Borrow APY',
                data: data || [],
                options: {
                  topColor: '#E5BC5B4D',
                  bottomColor: '#235B8800'
                }
              }
            ]}
            priceFormat={
              currentTab === '1'
                ? PriceFormat.Percentage
                : selectedCurrency === CURRENCY_USD
                  ? PriceFormat.Usd
                  : PriceFormat.Number
            }
            className="h-[380px]"
          />
        </div>
      </div>
      <div className="flex gap-3">
        <CapacityInfo data={borrowInfo} className="flex-1" />
        <div className="flex-1 flex flex-col gap-3">
          <OverviewBar data={borrowData1} cellClassName="p-0" />
          <OverviewBar data={borrowData2} cellClassName="p-0" />
        </div>
      </div>
    </div>
  )
}
