import {
  BorrowIcon,
  InterestRateIcon,
  ReserveLimitIcon,
  SupplyIcon,
  TokenPriceIcon,
  UserIcon,
  UtilizationIcon
} from '@/assets/icons'
import { AnchorMenu } from '@/components/anchor-menu'
import { OverviewBar } from '@/components/overview-bar'
import { MarketOverviewMenu } from '../../types'
import { MarketDetailSupply } from './supply'
import { MarketDetailBorrow } from './borrow'
import { MarketDetailTokenPrice } from './token-price'
import { MarketDetailUtilizationRatio } from './utilization-ratio'
import { MarketDetailInterestRate } from './interest-rate'
import { MarketDetailReverseLimits } from './reverse-limits'
import { useMemo, type FC } from 'react'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl } from '@/lib/assets'
import useMarketTokenInfo from '../../hooks/use-market-token-info'
import { formatUnits } from '@/lib/units'
import BigNumber from 'bignumber.js'
import { NumberCell } from '@/components/number-cell'
import { getShortName } from '@/utils/data'

const menu = [
  {
    id: MarketOverviewMenu.GeneralStatus,
    label: 'General Status',
    icon: <UserIcon />
  },
  {
    id: MarketOverviewMenu.SupplyOverview,
    label: 'Supply Overview',
    icon: <SupplyIcon />
  },
  {
    id: MarketOverviewMenu.BorrowOverview,
    label: 'Borrow Overview',
    icon: <BorrowIcon />
  },
  {
    id: MarketOverviewMenu.UtilizationRatio,
    label: 'Utilization Ratio',
    icon: <UtilizationIcon />
  },
  {
    id: MarketOverviewMenu.InterestRateCurve,
    label: 'Interest Rate Curve',
    icon: <InterestRateIcon />
  },
  {
    id: MarketOverviewMenu.TokenPrice,
    label: 'Token Price',
    icon: <TokenPriceIcon />
  },
  {
    id: MarketOverviewMenu.ReserveLimit,
    label: 'Reserve Limit',
    icon: <ReserveLimitIcon />
  }
]
export const MarketDetailOverview: FC<{
  marketType?: string
  tokenAddress?: string
}> = ({ marketType, tokenAddress }) => {
  const marketName = getShortName(marketType || '')
  const { marketTokenInfo } = useMarketTokenInfo(marketName, tokenAddress || '')
  const utilizationRaw = useMemo(() => {
    if (!marketTokenInfo) return '0'
    return marketTokenInfo.utilization
  }, [marketTokenInfo])
  // const { supplyApy: supplyApyRatio, borrowApy: borrowApyRatio } =
  //   useMarketTokenApy(marketName, tokenAddress || '', Number(utilizationRaw))
  const totalSupplied = useMemo(() => {
    if (!marketTokenInfo) return '0'
    return BigNumber(
      formatUnits(
        marketTokenInfo.totalSupply,
        Number(marketTokenInfo.tokenInfo.decimals)
      )
    ).toString()
  }, [marketTokenInfo])
  const liqAvailable = useMemo(() => {
    if (!marketTokenInfo) return '0'
    return BigNumber(
      formatUnits(
        marketTokenInfo.liqAvailable,
        Number(marketTokenInfo.tokenInfo.decimals)
      )
    ).toString()
  }, [marketTokenInfo])

  const utilization = useMemo(() => {
    return BigNumber(utilizationRaw).multipliedBy(100).toFixed(2)
  }, [utilizationRaw])
  const supplyAPY = useMemo(() => {
    if (!marketTokenInfo) return null
    return BigNumber(marketTokenInfo.supplyAPY).multipliedBy(100).toFixed(2)
  }, [marketTokenInfo])
  const borrowAPY = useMemo(() => {
    if (!marketTokenInfo) return null
    return BigNumber(marketTokenInfo.borrowAPY).multipliedBy(100).toFixed(2)
  }, [marketTokenInfo])

  const overviewData = useMemo(
    () => [
      {
        icon: <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />,
        label: 'Supplied',
        value: (
          <NumberCell
            value={totalSupplied}
            suffix={` ${marketTokenInfo?.tokenInfo.symbol}`}
          />
        )
      },
      {
        icon: <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />,
        label: 'Liq. Available',
        value: (
          <NumberCell
            value={liqAvailable}
            suffix={` ${marketTokenInfo?.tokenInfo.symbol}`}
          />
        )
      },
      {
        label: 'Utilization',
        value: `${utilization}%`
      },
      {
        label: 'Supply APY',
        value: supplyAPY ? `${supplyAPY}%` : '-',
        valueClassName: 'text-green'
      },
      {
        label: 'Borrow APY',
        value: borrowAPY ? `${borrowAPY}%` : '-',
        valueClassName: 'text-primary'
      }
    ],
    [
      marketTokenInfo,
      totalSupplied,
      liqAvailable,
      utilization,
      supplyAPY,
      borrowAPY
    ]
  )
  return (
    <AnchorMenu
      menu={menu}
      defaultActiveMenuId={MarketOverviewMenu.GeneralStatus}
      offsetTop={84}
      className="mt-6"
      menuClassName="w-[200px] mr-3">
      <div className="flex-1 flex flex-col gap-6">
        <div
          id={MarketOverviewMenu.GeneralStatus}
          className="flex flex-col gap-3">
          <OverviewBar data={overviewData} />
        </div>
        <MarketDetailSupply
          marketType={marketType}
          tokenAddress={tokenAddress}
          tokenInfo={marketTokenInfo}
        />
        <MarketDetailBorrow
          marketType={marketType}
          tokenAddress={tokenAddress}
          tokenInfo={marketTokenInfo}
        />
        <MarketDetailUtilizationRatio
          marketType={marketType}
          tokenAddress={tokenAddress}
          utilization={utilization}
        />
        <MarketDetailInterestRate
          marketType={marketType}
          tokenAddress={tokenAddress}
          utilization={utilization}
        />
        <MarketDetailTokenPrice
          marketType={marketType}
          tokenAddress={tokenAddress}
        />
        <MarketDetailReverseLimits tokenAddress={tokenAddress} />
      </div>
    </AnchorMenu>
  )
}
