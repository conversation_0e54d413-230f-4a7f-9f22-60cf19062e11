import { MarketOverviewMenu } from '../../types'
import { OverviewBar } from '@/components/overview-bar'
import { getShortName } from '@/utils/data'

export function MarketDetailReverseLimits({
  tokenAddress
}: {
  tokenAddress?: string
}) {
  const tokenSymbol = getShortName(tokenAddress || '')
  const reverseData1 = [
    {
      label: 'Debt'
    },
    {
      label: 'Daily New Debt Limit',
      value: `150000000 ${tokenSymbol}`
    },
    {
      label: 'New Debt Today',
      value: `~3243.23 ${tokenSymbol}`
    },
    {
      label: 'Daily Cap Reset In',
      value: '143 min'
    }
  ]
  const reverseData2 = [
    {
      label: 'Cottlteral'
    },
    {
      label: 'Daily New Withdrawal Cap',
      value: `150000000 ${tokenSymbol}`
    },
    {
      label: 'Withdrawal Today',
      value: `~3243.23 ${tokenSymbol}`
    },
    {
      label: 'Daily Cap Reset In',
      value: '143 min'
    }
  ]
  return (
    <div
      id={MarketOverviewMenu.ReserveLimit}
      className="px-3 rounded-xl border border-border-8 flex flex-col bg-overview-gradient">
      <div className="px-3 py-4.5 border-b border-border-8 leading-none">
        Reserve Limit
      </div>
      <OverviewBar data={reverseData1} className="my-3" cellClassName="p-0" />
      <OverviewBar data={reverseData2} className="mb-3" cellClassName="p-0" />
    </div>
  )
}
