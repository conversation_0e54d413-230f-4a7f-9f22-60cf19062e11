import { MarketOverviewMenu } from '../../types'
import {
  BaseChartType,
  PriceFormat,
  TimeFormat
} from '@/components/chart/types'
import { useMemo } from 'react'
import type { UTCTimestamp } from 'lightweight-charts'
import { BaseChart, type ChartData } from '@/components/chart/base-chart'
import { useMarket } from '@/hooks/use-market'
import { convertAprToApy, getShortName } from '@/utils/data'
import { Decimal } from '@pebble-protocol/pebble-sdk'

export function MarketDetailInterestRate({
  marketType,
  tokenAddress,
  utilization
}: {
  marketType?: string
  tokenAddress?: string
  utilization?: string
}) {
  const { data: marketsData } = useMarket()
  const marketName = getShortName(marketType || '')
  const market = useMemo(() => {
    return marketsData?.find((v) => v.marketName === marketName)?.market
  }, [marketsData, marketName])
  const repayFeeRate = useMemo(() => {
    return market
      ?.assets()
      .find((v) => v.coinType === tokenAddress)
      ?.borrowSetting().repayFeeRate
  }, [market, tokenAddress])
  const assetConfiguration = useMemo(() => {
    return market?.getAssetConfiguration(tokenAddress ?? '')
  }, [market, tokenAddress])
  const utilizations = Array.from({ length: 201 }, (_, i) => i * 0.005)
  const data = useMemo(() => {
    const supplyData: ChartData[] = []
    const borrowData: ChartData[] = []
    if (!!assetConfiguration && !!repayFeeRate) {
      utilizations.forEach((v) => {
        const depositInterestRate = assetConfiguration.depositInterestRate(
          Decimal.fromNumber(v),
          repayFeeRate
        )
        const supplyApr = depositInterestRate
          ? Decimal.fromNumber(365 * 24 * 3600)
              .mul(depositInterestRate)
              .asNumber()
          : 0
        supplyData.push({
          time: v as UTCTimestamp,
          value: convertAprToApy(supplyApr) * 100
        })
        const borrowInterestRate = assetConfiguration.borrowInterestRate(
          Decimal.fromNumber(v)
        )
        const borrowApr = borrowInterestRate
          ? Decimal.fromNumber(365 * 24 * 3600)
              .mul(borrowInterestRate)
              .asNumber()
          : 0
        borrowData.push({
          time: v as UTCTimestamp,
          value: convertAprToApy(borrowApr) * 100
        })
      })
    }
    return {
      supplyData,
      borrowData
    }
  }, [assetConfiguration, repayFeeRate, utilizations])

  return (
    <div id={MarketOverviewMenu.InterestRateCurve} className="flex flex-col">
      <div className="px-3 pb-3 rounded-xl border border-border-8 flex flex-col bg-overview-gradient">
        <div className="px-3 py-4.5 flex w-full items-center border-b border-border-8 leading-none">
          Interest Rate Model
        </div>
        <BaseChart
          type={BaseChartType.Line}
          timeFormat={TimeFormat.Percentage}
          priceFormat={PriceFormat.Percentage}
          toolTipTimePrefix="Utilization: "
          seriesData={[
            {
              title: 'Supply APY',
              data: data.supplyData,
              options: {
                lineColor: '#E5BC5B'
              },
              priceLineDisabled: true,
              vertLine: {
                time: Number(
                  Number(Number(utilization) / 100).toFixed(2)
                ) as UTCTimestamp
              }
            },
            {
              title: 'Borrow APY',
              data: data.borrowData,
              options: {
                lineColor: '#40FF9F'
              },
              priceLineDisabled: true
            }
          ]}
          className="h-[380px]"
        />
      </div>
    </div>
  )
}
