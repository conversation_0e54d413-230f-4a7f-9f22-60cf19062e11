import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'

const TokenSwitcher = ({
  tokens,
  value,
  onValueChange
}: {
  tokens: string[]
  value: string
  onValueChange: (value: string) => void
}) => {
  return (
    <ToggleGroup type="single" value={value} onValueChange={onValueChange}>
      {tokens.map((token) => (
        <ToggleGroupItem key={token} value={token}>
          {token}
        </ToggleGroupItem>
      ))}
    </ToggleGroup>
  )
}

export default TokenSwitcher
