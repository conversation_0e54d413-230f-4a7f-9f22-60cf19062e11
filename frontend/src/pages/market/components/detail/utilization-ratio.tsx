import { BaseChart } from '@/components/chart/base-chart'
import { MarketOverviewMenu } from '../../types'
import { RangeSwitcher } from '@/components/chart/range-switcher'
import { BaseChartType } from '@/components/chart/types'
import { useState, useMemo } from 'react'
import { useGetUtilizationHistoryChart } from '@/queries/chart'
import { ChartRangeEnum, PriceFormat } from '@/components/chart/types'
import { calculateTimeRange } from '@/lib/utils'
import type { UTCTimestamp } from 'lightweight-charts'
import BigNumber from 'bignumber.js'

export function MarketDetailUtilizationRatio({
  marketType,
  tokenAddress,
  utilization
}: {
  marketType?: string
  tokenAddress?: string
  utilization?: string
}) {
  const [currentRange, setCurrentRange] = useState(ChartRangeEnum.Day7)

  const timeRange = useMemo(
    () => calculateTimeRange(currentRange),
    [currentRange]
  )

  const params = useMemo(
    () => ({
      from: timeRange.from,
      to: timeRange.to,
      marketType: marketType || '',
      tokenAddress: tokenAddress || ''
    }),
    [timeRange, marketType, tokenAddress]
  )

  const { data: utilizationData } = useGetUtilizationHistoryChart(params)

  const data = useMemo(() => {
    return utilizationData?.map((v) => ({
      time: (v.t / 1000) as UTCTimestamp,
      value: Number(BigNumber(v.utilization).multipliedBy(100).toFixed(4))
    }))
  }, [utilizationData])

  return (
    <div id={MarketOverviewMenu.UtilizationRatio} className="flex flex-col">
      <div className="px-3 pb-3 rounded-xl border border-border-8 flex flex-col bg-overview-gradient">
        <div className="px-3 py-4.5 flex w-full gap-2 items-center justify-between border-b border-border-8 leading-none">
          Historical Utilization Ratio
          <RangeSwitcher
            value={currentRange}
            onRangeChange={(value) => {
              setCurrentRange(value)
            }}
          />
        </div>
        <BaseChart
          type={BaseChartType.Line}
          seriesData={[
            {
              title: 'Utilization Ratio',
              data: data || [],
              priceLine: {
                value: Number(utilization),
                title: `Current Utilization: ${utilization}%`
              }
            }
          ]}
          priceFormat={PriceFormat.Percentage}
          className="h-[380px]"
        />
      </div>
    </div>
  )
}
