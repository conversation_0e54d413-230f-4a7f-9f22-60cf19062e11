import { useEffect, useState } from 'react'
import { mockLeverageDetailDashboardTransactionHistoryData } from '@/pages/leverage/mock'
import { DataTable } from '@/components/data-table'
import { useTransactionHistoryColumns } from '@/pages/dashboard/hooks/use-transaction-history-columns'

export function MarketDetailHistory() {
  const { columns } = useTransactionHistoryColumns()

  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<
    typeof mockLeverageDetailDashboardTransactionHistoryData
  >([])

  useEffect(() => {
    setLoading(true)
    const timer = setTimeout(() => {
      setData(mockLeverageDetailDashboardTransactionHistoryData)
      setLoading(false)
    }, 0)
    return () => clearTimeout(timer)
  }, [])

  return (
    <DataTable
      data={data}
      columns={columns}
      isLoading={loading}
      size="md"
      classNames={{
        headerCell: 'px-3',
        contentCell: 'px-3'
      }}
    />
  )
}
