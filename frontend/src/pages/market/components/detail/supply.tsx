import { BaseChart } from '@/components/chart/base-chart'
import { CURRENCY_USD, MarketOverviewMenu } from '../../types'
import { useMemo, useState } from 'react'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { RangeSwitcher } from '@/components/chart/range-switcher'
import { OverviewBar } from '@/components/overview-bar'
import { CapacityInfo } from './capacity-info'
import TokenSwitcher from './token-switcher'
import {
  useGetSupplyHistoryChart,
  useGetSupplyAPYHistoryChart
} from '@/queries/chart'
import { type FC } from 'react'
import { ChartRangeEnum, PriceFormat } from '@/components/chart/types'
import { calculateTimeRange } from '@/lib/utils'
import type { UTCTimestamp } from 'lightweight-charts'
import { getShortName } from '@/utils/data'
import { formatUnits } from '@/lib/units'
import BigNumber from 'bignumber.js'
import type { IMarketInfo } from '@/types/market'
import { useGetAllMarketConfig } from '@/queries/config'

export const MarketDetailSupply: FC<{
  marketType?: string
  tokenAddress?: string
  tokenInfo?: IMarketInfo | null
}> = ({ marketType, tokenAddress, tokenInfo }) => {
  const tokenSymbol = getShortName(tokenAddress || '')
  const currencies = [CURRENCY_USD, tokenSymbol]
  const [selectedCurrency, setSelectedCurrency] = useState(currencies[0])
  const [currentTab, setCurrentTab] = useState('0')
  const [currentRange, setCurrentRange] = useState(ChartRangeEnum.Day7)

  const timeRange = useMemo(
    () => calculateTimeRange(currentRange),
    [currentRange]
  )

  const params = useMemo(() => {
    return {
      from: timeRange.from,
      to: timeRange.to,
      marketType: marketType || '',
      tokenAddress: tokenAddress || ''
    }
  }, [timeRange, marketType, tokenAddress])

  const { data: supplyHistoryData } = useGetSupplyHistoryChart(params)
  const { data: supplyAPYData } = useGetSupplyAPYHistoryChart(params)
  const { data: marketConfig } = useGetAllMarketConfig()
  const marketConfigData = marketConfig?.find(
    (v) => v.marketType === marketType
  )

  const data = useMemo(() => {
    return currentTab === '0'
      ? selectedCurrency === CURRENCY_USD
        ? supplyHistoryData
            ?.filter((v) => v.tokenPrice !== null)
            ?.map((v) => ({
              time: (v.t / 1000) as UTCTimestamp,
              value: Number(
                BigNumber(
                  formatUnits(
                    v.totalSupply,
                    Number(tokenInfo?.tokenInfo.decimals)
                  )
                )
                  .multipliedBy(v.tokenPrice)
                  .toNumber()
                  .toFixed(2)
              )
            }))
        : supplyHistoryData?.map((v) => ({
            time: (v.t / 1000) as UTCTimestamp,
            value: Number(
              formatUnits(v.totalSupply, Number(tokenInfo?.tokenInfo.decimals))
            )
          }))
      : supplyAPYData?.map((v) => ({
          time: (v.t / 1000) as UTCTimestamp,
          value: Number(BigNumber(v.supplyAPY).multipliedBy(100).toFixed(4))
        }))
  }, [
    supplyHistoryData,
    supplyAPYData,
    currentTab,
    tokenInfo,
    selectedCurrency
  ])

  const totalSupply = BigNumber(
    formatUnits(
      tokenInfo?.totalSupply || 0n,
      Number(tokenInfo?.tokenInfo.decimals)
    )
  )
  const totalSupplyUSD = totalSupply
    .multipliedBy(tokenInfo?.tokenInfo.price || 0)
    .toNumber()

  const supplyCap = BigNumber(
    formatUnits(
      tokenInfo?.supplyCap || 0n,
      Number(tokenInfo?.tokenInfo.decimals)
    )
  )
  const supplyCapUSD = supplyCap
    .multipliedBy(tokenInfo?.tokenInfo.price || 0)
    .toNumber()

  const supplyAvailable = supplyCap.minus(totalSupply)
  const supplyAvailableUSD = supplyCap
    .multipliedBy(tokenInfo?.tokenInfo.price || 0)
    .toNumber()

  const supplyInfo = {
    current: {
      label: 'Supplied',
      value: totalSupply.toNumber(),
      usdValue: totalSupplyUSD
    },
    available: {
      label: 'Capacity Available',
      value: supplyAvailable.toNumber(),
      usdValue: supplyAvailableUSD
    },
    max: {
      label: 'Max Capacity',
      value: supplyCap.toNumber(),
      usdValue: supplyCapUSD
    },
    token: tokenInfo?.tokenInfo.symbol || ''
  }

  const supplyData1 = [
    {
      label: 'Max LTV',
      value: `${marketConfigData?.maxLTV ? (marketConfigData.maxLTV * 100).toFixed(2) : 0}%`
    },
    {
      label: 'Liq.LTV',
      value: `${tokenInfo ? (tokenInfo.liqLTV * 100).toFixed(2) : 0}%`
    },
    {
      label: 'Liq.Penalty',
      value: tokenInfo?.liqPenalty
    }
  ]
  const supplyData2 = [
    {
      label: '30D Avg.APY',
      value: `${(tokenInfo?.avgSupplyAPY30D ?? 0).toFixed(2)}%`,
      valueClassName: 'text-green'
    },
    {
      label: '90D Avg.APY',
      value: `${(tokenInfo?.avgSupplyAPY90D ?? 0).toFixed(2)}%`,
      valueClassName: 'text-green'
    },
    {
      label: '180D Avg.APY',
      value: `${(tokenInfo?.avgSupplyAPY180D ?? 0).toFixed(2)}%`,
      valueClassName: 'text-green'
    }
  ]

  return (
    <div id={MarketOverviewMenu.SupplyOverview} className="flex flex-col gap-6">
      <div className="pb-3 rounded-xl border border-border-8 flex flex-col bg-overview-gradient">
        <Tabs
          value={currentTab}
          orientation="vertical"
          onValueChange={(value) => {
            setCurrentTab(value)
          }}>
          <TabsList
            aria-label="collateral tabs"
            className="flex justify-between items-center px-3">
            <div className="flex gap-3">
              <TabsTrigger className="px-1 py-4.5" value="0">
                Total Supplied
              </TabsTrigger>
              <TabsTrigger value="1" className="px-1 flex gap-2.5">
                <span>Supply APY</span>
                <span className="text-green rounded-sm bg-green/10 p-1.5">
                  {tokenInfo
                    ? (Number(tokenInfo.supplyAPY) * 100).toFixed(2)
                    : 0}
                  % APY
                </span>
              </TabsTrigger>
            </div>

            <div className="flex gap-6">
              <TokenSwitcher
                tokens={currencies}
                value={selectedCurrency}
                onValueChange={(value) => {
                  setSelectedCurrency(value)
                }}
              />
              <RangeSwitcher
                value={currentRange}
                onRangeChange={(value) => {
                  setCurrentRange(value)
                }}
              />
            </div>
          </TabsList>
        </Tabs>
        <div className="px-3">
          <BaseChart
            seriesData={[
              {
                title: currentTab === '0' ? 'Total Supplied' : 'Supply APY',
                data: data || [],
                options: {
                  topColor: '#40FF9F4D',
                  bottomColor: '#235B8800'
                }
              }
            ]}
            priceFormat={
              currentTab === '1'
                ? PriceFormat.Percentage
                : selectedCurrency === CURRENCY_USD
                  ? PriceFormat.Usd
                  : PriceFormat.Number
            }
            className="h-[380px]"
          />
        </div>
      </div>
      <div className="flex gap-3">
        <CapacityInfo data={supplyInfo} className="flex-1" />
        <div className="flex-1 flex flex-col gap-3">
          <OverviewBar data={supplyData1} cellClassName="p-0" />
          <OverviewBar data={supplyData2} cellClassName="p-0" />
        </div>
      </div>
    </div>
  )
}
