import { UserIcon } from '@/assets/icons'
import MarketOverview from './market-overview'
import { OverviewBar } from '@/components/overview-bar'
import { useMarketOverview } from '../hooks/use-market-overview'
import { useMemo } from 'react'
import { NumberCell } from '@/components/number-cell'

const Overview = () => {
  const { marketOverview } = useMarketOverview()
  const overviewData = useMemo(
    () => [
      {
        label: 'My Loans',
        value: marketOverview?.myLoans
      },
      {
        label: 'Net Value',
        value: <NumberCell value={marketOverview?.netValue} prefix="$" />
      },
      {
        label: 'Total Supplied',
        value: <NumberCell value={marketOverview?.totalSupplied} prefix="$" />
      },
      {
        label: 'Total Borrowed',
        value: <NumberCell value={marketOverview?.totalBorrowed} prefix="$" />
      },
      {
        label: 'Avg LTV',
        value: marketOverview ? (
          <NumberCell value={marketOverview?.avgLTV * 100} suffix="%" />
        ) : (
          0
        )
      },
      {
        label: 'Loans At-Risk',
        value: marketOverview?.loansAtRisk,
        className: 'text-primary'
      }
    ],
    [marketOverview]
  )
  if (!marketOverview || marketOverview.myLoans === 0) return null
  return (
    <div className="flex flex-col gap-6 pb-6">
      <div className="flex items-center gap-x-1.5">
        <UserIcon />
        <span>Your Overview</span>
      </div>
      <OverviewBar data={overviewData} />
      {marketOverview?.markets.map((market, index) => (
        <MarketOverview key={index} data={market} />
      ))}
    </div>
  )
}
export default Overview
