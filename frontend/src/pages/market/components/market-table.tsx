import { type FC, useMemo, useState } from 'react'
import { useMarketColumns } from './columns'
import { DataTable } from '@/components/data-table'
import { useMarketList } from '../hooks/use-market-list'
import { useNavigate, useParams } from 'react-router'
import type { SortingState } from '@tanstack/react-table'

export const MarketTable: FC<{
  marketName?: string
  showMarketNameInHeader?: boolean
  size?: 'sm' | 'md' | 'lg'
  className?: string
}> = ({ marketName = '', showMarketNameInHeader = true, size, className }) => {
  const { leftToken, rightToken } = useParams()
  const navigate = useNavigate()
  const { data, isLoading } = useMarketList(marketName)
  const columns = useMarketColumns(
    showMarketNameInHeader ? marketName : undefined
  )

  const filteredData = useMemo(() => {
    if (!leftToken || !rightToken) return data
    return data?.filter((item) => {
      return item.symbol === leftToken || item.symbol === rightToken
    })
  }, [data, leftToken, rightToken])

  // Set default sorting to TotalSupply column in descending order
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: 'totalSupply',
      desc: true
    }
  ])

  return (
    <DataTable
      data={filteredData ?? []}
      size={size}
      columns={columns}
      isLoading={isLoading}
      sorting={sorting}
      onSortingChange={setSorting}
      onPressRow={(row) =>
        navigate(`/market/${row.original.marketType}/${row.original.token}`)
      }
      className={className}
      classNames={{ headerCell: 'px-0', contentCell: 'px-0 py-3' }}
    />
  )
}
