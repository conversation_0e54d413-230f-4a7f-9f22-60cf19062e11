import { MinusIcon, PlusIcon } from '@/assets/icons'
import { Collapsible, CollapsibleContent } from '@/components/ui/collapsible'
import { cn } from '@/lib/utils'
import React, { type FC } from 'react'
import { MarketAssetsEnum, type AssetsInfo } from '../types'
import { AssetsTable } from './assets-table'
import { Progress, ProgressIndicator } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { useNavigate } from 'react-router'
import { NumberCell } from '@/components/number-cell'
import { getMarketName } from '@/lib/utils'

export const PositionStatusBar: FC<{
  ltv: number
  warningLtv: number
  maxLtv: number
  leftAmount: number
}> = ({ ltv, warningLtv, maxLtv, leftAmount }) => {
  return (
    <Progress className="flex-1 relative text-xs" value={0.5}>
      <ProgressIndicator style={{ width: `${ltv * 100}%` }} />
      <div className="absolute left-0 top-3 opacity-60">
        You can borrow another <NumberCell value={leftAmount} prefix="$" />{' '}
        before reaching Max LTV
      </div>
      <div
        className="absolute top-0 text-primary flex flex-col items-center"
        style={{ left: `${warningLtv * 100}%` }}>
        <div className="w-1 h-2 rounded bg-primary" />
        <div className="underline">{warningLtv * 100}%</div>
      </div>
      <div
        className="absolute top-0 text-red flex flex-col items-center"
        style={{ left: `${maxLtv * 100}%` }}>
        <div className="w-1 h-2 rounded bg-red" />
        <div className="underline">{maxLtv * 100}%</div>
      </div>
    </Progress>
  )
}

export const AssetsList: FC<{
  marketName: string
  marketType: string
  assetsType: MarketAssetsEnum
  total: number
  data: AssetsInfo[]
  className?: string
  innerClassName?: string
  tableClassName?: string
  showOperation?: boolean
}> = ({
  marketName,
  marketType,
  assetsType,
  total,
  data,
  className,
  innerClassName,
  tableClassName,
  showOperation = true
}) => {
  return (
    <div className={cn('flex flex-col gap-1.5', className)}>
      <div
        className={cn(
          'flex justify-between border-b border-border-8 pb-3 items-center',
          innerClassName
        )}>
        <div>{assetsType}</div>
        <div className="flex gap-1.5 text-xs">
          <span className="opacity-60">
            Total{' '}
            {assetsType === MarketAssetsEnum.Supplied ? 'Supplied' : 'Borrowed'}
          </span>
          <NumberCell value={total} prefix="$" />
        </div>
      </div>
      <div className={tableClassName}>
        <AssetsTable
          marketName={marketName}
          marketType={marketType}
          data={data}
          isLoading={false}
          assetsType={assetsType}
          showOperation={showOperation}
        />
      </div>
    </div>
  )
}
const MarketOverview: FC<{
  data: {
    marketName: string
    marketType: string
    netValue: number
    surplus: number
    supplied: number
    borrowed: number
    ltv: number
    warnLtv: number
    maxLtv: number
    collaterals: AssetsInfo[]
    debts: AssetsInfo[]
  }
}> = ({ data }) => {
  const [open, setOpen] = React.useState(false)
  const navigate = useNavigate()

  return (
    <Collapsible
      open={open}
      onOpenChange={setOpen}
      className={!open ? 'border-none' : ''}>
      <div
        className={cn(
          'flex items-center p-3 justify-between w-full hover:bg-gradient-3 border border-border-8/0 hover:border-border-12',
          open ? 'border-none rounded-t-xl' : 'rounded-xl'
        )}
        onClick={() => setOpen((prev) => !prev)}>
        <div className="flex items-center gap-3">
          <div className="size-4.5">{open ? <MinusIcon /> : <PlusIcon />}</div>
          <span>{getMarketName(data.marketName)}</span>
        </div>
        <div className="flex gap-12 text-sm items-center">
          <div className="flex gap-3">
            <span className="opacity-60">Net Value</span>
            <NumberCell value={data.netValue} prefix="$" />
          </div>
          <div className="flex gap-3">
            <span className="opacity-60">Supplied</span>
            <NumberCell value={data.supplied} prefix="$" />
          </div>
          <div className="flex gap-3">
            <span className="opacity-60">Borrowed</span>
            <NumberCell value={data.borrowed} prefix="$" />
          </div>
          <div className="flex gap-3">
            <span className="opacity-60">LTV</span>{' '}
            <NumberCell
              className={cn(
                'text-green',
                data.ltv >= data.warnLtv && 'text-primary',
                data.ltv >= data.maxLtv && 'text-red'
              )}
              value={data.ltv * 100}
              suffix="%"
            />
          </div>
          <Button
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              navigate(`/market/dashboard/${data.marketName}`)
            }}>
            Dashboard
          </Button>
        </div>
      </div>

      <CollapsibleContent
        className={cn(
          'py-6 flex flex-col gap-6',
          open && 'border-t border-border-8'
        )}>
        <div className="flex gap-6 items-center border-b border-border-8 pb-6 px-6">
          <div className="underline">Positions Status</div>
          <PositionStatusBar
            ltv={data.ltv}
            warningLtv={data.warnLtv}
            maxLtv={data.maxLtv}
            leftAmount={data.surplus}
          />
        </div>
        <div className="flex justify-between gap-12 px-6">
          <AssetsList
            marketName={data.marketName}
            marketType={data.marketType}
            assetsType={MarketAssetsEnum.Supplied}
            total={data.supplied}
            data={data.collaterals}
          />
          <AssetsList
            marketName={data.marketName}
            marketType={data.marketType}
            assetsType={MarketAssetsEnum.Borrowed}
            total={data.borrowed}
            data={data.debts}
          />
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
export default MarketOverview
