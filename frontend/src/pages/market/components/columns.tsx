import { useMemo } from 'react'
import type { ColumnDef } from '@tanstack/react-table'
import { MarketAssetsEnum, type AssetsInfo, type MarketInfo } from '../types'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl, getIllustrationUrl } from '@/lib/assets'
import { BorrowButton } from '@/components/dialogs/borrow-dialog'
import { SupplyButton } from '@/components/dialogs/supply-dialog'
import { ManageCollateralButton } from '@/components/dialogs/manage-collateral-dialog'
import { ManageDebtButton } from '@/components/dialogs/manage-debt-dialog'

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger
} from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import { NumberCell } from '@/components/number-cell'
import AssetApy from './asset-apy'

export const useMarketColumns = (marketName?: string) => {
  const columns: ColumnDef<MarketInfo>[] = useMemo(
    () => [
      {
        id: 'assets',
        size: 220,
        cell: ({ row }) => (
          <>
            {row.original.isLp ? (
              <div className="flex flex-col gap-1.5">
                <div className="flex items-center gap-3 pr-16">
                  <div className="relative mr-4">
                    <Avatar
                      src={getIconUrl('stSui')}
                      alt="sui-logo"
                      size={24}
                    />
                    <Avatar
                      src={getIconUrl('sui')}
                      alt="sui-logo"
                      size={24}
                      className="absolute left-4 top-0"
                    />
                  </div>
                  <span className="text-sm">{row.original.assets}</span>
                </div>
                {row.original.cetus && (
                  <div className="w-[67px] flex gap-1 items-center rounded-sm bg-border-5 px-1 py-1.5">
                    <img
                      width={18}
                      height={18}
                      className="shrink-0 no-drag"
                      src={getIllustrationUrl('cetus')}
                      alt="cetus"
                    />
                    <span className="text-xs">{row.original.cetus}</span>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center gap-3 pr-16">
                <Avatar src={getIconUrl('stSui')} alt={row.original.assets} />
                <div className="flex flex-col">
                  <div className="flex items-center gap-1.5">
                    <span className="text-sm">{row.original.assets}</span>
                    {row.original.stSUIApy && (
                      <div className="bg-[#008BD5]/20 p-1.5 rounded-md text-[#008BD5] text-xs">
                        {row.original.stSUIApy} APY
                      </div>
                    )}
                  </div>
                  {row.original.walletBalance && (
                    <NumberCell
                      className="text-xs opacity-60"
                      value={row.original.walletBalance}
                      prefix="Wallet: "
                    />
                  )}
                </div>
              </div>
            )}
          </>
        ),
        header: () =>
          marketName ? (
            <span className="text-sm text-primary font-medium">
              {marketName}
            </span>
          ) : (
            'Assets'
          ),
        enableSorting: !marketName,
        accessorKey: 'assets',
        defaultVisibility: true
      },
      {
        id: 'totalSupply',
        header: 'Total Supply',
        accessorKey: 'totalSupplyUSD',
        cell: ({ row }) => (
          <Tooltip>
            <TooltipTrigger>
              <NumberCell value={row.original.totalSupplyUSD} prefix="$" />
            </TooltipTrigger>
            <TooltipContent
              className="flex flex-col gap-3 w-[230px]"
              side="left">
              <div className="flex justify-between">
                <span className="opacity-60">Filled</span>
                <NumberCell value={row.original.supplyFilled} suffix="%" />
              </div>
              <div className="flex justify-between">
                <span className="opacity-60">Supply Cap</span>
                <NumberCell
                  value={row.original.supplyCap}
                  suffix={` ${row.original.assets}`}
                />
              </div>
              <div className="flex justify-between">
                <span className="opacity-60">Total Supply</span>
                <NumberCell
                  value={row.original.totalSupply}
                  suffix={` ${row.original.assets}`}
                />
              </div>
            </TooltipContent>
          </Tooltip>
        )
      },
      {
        id: 'totalBorrow',
        header: 'Total Borrow',
        accessorKey: 'totalBorrowUSD',
        cell: ({ row }) => (
          <Tooltip>
            <TooltipTrigger>
              <NumberCell value={row.original.totalBorrowUSD} prefix="$" />
            </TooltipTrigger>
            <TooltipContent
              className="flex flex-col gap-3 w-[230px]"
              side="left">
              <div className="flex justify-between">
                <span className="opacity-60">Filled</span>
                <NumberCell value={row.original.borrowFilled} suffix="%" />
              </div>
              <div className="flex justify-between">
                <span className="opacity-60">Borrow Cap</span>
                <NumberCell
                  value={row.original.borrowCap}
                  suffix={` ${row.original.assets}`}
                />
              </div>
              <div className="flex justify-between">
                <span className="opacity-60">Total Borrow</span>
                <NumberCell
                  value={row.original.totalBorrow}
                  suffix={` ${row.original.assets}`}
                />
              </div>
              <div className="flex justify-between">
                <span className="opacity-60">Utilization</span>
                <NumberCell value={row.original.utilization} suffix="%" />
              </div>
            </TooltipContent>
          </Tooltip>
        )
      },
      {
        id: 'liqLtv',
        header: 'Liq LTV',
        accessorKey: 'liqLtv',
        cell: ({ row }) => (
          <NumberCell value={row.original.liqLtv * 100} suffix="%" />
        )
      },
      {
        id: 'supplyAPY',
        header: 'Supply APY',
        accessorKey: 'supplyAPY',
        cell: ({ row }) => (
          <span className="text-sm text-green">
            {row.original.supplyAPY !== null
              ? `${row.original.supplyAPY.toFixed(2)}%`
              : '-'}
          </span>
        )
      },
      {
        id: 'supply',
        header: '',
        accessorKey: 'supply',
        enableSorting: false,
        cell: ({ row }) => (
          <div className="flex justify-start">
            {row.original.hasSupplied ? (
              <ManageCollateralButton
                id={row.original.id}
                marketName={row.original.marketName}
                marketType={row.original.marketType}
                coinType={row.original.token}
                disabled={row.original.hasBorrowed}
              />
            ) : (
              <SupplyButton
                id={row.original.id}
                isLp={row.original.isLp}
                marketName={row.original.marketName}
                coinType={row.original.token}
                disabled={row.original.hasBorrowed}
              />
            )}
          </div>
        )
      },
      {
        id: 'borrowAPY',
        header: 'Borrow APY',
        accessorKey: 'borrowAPY',
        cell: ({ row }) => (
          <span className="text-sm text-primary">
            {row.original.borrowAPY !== null
              ? `${row.original.borrowAPY.toFixed(2)}%`
              : '-'}
          </span>
        )
      },
      {
        id: 'borrow',
        header: '',
        accessorKey: 'borrow',
        enableSorting: false,
        cell: ({ row }) => (
          <div className="flex justify-start">
            {row.original.hasBorrowed ? (
              <ManageDebtButton
                id={row.original.id}
                marketName={row.original.marketName}
                marketType={row.original.marketType}
                coinType={row.original.token}
                disabled={row.original.hasSupplied}
              />
            ) : (
              <BorrowButton
                id={row.original.id}
                marketName={row.original.marketName}
                coinType={row.original.token}
                disabled={row.original.hasSupplied}
              />
            )}
          </div>
        )
      }
    ],
    [marketName]
  )

  return columns
}

export const useAssetsColumns = (
  marketName: string,
  marketType: string,
  type: MarketAssetsEnum,
  openRowId: string | null,
  setOpenRowId: React.Dispatch<React.SetStateAction<string | null>>,
  showOperation: boolean
) => {
  const baseColumns: ColumnDef<AssetsInfo>[] = useMemo(
    () => [
      {
        id: 'assets',
        cell: ({ row }) => (
          <div className="flex items-center gap-1.5 pr-16">
            <Avatar
              src={getIconUrl(row.original.assets.toLowerCase(), 'svg')}
              alt={row.original.assets}
            />
            <span className="text-sm">{row.original.assets}</span>
          </div>
        ),
        header: 'Assets',
        accessorKey: 'assets',
        enableSorting: true
      },
      {
        id: 'value',
        header: 'Value',
        accessorKey: 'value',
        cell: ({ row }) => <NumberCell value={row.original.value} prefix="$" />
      },
      {
        id: 'apy',
        header: 'APY',
        accessorKey: 'apy',
        cell: ({ row }) => (
          <AssetApy
            marketType={marketType}
            type={type}
            coinType={row.original.coinType}
            className={cn(
              'text-sm',
              type === MarketAssetsEnum.Supplied ? 'text-green' : 'text-primary'
            )}
          />
        )
      }
    ],
    [marketType, type]
  )

  const manageColumn: ColumnDef<AssetsInfo> = useMemo(
    () => ({
      id: 'manage',
      header: '',
      accessorKey: 'manage',
      size: 12,
      enableSorting: false,
      cell: ({ row }) => {
        return (
          <div className="flex justify-end">
            {type === MarketAssetsEnum.Supplied ? (
              <ManageCollateralButton
                marketName={marketName}
                marketType={marketType}
                coinType={row.original.coinType}
                id={row.original.assets}
                fromAssets
                isDropdownMenuOpen={row.id === openRowId}
                onDropdownMenuChange={(value) =>
                  setOpenRowId(value ? row.id : null)
                }
              />
            ) : (
              <ManageDebtButton
                marketName={marketName}
                marketType={marketType}
                coinType={row.original.coinType}
                id={row.original.assets}
                fromAssets
                isDropdownMenuOpen={row.id === openRowId}
                onDropdownMenuChange={(value) =>
                  setOpenRowId(value ? row.id : null)
                }
              />
            )}
          </div>
        )
      }
    }),
    [marketName, marketType, type, openRowId, setOpenRowId]
  )

  const columns: ColumnDef<AssetsInfo>[] = useMemo(
    () => (showOperation ? [...baseColumns, manageColumn] : baseColumns),
    [showOperation, manageColumn, baseColumns]
  )

  return columns
}
