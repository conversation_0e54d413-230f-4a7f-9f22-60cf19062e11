import { useAssetsColumns } from './columns'
import type { AssetsInfo, MarketAssetsEnum } from '../types'
import { DataTable } from '@/components/data-table'
import { useState } from 'react'

export const AssetsTable: React.FC<{
  marketName: string
  marketType: string
  assetsType: MarketAssetsEnum
  data?: AssetsInfo[]
  isLoading: boolean
  className?: string
  showOperation?: boolean
}> = ({
  marketName,
  marketType,
  assetsType,
  data = [],
  isLoading = false,
  className,
  showOperation = true
}) => {
  const [openRowId, setOpenRowId] = useState<string | null>(null)
  const columns = useAssetsColumns(
    marketName,
    marketType,
    assetsType,
    openRowId,
    setOpenRowId,
    showOperation
  )

  return (
    <DataTable
      size="sm"
      data={data}
      columns={columns}
      isLoading={isLoading}
      className={className}
      classNames={{
        headerCell: 'pl-0',
        contentCell: 'pl-0',
        bodyRow: 'border-none'
      }}
      onPressRow={(row) =>
        setOpenRowId((prev) => (prev === row.id ? null : row.id))
      }
    />
  )
}
