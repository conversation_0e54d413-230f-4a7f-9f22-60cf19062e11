import { useMemo } from 'react'
import { useMarketTokenApy } from '../hooks/use-market-apy'

const MarketTokenApy = ({
  marketName,
  tokenAddress,
  utilization,
  apyType,
  className
}: {
  marketName: string
  tokenAddress: string
  utilization?: string
  apyType: 'supply' | 'borrow'
  className?: string
}) => {
  const utilizationNumber = useMemo(() => {
    return Number(Number(utilization) / 100)
  }, [utilization])
  const { supplyApy, borrowApy } = useMarketTokenApy(
    marketName,
    tokenAddress,
    utilizationNumber
  )
  const apy = useMemo(() => {
    if (apyType === 'supply') {
      return supplyApy !== null ? (supplyApy * 100).toFixed(2) : null
    }
    return borrowApy !== null ? (borrowApy * 100).toFixed(2) : null
  }, [apyType, supplyApy, borrowApy])
  return <span className={className}>{apy ? `${apy}%` : '-'}</span>
}

export default MarketTokenApy
