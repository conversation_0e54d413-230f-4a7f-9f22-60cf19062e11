import { useMemo } from 'react'
import { useGetMarketInfo } from '@/queries/market'
import { getShortName } from '@/utils/data'
import { MarketAssetsEnum } from '../types'
import { BigNumber } from 'bignumber.js'

const AssetApy = ({
  marketType,
  type,
  coinType,
  className
}: {
  marketType: string
  type: MarketAssetsEnum
  coinType: string
  className?: string
}) => {
  const marketName = getShortName(marketType)
  const { data: marketInfo } = useGetMarketInfo(marketName, coinType, {
    enabled: !!marketName && !!coinType
  })
  // const utilization = useMemo(() => {
  //   if (!marketInfo) return 0
  //   return Number(Number(marketInfo?.utilization).toFixed(4))
  // }, [marketInfo])
  // const { supplyApy, borrowApy } = useMarketTokenApy(
  //   marketName,
  //   coinType,
  //   utilization
  // )
  const apy = useMemo(() => {
    if (!marketInfo) return null
    if (type === MarketAssetsEnum.Supplied) {
      return marketInfo.supplyAPY !== null
        ? BigNumber(marketInfo.supplyAPY).times(100).toFixed(2)
        : null
    }
    return marketInfo.borrowAPY !== null
      ? BigNumber(marketInfo.borrowAPY).times(100).toFixed(2)
      : null
  }, [type, marketInfo])
  return <span className={className}>{apy ? `${apy}%` : '-'}</span>
}

export default AssetApy
