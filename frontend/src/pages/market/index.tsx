import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { MarketTable } from './components/market-table'
import { useEffect, useMemo, useState } from 'react'
import Overview from './components/overview'
import BorrowDialog from '../../components/dialogs/borrow-dialog'
import SupplyDialog from '@/components/dialogs/supply-dialog'
import ManageCollateralDialog from '@/components/dialogs/manage-collateral-dialog'
import ManageDebtDialog from '@/components/dialogs/manage-debt-dialog'
import { useGetWeb3Config } from '@/queries/config'
import { getMarketName } from '@/lib/utils'
import { MarketName } from '@/hooks/use-pebble-operation'
import { GradientPageTitle } from '@/components/gradient-page-title'

export default function Market() {
  const { suiNetworkConfig: networks } = useGetWeb3Config()
  const markets = useMemo(() => {
    return networks?.contract?.marketType
  }, [networks])
  const [currentTab, setCurrentTab] = useState<string>(MarketName.MainMarket)
  useEffect(() => {
    if (markets?.length) {
      setCurrentTab(markets[0])
    }
  }, [markets])

  return (
    <section className="relative pt-[53px] pb-10 max-w-6xl flex flex-col" 
      style={{ maxWidth: 1176 }}>
      <GradientPageTitle
        className="mb-12"
        title="Market"
        description="Borrow and earn with Pebble lending"
      />
      <Overview />
      <Tabs value={currentTab} orientation="vertical">
        <TabsList aria-label="market tabs">
          {markets?.map((market) => (
            <TabsTrigger
              key={market}
              value={market}
              onClick={() => setCurrentTab(market)}>
              {getMarketName(market)}
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>
      <div className="pl-6 pr-3">
        <MarketTable marketName={currentTab} />
      </div>

      <BorrowDialog />
      <SupplyDialog />
      <ManageCollateralDialog />
      <ManageDebtDialog />
    </section>
  )
}
