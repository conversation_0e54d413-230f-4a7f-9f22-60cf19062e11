import type { AssetBorrow, AssetDeposit } from '@pebble-protocol/pebble-sdk'

export enum MarketEnum {
  Main = 'Main Market',
  StSui = 'stSUI Market',
  Lp = 'LP Market'
}

export enum MarketAssetsEnum {
  Supplied = 'Supplied',
  Borrowed = 'Borrowed'
}

export enum CollateralActionEnum {
  Supply = 'Supply',
  Withdraw = 'Withdraw'
}

export enum DebtActionEnum {
  Borrow = 'Borrow',
  RepayFromWallet = 'RepayFromWallet',
  RepayWithCollateral = 'RepayWithCollateral'
}

export type MarketInfo = {
  id: string
  token: string
  symbol: string
  marketType: string
  marketID: string
  marketName: string
  assets: string
  totalSupply: string
  totalBorrow: string
  totalSupplyUSD: number
  totalBorrowUSD: number
  liqLtv: number
  supplyAPY: number
  borrowAPY: number
  hasSupplied?: boolean
  hasBorrowed?: boolean
  isLp?: boolean
  cetus?: string
  walletBalance?: string
  stSUIApy?: string
  supplyFilled?: string
  borrowFilled?: string
  borrowCap?: string
  utilization?: string
  supplyCap?: string
}

export type AssetsInfo = {
  coinType: string
  assets: string
  value: number
  apy: number
  assetObject?: AssetDeposit | AssetBorrow
}

export enum MarketDetailTabEnum {
  MarketOverview = 'Market Overview',
  TransactionHistrory = 'Transaction History'
}

export enum MarketOverviewMenu {
  GeneralStatus = 'general-status',
  SupplyOverview = 'supply-overview',
  BorrowOverview = 'borrow-overview',
  UtilizationRatio = 'utilization-ratio',
  InterestRateCurve = 'interest-rate-curve',
  TokenPrice = 'token-price',
  ProofOfReserve = 'proof-op-reserve',
  ReserveLimit = 'reserve-limit'
}

export const CURRENCY_USD = 'USD'
