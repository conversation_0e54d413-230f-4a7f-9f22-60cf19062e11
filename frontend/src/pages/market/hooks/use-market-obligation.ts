import { useObligationInfo } from '@/hooks/use-obligation-info'
import { useMemo } from 'react'

export const useMarketObligation = (marketName: string) => {
  const { data: obligationInfo, isFetching: isFetchingObligationInfo } =
    useObligationInfo()
  const obligation = useMemo(() => {
    const obligationData = obligationInfo?.find(
      (obligationData) => obligationData.marketName === marketName
    )
    if (!obligationData) return null
    return obligationData.obligation
  }, [obligationInfo, marketName])
  return {
    obligation,
    isFetchingObligationInfo
  }
}

export const useMakertObligationInfo = (marketName: string) => {
  const { data: obligationInfo } = useObligationInfo()
  return obligationInfo?.find(
    (obligationData) => obligationData.marketName === marketName
  )
}
