import { getShortName } from '@/utils/data'
import { useMemo } from 'react'
import { useObligationInfo } from '@/hooks/use-obligation-info'
import { useMarket } from '@/hooks/use-market'
import { useGetAllMarketConfig } from '@/queries/config'
import type { AssetsInfo } from '../types'
import { Decimal } from '@pebble-protocol/pebble-sdk'

export const useMarketOverview = () => {
  const { data: obligationInfo } = useObligationInfo()
  const { data: marketsData } = useMarket()
  const { data: marketConfig } = useGetAllMarketConfig()

  const marketOverview = useMemo(() => {
    const rawMarkets = obligationInfo
      ? obligationInfo.map((obligationData) => {
          const obligation = obligationData.obligation
          const market = marketsData?.find(
            (v) => v.marketName === obligationData.marketName
          )?.market
          const config = marketConfig?.find(
            (v) => v.name === obligationData.marketName
          )
          let supplied = 0
          let borrowed = 0
          const collaterals: AssetsInfo[] = []
          obligation.depositAssets().forEach((v) => {
            const deposit = obligation.getDeposit(v)
            collaterals.push({
              coinType: deposit.coinType,
              assets: getShortName(deposit.coinType),
              value: deposit.usdValue.asNumber(),
              apy: 0, // calculate apy in useMarketColumns
              assetObject: deposit
            })
            supplied += deposit.usdValue.asNumber()
          })
          const debts: AssetsInfo[] = []
          obligation.borrowedAssets().forEach((v) => {
            const borrow = obligation.getBorrow(v)
            debts.push({
              coinType: borrow.coinType,
              assets: getShortName(borrow.coinType),
              value: borrow.usdValue.asNumber(),
              apy: 0, // calculate apy in useMarketColumns
              assetObject: borrow
            })
            borrowed += borrow.usdValue.asNumber()
          })
          const ltv = market ? obligation.currentLTV(market).asNumber() : 0
          const warnLtv = config?.warnLTV ?? 0
          const maxLtv = config?.maxLTV ?? 0
          return {
            marketName: obligationData.marketName,
            marketType: obligationData.marketType,
            netValue: obligation.netValue().asNumber(),
            surplus: market
              ? obligation
                  .surplus(market, Decimal.fromNumber(maxLtv))
                  .asNumber()
              : 0,
            supplied: supplied,
            borrowed: borrowed,
            ltv: ltv,
            maxLtv: maxLtv,
            warnLtv: warnLtv,
            collaterals: collaterals,
            debts: debts,
            atRisk: ltv >= warnLtv
          }
        })
      : []
    const markets = rawMarkets.filter(
      (v) => v.collaterals.length > 0 || v.debts.length > 0
    )
    const loansAtRisk = markets.filter((v) => v.atRisk).length
    return {
      myLoans: markets.length,
      netValue: markets.reduce((acc, cur) => acc + cur.netValue, 0),
      totalSupplied: markets.reduce((acc, cur) => acc + cur.supplied, 0),
      totalBorrowed: markets.reduce((acc, cur) => acc + cur.borrowed, 0),
      avgLTV:
        markets.length > 0
          ? markets.reduce((acc, cur) => acc + cur.ltv, 0) / markets.length
          : 0,
      loansAtRisk: loansAtRisk,
      markets: markets
    }
  }, [obligationInfo, marketsData, marketConfig])

  return { marketOverview }
}
