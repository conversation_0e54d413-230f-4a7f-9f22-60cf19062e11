import { useGetMarketList } from '@/queries/market'
import { useMemo } from 'react'
import { MarketEnum, type MarketInfo } from '../types'
import { useObligationInfo } from '@/hooks/use-obligation-info'
import { formatUnits } from '@/lib/units'
import BigNumber from 'bignumber.js'
import { useAllBalance } from '@/hooks/use-all-balance'

export const useMarketList = (marketName: string) => {
  const { data: marketList, isLoading } = useGetMarketList(marketName)
  const { data: obligationInfo } = useObligationInfo()
  const { balanceRaw } = useAllBalance()
  const obligation = useMemo(() => {
    const obligationData = obligationInfo?.find(
      (obligationData) => obligationData.marketName === marketName
    )
    if (!obligationData) return null
    return obligationData.obligation
  }, [obligationInfo, marketName])
  const data = useMemo(() => {
    return marketList?.map((item) => {
      const hasSupplied = obligation?.depositAssets().includes(item.token)
      const hasBorrowed = obligation?.borrowedAssets().includes(item.token)
      const totalSupply = BigNumber(
        formatUnits(item.totalSupply, Number(item.tokenInfo.decimals))
      )
      const totalSupplyUSD = totalSupply
        .multipliedBy(item.tokenInfo.price)
        .toNumber()
      const totalBorrow = BigNumber(
        formatUnits(item.totalBorrow, Number(item.tokenInfo.decimals))
      )
      const totalBorrowUSD = totalBorrow
        .multipliedBy(item.tokenInfo.price)
        .toNumber()
      const supplyCap = BigNumber(
        formatUnits(item.supplyCap, Number(item.tokenInfo.decimals))
      )
      const borrowCap = BigNumber(
        formatUnits(item.borrowCap, Number(item.tokenInfo.decimals))
      )
      const supplyFilled = supplyCap
        ? totalSupply.div(supplyCap).multipliedBy(100).toString()
        : '0'
      const borrowFilled = borrowCap
        ? totalBorrow.div(borrowCap).multipliedBy(100).toString()
        : '0'
      const balance = balanceRaw?.find(
        (v) => v.coinType === `0x${item.token}`
      )?.totalBalance
      return {
        id: item.id,
        token: item.token,
        symbol: item.tokenInfo.symbol,
        marketType: item.marketType,
        marketID: item.marketID,
        marketName: item.name,
        assets: item.tokenInfo.symbol,
        totalSupply: totalSupply.toString(),
        totalBorrow: totalBorrow.toString(),
        totalSupplyUSD,
        totalBorrowUSD,
        liqLtv: item.liqLTV,
        supplyAPY: BigNumber(item.supplyAPY).multipliedBy(100).toNumber(),
        borrowAPY: BigNumber(item.borrowAPY).multipliedBy(100).toNumber(),
        hasSupplied,
        hasBorrowed,
        isLp: marketName === MarketEnum.Lp,
        walletBalance: balance
          ? formatUnits(BigInt(balance), Number(item.tokenInfo.decimals))
          : undefined,
        supplyFilled,
        borrowFilled,
        borrowCap: borrowCap.toString(),
        utilization: BigNumber(item.utilization).multipliedBy(100).toFixed(2),
        supplyCap: supplyCap.toString()
      } as MarketInfo
    })
  }, [marketList, marketName, balanceRaw, obligation])
  return {
    data,
    hasObligation: !!obligation,
    isLoading: isLoading
  }
}
