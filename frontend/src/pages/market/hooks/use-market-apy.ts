import { useMarket } from '@/hooks/use-market'
import { convertAprToApy } from '@/utils/data'
import { Decimal } from '@pebble-protocol/pebble-sdk'
import { useMemo } from 'react'

export const useMarketTokenApy = (
  marketName: string,
  tokenAddress: string,
  utilization: number
) => {
  const { data: marketsData } = useMarket()
  const market = useMemo(() => {
    return marketsData?.find((v) => v.marketName === marketName)?.market
  }, [marketsData, marketName])
  const repayFeeRate = useMemo(() => {
    return market
      ?.assets()
      .find((v) => v.coinType === tokenAddress)
      ?.borrowSetting().repayFeeRate
  }, [market, tokenAddress])
  const assetConfiguration = useMemo(() => {
    return market?.getAssetConfiguration(tokenAddress ?? '')
  }, [market, tokenAddress])
  const supplyApy = useMemo(() => {
    if (!assetConfiguration || !repayFeeRate) return null
    const depositInterestRate = assetConfiguration.depositInterestRate(
      Decimal.fromNumber(utilization),
      repayFeeRate
    )
    const supplyApr = Decimal.fromNumber(365 * 24 * 3600)
      .mul(depositInterestRate)
      .asNumber()
    return convertAprToApy(supplyApr)
  }, [assetConfiguration, repayFeeRate, utilization])
  const borrowApy = useMemo(() => {
    if (!assetConfiguration) return null
    const borrowInterestRate = assetConfiguration.borrowInterestRate(
      Decimal.fromNumber(utilization)
    )
    const borrowApr = Decimal.fromNumber(365 * 24 * 3600)
      .mul(borrowInterestRate)
      .asNumber()
    return convertAprToApy(borrowApr)
  }, [assetConfiguration, utilization])
  return { supplyApy, borrowApy }
}
