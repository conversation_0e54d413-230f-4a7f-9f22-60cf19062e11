import { useGetMarketInfo, useGetMarketList } from '@/queries/market'
import { useMemo } from 'react'

export default function useMarketTokenInfo(
  marketName: string,
  tokenAddress: string
) {
  const { data: marketList } = useGetMarketList(marketName, { enabled: false })
  const cachedMarketInfo = useMemo(
    () => marketList?.find((v) => v.token === tokenAddress),
    [marketList, tokenAddress]
  )
  const { data: marketInfoData, isFetching } = useGetMarketInfo(
    marketName,
    tokenAddress,
    {
      enabled: !cachedMarketInfo
    }
  )
  const marketTokenInfo = useMemo(() => {
    if (cachedMarketInfo) {
      return cachedMarketInfo
    }
    return marketInfoData
  }, [cachedMarketInfo, marketInfoData])
  return {
    marketTokenInfo,
    isFetching
  }
}
