import { getIconUrl } from '@/lib/assets'
import { useMemo } from 'react'
import { formatUnits } from '@/lib/units'
import useMarketTokenInfo from './use-market-token-info'
import { useMarketObligation } from './use-market-obligation'

export const useMarketOperationInfo = (
  marketName: string,
  assetCoinType: string
) => {
  const { obligation, isFetchingObligationInfo } =
    useMarketObligation(marketName)
  const { marketTokenInfo: marketInfo, isFetching } = useMarketTokenInfo(
    marketName,
    assetCoinType
  )

  const coinType = useMemo(
    () => (marketInfo?.token ? `0x${marketInfo.token}` : ''),
    [marketInfo?.token]
  )

  const tokens = useMemo(() => {
    return {
      token0: {
        symbol: marketInfo?.tokenInfo?.symbol ?? assetCoinType,
        icon: getIconUrl('usdc', 'svg'),
        coinType
      }
    }
  }, [marketInfo?.tokenInfo?.symbol, coinType, assetCoinType])

  const decimals = useMemo(() => {
    if (!marketInfo?.tokenInfo?.decimals) return null
    return Number(marketInfo?.tokenInfo?.decimals)
  }, [marketInfo?.tokenInfo?.decimals])

  const borrowedAmount = useMemo(() => {
    if (!obligation || !decimals) return 0
    return obligation.borrowedAssets().includes(assetCoinType)
      ? Number(
          formatUnits(obligation.getBorrow(assetCoinType).amount(), decimals)
        )
      : 0
  }, [obligation, assetCoinType, decimals])
  const depositedAmount = useMemo(() => {
    if (!obligation || !decimals) return 0
    return obligation.depositAssets().includes(assetCoinType)
      ? Number(
          formatUnits(obligation.getDeposit(assetCoinType).amount(), decimals)
        )
      : 0
  }, [obligation, assetCoinType, decimals])

  return {
    tokens,
    coinType,
    marketInfo,
    borrowedAmount,
    depositedAmount,
    isFetching: isFetchingObligationInfo || isFetching
  }
}
