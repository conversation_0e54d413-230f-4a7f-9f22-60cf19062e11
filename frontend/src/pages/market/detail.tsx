import { BackButton } from '@/components/buttons'
import { MarketDetailOverview } from './components/detail/overview'
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'
import { Link, useParams } from 'react-router'
import { getShortName } from '@/utils/data'
import { getMarketName } from '@/lib/utils'
import { useMemo } from 'react'

export default function MarketDetail() {
  const { marketType, tokenAddress } = useParams()
  const tokenName = useMemo(() => {
    return getShortName(tokenAddress || '')
  }, [tokenAddress])
  return (
    <section className="relative mt-[90px] pb-10 w-[1244px] flex flex-col gap-y-6">
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              asChild
              className="opacity-60 hover:opacity-100 transition-all">
              <Link to="/market">Market</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <span>
              {tokenName} Reserve(
              {getMarketName(getShortName(marketType || ''))})
            </span>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      <div className="flex gap-3 items-center">
        <BackButton />
        <div className="text-2xl">{tokenName} Reserve</div>
      </div>

      <MarketDetailOverview
        marketType={marketType}
        tokenAddress={tokenAddress}
      />
    </section>
  )
}
