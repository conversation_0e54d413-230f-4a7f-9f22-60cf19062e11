import { PebblePointsIcon } from '@/assets/icons'
import { TransactionHistoryTypeEnum } from '../leverage/mock'
import { MarketEnum } from '../market/types'
import { ProductEnum } from './types'
import { PebblePiontsButton } from './components/pebble-points-button'

export const mockPortfolioData = [
  {
    id: 'netValue',
    label: <span className="opacity-60">Net Value</span>,
    value: '$4893.1'
  },
  {
    id: 'feeInterest',
    label: <span className="opacity-60">Fee & Interest</span>,
    value: <span className="text-green">+ $2.8</span>
  },
  {
    id: 'totalPnL',
    label: <span className="opacity-60">Total PnL</span>,
    value: <span className="text-red">- $2.8</span>
  },
  {
    id: 'point',
    label: (
      <span className="flex items-center gap-x-1.5">
        <PebblePointsIcon />
        <span className="text-sm opacity-60">Pebble Points</span>
      </span>
    ),
    value: (
      <span className="flex items-center gap-x-3">
        <span
          style={{
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}
          className="text-primary bg-gradient-1">
          0
        </span>
        <PebblePiontsButton />
      </span>
    )
  }
]

export const mockMarketData = [
  {
    id: 'main-market',
    market: 'Main Market',
    netValue: '$1,248.19',
    collateral: ['SUI', 'USDC', 'USDT'],
    debt: ['SUI', 'USDC', 'USDT'],
    ltv: '54.5%',
    interestEarned: '+$0.34',
    netApy: '10.43%'
  },
  {
    id: 'lp-market',
    market: 'LP Market',
    netValue: '$1,248.19',
    collateral: 'USDC / USDT',
    debt: '-',
    ltv: '54.5%',
    interestEarned: '+$0.34',
    netApy: '10.43%'
  }
]
export const mockMultiplyPositionsData = [
  {
    id: 'multiply',
    position: 'Position',
    netValue: '$1,248.19',
    pnl: '+$0.0519 USDC',
    ltv: '54.5%',
    netApy: '10.43%'
  }
]
export const mockMarginTradePositionsData = [
  {
    id: 'margin-trade',
    position: 'Position',
    netValue: '$1,248.19',
    pnl: '+$0.0519 USDC',
    ltv: '54.5%',
    leverage: '1.5x Long'
  }
]

export const mockAvailableRewardsData = [
  {
    id: 'rewards',
    asset: 'suiUSDT-USDC',
    rewardToken: ['SUI', 'CETUS'],
    value: ['$39.21', '$9.21']
  }
]

export const mockTransactionHistoryData = [
  {
    id: 'tx-1',
    date: '11 Jul 2025 13:43',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Repay,
    asset: '3.21',
    value: '$22.42',
    hash: '0x12345'
  },
  {
    id: 'tx-2',
    date: '12 Jul 2025 09:15',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Borrow,
    asset: 'SUI',
    value: '$32.42',
    hash: '0xabcdef123456'
  },
  {
    id: 'tx-3',
    date: '13 Jul 2025 17:22',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Deposit,
    asset: 'USDC',
    value: '$50.00',
    hash: '0x9876543210ab'
  },
  {
    id: 'tx-4',
    date: '14 Jul 2025 11:05',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Withdraw,
    asset: 'SUI',
    value: '$30.00',
    hash: '0x1234abcd5678'
  },
  {
    id: 'tx-5',
    date: '15 Jul 2025 14:30',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Borrow,
    asset: 'USDC',
    value: '$80.00',
    hash: '0xdeadbeefcafe'
  },
  {
    id: 'tx-6',
    date: '16 Jul 2025 08:45',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Repay,
    asset: 'SUI',
    value: '$40.00',
    hash: '0xbeadfeed1234'
  },
  {
    id: 'tx-7',
    date: '17 Jul 2025 19:10',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Deposit,
    asset: 'USDC',
    value: '$60.00',
    hash: '0xfaceb00c1234'
  },
  {
    id: 'tx-8',
    date: '18 Jul 2025 13:55',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Withdraw,
    asset: 'SUI',
    value: '$25.00',
    hash: '0x123456abcdef'
  },
  {
    id: 'tx-9',
    date: '19 Jul 2025 10:20',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Borrow,
    asset: 'USDT',
    value: '$70.00',
    hash: '0xabcdef987654'
  },
  {
    id: 'tx-10',
    date: '20 Jul 2025 15:30',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Repay,
    asset: 'USDT',
    value: '$55.00',
    hash: '0x123abc456def'
  },
  {
    id: 'tx-11',
    date: '21 Jul 2025 12:00',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Deposit,
    asset: 'SUI',
    value: '$100.00',
    hash: '0xfeedbead1234'
  },
  {
    id: 'tx-12',
    date: '22 Jul 2025 09:45',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Withdraw,
    asset: 'USDC',
    value: '$45.00',
    hash: '0xdeadbead5678'
  },
  {
    id: 'tx-13',
    date: '23 Jul 2025 16:10',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Borrow,
    asset: 'SUI',
    value: '$120.00',
    hash: '0x123456789abc'
  },
  {
    id: 'tx-14',
    date: '24 Jul 2025 14:25',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Repay,
    asset: 'USDC',
    value: '$90.00',
    hash: '0xabcdefabcdef'
  },
  {
    id: 'tx-15',
    date: '25 Jul 2025 11:35',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Deposit,
    asset: 'USDT',
    value: '$110.00',
    hash: '0xfacefeed1234'
  },
  {
    id: 'tx-16',
    date: '26 Jul 2025 13:50',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Withdraw,
    asset: 'USDT',
    value: '$35.00',
    hash: '0xbeefdead5678'
  },
  {
    id: 'tx-17',
    date: '27 Jul 2025 10:05',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Borrow,
    asset: 'USDC',
    value: '$140.00',
    hash: '0x1234feed5678'
  },
  {
    id: 'tx-18',
    date: '28 Jul 2025 15:15',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Repay,
    asset: 'SUI',
    value: '$75.00',
    hash: '0xfeed1234bead'
  },
  {
    id: 'tx-19',
    date: '29 Jul 2025 12:40',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Deposit,
    asset: 'USDC',
    value: '$130.00',
    hash: '0xdead1234beef'
  },
  {
    id: 'tx-20',
    date: '30 Jul 2025 09:55',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Withdraw,
    asset: 'SUI',
    value: '$60.00',
    hash: '0xbead1234feed'
  },
  {
    id: 'tx-21',
    date: '31 Jul 2025 14:10',
    product: ProductEnum.Market,
    market: MarketEnum.Main,
    actionType: TransactionHistoryTypeEnum.Borrow,
    asset: 'USDT',
    value: '$150.00',
    hash: '0x1234beadfeed'
  }
]
