export enum PortfolioTabEnum {
  PositionOverview = 'Position Overview',
  MyRewards = 'My Rewards',
  TransactionHistory = 'Transaction History'
}

export enum PositionOverviewMenuEnum {
  Market = 'Market',
  Multiply = 'Multiply',
  MarginTrade = 'Margin Trade'
}

export interface PositionOverviewMarketTableData {
  id: string
  market: string
  netValue: string
  collateral: string
  debt: string
  ltv: string
  interestEarned: string
  netApy: string
}

export enum ProductEnum {
  Market = 'Market',
  Multiply = 'Multiply'
}
