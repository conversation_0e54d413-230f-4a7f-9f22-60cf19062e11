import { Overview } from './components/overview'
import { InterestRateModelChart } from './components/interest-rate-model-chart'
import { GradientPageTitle } from '@/components/gradient-page-title'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { PortfolioTabEnum, PositionOverviewMenuEnum } from './types'
import { PositionOverview } from './components/position-overview'
import { MyRewards } from './components/my-rewards'
import { TransactionHistory } from './components/transaction-history'
import { useState } from 'react'
import { MultiSelect } from '@/components/multi-select'
import { enumToArray } from '@/lib/enum'
import { FilterIcon } from '@/assets/icons'

export default function Portfolio() {
  const [tab, setTab] = useState(PortfolioTabEnum.PositionOverview)
  const [filter, setFilter] = useState<PositionOverviewMenuEnum[]>([])

  return (
    <section
      className="relative py-[53px] flex flex-col"
      style={{ width: 1200 }}>
      <GradientPageTitle
        title="Portfolio"
        description="Track all your positions in one place"
        className="mb-11"
      />
      <div className="flex flex-col gap-y-9">
        <div className="flex flex-col gap-y-3">
          <Overview />
          <InterestRateModelChart />
        </div>

        <Tabs
          className="relative"
          value={tab}
          orientation="vertical"
          onValueChange={(v) => setTab(v as PortfolioTabEnum)}>
          <TabsList className="pl-0" aria-label="market tabs">
            <TabsTrigger value={PortfolioTabEnum.PositionOverview}>
              {PortfolioTabEnum.PositionOverview}
            </TabsTrigger>
            <TabsTrigger value={PortfolioTabEnum.MyRewards}>
              {PortfolioTabEnum.MyRewards}
            </TabsTrigger>
            <TabsTrigger value={PortfolioTabEnum.TransactionHistory}>
              {PortfolioTabEnum.TransactionHistory}
            </TabsTrigger>
          </TabsList>
          <TabsContent value={PortfolioTabEnum.PositionOverview}>
            <PositionOverview />
          </TabsContent>
          <TabsContent value={PortfolioTabEnum.MyRewards}>
            <MyRewards />
          </TabsContent>
          <TabsContent value={PortfolioTabEnum.TransactionHistory}>
            <TransactionHistory />
          </TabsContent>

          {tab === PortfolioTabEnum.TransactionHistory && (
            <MultiSelect
              values={filter}
              onValueChange={(v) => setFilter(v)}
              className="absolute right-3 top-2"
              selectAllLabel="All Positions"
              items={enumToArray(PositionOverviewMenuEnum).map(({ value }) => ({
                label: value,
                value: value
              }))}>
              <span className="text-xs flex items-center gap-x-1.5">
                <FilterIcon className="w-[11px] h-[11px]" />
                <span>All positions</span>
              </span>
            </MultiSelect>
          )}
        </Tabs>
      </div>
    </section>
  )
}
