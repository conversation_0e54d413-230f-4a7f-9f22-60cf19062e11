import type { ColumnDef } from '@tanstack/react-table'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl } from '@/lib/assets'
import { Button } from '@/components/ui/button'
import { LinkIcon } from '@/assets/icons'
import type { mockMarketData } from '../mock'
import { useNavigate } from 'react-router'

export const usePositionMarketPositionsColumns = () => {
  const natigate = useNavigate()
  const columns: ColumnDef<(typeof mockMarketData)[number]>[] = [
    {
      id: 'market',
      header: 'Market',
      accessorKey: 'market',
      enableSorting: false
    },
    {
      id: 'netValue',
      header: 'Net Value',
      accessorKey: 'netValue',
      enableSorting: false
    },
    {
      id: 'collateral',
      header: 'Collateral',
      accessorKey: 'collateral',
      enableSorting: false,
      cell: () => {
        return (
          <div className="flex items-center gap-x-0.5">
            <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />
            <Avatar src={getIconUrl('usdc', 'svg')} alt="usdc-logo" size={18} />
            <Avatar src={getIconUrl('stSui')} alt="stsui-logo" size={18} />
          </div>
        )
      }
    },
    {
      id: 'debt',
      header: 'Debt',
      accessorKey: 'debt',
      enableSorting: false,
      cell: () => {
        return (
          <div className="flex items-center gap-x-0.5">
            <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />
            <Avatar src={getIconUrl('usdc', 'svg')} alt="usdc-logo" size={18} />
            <Avatar src={getIconUrl('stSui')} alt="stsui-logo" size={18} />
          </div>
        )
      }
    },
    {
      id: 'ltv',
      header: 'LTV',
      accessorKey: 'ltv',
      enableSorting: false,
      cell: ({ row }) => <span className="text-green">{row.original.ltv}</span>
    },
    {
      id: 'interestEarned',
      header: 'Interest Earned',
      accessorKey: 'interestEarned',
      enableSorting: false,
      cell: ({ row }) => (
        <span className="text-green">{row.original.interestEarned}</span>
      )
    },
    {
      id: 'netApy',
      header: 'Net APY',
      accessorKey: 'netApy',
      cell: ({ row }) => (
        <span className="text-green">{row.original.interestEarned}</span>
      )
    },
    {
      id: 'action',
      header: '',
      size: 20,
      cell: () => (
        <div className="w-full flex justify-end">
          <Button
            onClick={() => natigate('/market/dashboard/MainMarket')}
            variant="icon"
            className="w-5 h-5">
            <LinkIcon />
          </Button>
        </div>
      )
    }
  ]
  return { columns }
}
