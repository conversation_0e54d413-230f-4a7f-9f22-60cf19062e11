import type { ColumnDef } from '@tanstack/react-table'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl } from '@/lib/assets'
import { Button } from '@/components/ui/button'
import { LinkIcon } from '@/assets/icons'
import type { mockMultiplyPositionsData } from '../mock'
import { useNavigate } from 'react-router'

export const usePositionMultiplyColumns = () => {
  const navigate = useNavigate()
  const columns: ColumnDef<(typeof mockMultiplyPositionsData)[number]>[] = [
    {
      id: 'position',
      header: 'Position',
      accessorKey: 'position',
      enableSorting: false,
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-x-1.5">
            <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />
            <span>{row.original.position}</span>
          </div>
        )
      }
    },
    {
      id: 'netValue',
      header: 'Net Value',
      accessorKey: 'netValue',
      enableSorting: false
    },
    {
      id: 'pnl',
      header: 'pnl',
      accessorKey: 'pnl',
      enableSorting: false,
      cell: ({ row }) => <span className="text-green">{row.original.pnl}</span>
    },
    {
      id: 'ltv',
      header: 'LTV',
      accessorKey: 'ltv',
      enableSorting: false,
      cell: ({ row }) => <span className="text-green">{row.original.ltv}</span>
    },
    {
      id: 'netApy',
      header: 'Net APY',
      accessorKey: 'netApy',
      cell: ({ row }) => (
        <span className="text-green">{row.original.netApy}</span>
      )
    },
    {
      id: 'action',
      header: '',
      size: 20,
      cell: ({ row }) => (
        <div className="w-full flex justify-end">
          <Button
            variant="icon"
            className="w-5 h-5"
            onClick={() => navigate(`/multiply/${row.original.id}`)}>
            <LinkIcon />
          </Button>
        </div>
      )
    }
  ]
  return { columns }
}
