import type { ColumnDef } from '@tanstack/react-table'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl } from '@/lib/assets'
import type { mockAvailableRewardsData } from '../mock'
import { ClaimRewardsButton } from '@/components/dialogs'

export const useRewardsColumns = () => {
  const columns: ColumnDef<(typeof mockAvailableRewardsData)[number]>[] = [
    {
      id: 'asset',
      header: 'Asset',
      accessorKey: 'asset',
      enableSorting: false,
      size: 150,
      cell: () => {
        return (
          <div className="flex flex-col gap-y-1.5">
            <div className="flex items-center gap-x-1.5">
              <div className="relative mr-4">
                <Avatar src={getIconUrl('stSui')} alt="sui-logo" size={18} />
                <Avatar
                  src={getIconUrl('sui')}
                  alt="sui-logo"
                  size={18}
                  className="absolute left-4 top-0"
                />
              </div>
              <span>suiUSDT-USDC</span>
            </div>
            <div className="text-[10px] opacity-60 text-green flex items-center gap-x-1.5">
              <span>CETUS</span>
              <span>0.01%</span>
            </div>
          </div>
        )
      }
    },
    {
      id: 'rewardToken',
      header: 'Reward Token',
      accessorKey: 'rewardToken',
      enableSorting: false,
      size: 120,
      cell: () => {
        return (
          <div className="flex flex-col gap-y-1.5">
            <div className="flex items-center gap-x-1.5">
              <Avatar src={getIconUrl('stSui')} alt="sui-logo" size={18} />
              <span className="text-[10px]">SUI</span>
            </div>
            <div className="flex items-center gap-x-1.5">
              <Avatar src={getIconUrl('cetus')} alt="cetus-logo" size={18} />
              <span className="text-[10px]">CETUS</span>
            </div>
          </div>
        )
      }
    },
    {
      id: 'value',
      header: 'value',
      accessorKey: 'value',
      enableSorting: false,
      size: 120,
      cell: () => {
        return (
          <div className="flex flex-col gap-x-1.5 text-xs">
            <span>$39.21</span>
            <span>$9.21</span>
          </div>
        )
      }
    },

    {
      id: 'action',
      header: '',
      size: 40,
      cell: ({ row }) => (
        <div className="flex justify-end">
          <ClaimRewardsButton id={row.original.id} />
        </div>
      )
    }
  ]
  return { columns }
}
