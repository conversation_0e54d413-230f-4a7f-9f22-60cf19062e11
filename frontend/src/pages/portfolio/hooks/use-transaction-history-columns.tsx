import type { ColumnDef } from '@tanstack/react-table'
import type { mockTransactionHistoryData } from '../mock'
import { Button } from '@/components/ui/button'
import { WebIcon } from '@/assets/icons'
import { getIconUrl } from '@/lib/assets'
import { Avatar } from '@/components/ui/avatar'
import { TransactionHistoryTypeEnum } from '@/pages/leverage/mock'
import { Badge } from '@/components/ui/badge'

export const useTransactionsColumns = () => {
  const columns: ColumnDef<(typeof mockTransactionHistoryData)[number]>[] = [
    {
      id: 'date',
      header: 'Date',
      accessorKey: 'date'
    },
    {
      id: 'product',
      header: 'Product',
      accessorKey: 'product',
      cell: ({ row }) => {
        return (
          <Badge className="w-[100px]">
            <span className="opacity-80">{row.original.product}</span>
          </Badge>
        )
      }
    },
    {
      id: 'market',
      header: 'Market',
      accessorKey: 'market',
      cell: ({ row }) => {
        return (
          <Badge variant="blue" className="w-[100px]">
            {row.original.market}
          </Badge>
        )
      }
    },
    {
      id: 'actionType',
      header: 'Type',
      accessorKey: 'actionType',
      cell: ({ row }) => {
        const type = row.original.actionType
        const badgeVariant =
          type === TransactionHistoryTypeEnum.Withdraw ||
          type === TransactionHistoryTypeEnum.Borrow
            ? 'yellow'
            : 'green'
        return (
          <Badge className="w-[100px]" variant={badgeVariant}>
            {type}
          </Badge>
        )
      }
    },
    {
      id: 'asset',
      header: 'Asset',
      accessorKey: 'asset',
      cell: ({ row }) => {
        const asset = row.original.asset
        return (
          <div className="flex items-center gap-x-1.5">
            <Avatar src={getIconUrl('sui')} alt={asset} />
            <span>{asset}</span>
          </div>
        )
      }
    },
    {
      id: 'value',
      header: 'Value',
      accessorKey: 'value'
    },
    {
      id: 'hash',
      header: () => null,
      accessorKey: 'hash',
      size: 14,
      enableSorting: false,
      cell: ({ row }) => {
        const hash = row.original.hash
        return (
          <div className="flex items-center justify-end">
            <Button
              onClick={() => {
                window.open(hash, '_blank')
              }}
              variant="icon"
              className="h-[14px] w-[14px]">
              <WebIcon className="size-[14px]" />
            </Button>
          </div>
        )
      }
    }
  ]

  return {
    columns
  }
}
