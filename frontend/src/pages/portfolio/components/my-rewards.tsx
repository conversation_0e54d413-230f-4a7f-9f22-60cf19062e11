import { DataTable } from '@/components/data-table'
import { TableCard } from './table-card'
import { useRewardsColumns } from '../hooks/use-rewards-columns'
import { mockAvailableRewardsData } from '../mock'
import ClaimRewardsDiablog from '@/components/dialogs/claim-rewards-dialog'

export const MyRewards = () => {
  const { columns } = useRewardsColumns()
  return (
    <>
      <ClaimRewardsDiablog />
      <TableCard
        headerLeftLabel="Available Rewards"
        headerRightLabel="Total Value"
        headerRightValue="$48.42">
        <DataTable
          className="table-fixed w-full"
          classNames={{ headerCell: 'h-[44px] opacity-60' }}
          isLoading={false}
          columns={columns}
          data={mockAvailableRewardsData}
        />
      </TableCard>
    </>
  )
}
