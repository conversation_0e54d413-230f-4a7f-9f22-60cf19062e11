import { DataTable } from '@/components/data-table'
import { useTransactionsColumns } from '../hooks/use-transaction-history-columns'
import { mockTransactionHistoryData } from '../mock'

export const TransactionHistory = () => {
  const { columns } = useTransactionsColumns()
  return (
    <div className="h-[231px] overflow-y-auto">
      <DataTable
        size="md"
        classNames={{
          headerCell: 'px-3 h-[44px]',
          contentCell: 'px-3'
        }}
        columns={columns}
        isLoading={false}
        data={mockTransactionHistoryData}
      />
    </div>
  )
}
