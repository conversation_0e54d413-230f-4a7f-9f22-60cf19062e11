import { MarginTradeIcon, MarketIcon, MultiplyIcon } from '@/assets/icons'
import { PositionOverviewMenuEnum } from '../types'
import { AnchorMenu } from '@/components/anchor-menu'
import { usePositionMarketPositionsColumns } from '../hooks/use-position-columns'
import {
  mockMarginTradePositionsData,
  mockMarketData,
  mockMultiplyPositionsData
} from '../mock'
import { DataTable } from '@/components/data-table'
import { TableCard } from './table-card'
import { usePositionMultiplyColumns } from '../hooks/use-position-multiply-columns'
import { usePositionMarginTradeColumns } from '../hooks/use-position-margintrade-columns'
import { useNavigate } from 'react-router'

const menu = [
  {
    id: PositionOverviewMenuEnum.Market,
    label: PositionOverviewMenuEnum.Market,
    icon: <MarketIcon className="size-[15px]" />
  },
  {
    id: PositionOverviewMenuEnum.Multiply,
    label: PositionOverviewMenuEnum.Multiply,
    icon: <MultiplyIcon className="size-[15px]" />
  },
  {
    id: PositionOverviewMenuEnum.MarginTrade,
    label: PositionOverviewMenuEnum.MarginTrade,
    icon: <MarginTradeIcon className="size-[15px]" />
  }
]

export const PositionOverview = () => {
  return (
    <AnchorMenu
      menu={menu}
      defaultActiveMenuId={PositionOverviewMenuEnum.Market}
      offsetTop={84}
      menuClassName="w-[200px] mr-3">
      <div className="flex-1 flex flex-col gap-y-3">
        <div id={PositionOverviewMenuEnum.Market}>
          <TableCard
            headerLeftLabel="Market Positions"
            headerRightLabel="Net Value"
            headerRightValue="$8,382.12">
            <MarketPositionsTable />
          </TableCard>
        </div>
        <div id={PositionOverviewMenuEnum.Multiply}>
          <TableCard
            headerLeftLabel="Multiply"
            headerRightLabel="Net Value"
            headerRightValue="$8,382.12">
            <MultiplyPositionsTable />
          </TableCard>
        </div>
        <div id={PositionOverviewMenuEnum.MarginTrade}>
          <TableCard
            headerLeftLabel="Margin Trade"
            headerRightLabel="Net Value"
            headerRightValue="$8,382.12">
            <MarginTradePositionsTable />
          </TableCard>
        </div>
      </div>
    </AnchorMenu>
  )
}

const MarketPositionsTable = () => {
  const navigate = useNavigate()
  const { columns } = usePositionMarketPositionsColumns()
  return (
    <DataTable
      onPressRow={() => navigate('/market/dashboard/MainMarket')}
      classNames={{
        headerCell: 'h-[44px] opacity-60',
        sortableHeaderCell: 'opacity-100'
      }}
      data={mockMarketData}
      columns={columns}
      isLoading={false}
    />
  )
}

const MultiplyPositionsTable = () => {
  const navigate = useNavigate()
  const { columns } = usePositionMultiplyColumns()
  return (
    <DataTable
      onPressRow={(row) => navigate(`/multiply/${row.original.id}`)}
      classNames={{
        headerCell: 'h-[44px] opacity-60',
        sortableHeaderCell: 'opacity-100'
      }}
      data={mockMultiplyPositionsData}
      columns={columns}
      isLoading={false}
    />
  )
}

const MarginTradePositionsTable = () => {
  const navigate = useNavigate()
  const { columns } = usePositionMarginTradeColumns()
  return (
    <DataTable
      onPressRow={() => navigate('/margin')}
      classNames={{
        headerCell: 'h-[44px] opacity-60',
        sortableHeaderCell: 'opacity-100'
      }}
      data={mockMarginTradePositionsData}
      columns={columns}
      isLoading={false}
    />
  )
}
