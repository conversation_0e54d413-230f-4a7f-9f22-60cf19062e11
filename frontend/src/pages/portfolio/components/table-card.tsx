export const TableCard: React.FC<{
  headerLeftLabel: string
  headerRightLabel: string
  headerRightValue: string
  children: React.ReactElement
}> = ({ children, headerLeftLabel, headerRightLabel, headerRightValue }) => {
  return (
    <div className="border border-border-8 rounded-[10px] bg-overview-gradient flex flex-col gap-y-3">
      <div className="h-[66px] px-6 flex items-center justify-between border-b border-border-8">
        <span>{headerLeftLabel}</span>
        <span className="flex items-center gap-x-1.5">
          <span className="opacity-60">{headerRightLabel}</span>
          <span>{headerRightValue}</span>
        </span>
      </div>

      {children}
    </div>
  )
}
