import { mockPortfolioData } from '../mock'

export const Overview = () => {
  return (
    <div className="border border-border-8 rounded-[10px] grid grid-cols-4 py-3 bg-overview-gradient">
      {mockPortfolioData.map((item) => (
        <div
          key={item.id}
          className="flex py-3 border-r last:border-r-0 border-border-8 items-center justify-center">
          <div className="flex flex-col gap-y-3 items-center">
            <span className="text-sm">{item.label}</span>
            <span className="text-base">{item.value}</span>
          </div>
        </div>
      ))}
    </div>
  )
}
