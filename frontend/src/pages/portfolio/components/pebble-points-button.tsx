import { ChevronRightIcon } from '@/assets/icons'
import { Button } from '@/components/ui/button'
import { useDrawerStore } from '@/store/drawer-store'

export const PebblePiontsButton: React.FC = () => {
  const { open } = useDrawerStore((state) => state.points)
  return (
    <Button
      onClick={open}
      variant="icon"
      className="rounded-full overflow-hidden">
      <ChevronRightIcon className="w-[13px] h-[13px]" />
    </Button>
  )
}
