import { BaseChart } from '@/components/chart/base-chart'
import { RangeSwitcher } from '@/components/chart/range-switcher'
import { mockMarketDayData } from '@/pages/market/mock'
import { ChartRangeEnum } from '@/components/chart/types'
import { useState } from 'react'

export const InterestRateModelChart = () => {
  const [currentRange, setCurrentRange] = useState(ChartRangeEnum.Day7)

  return (
    <div className="border border-border-8 rounded-[10px] bg-overview-gradient px-3 pb-3 flex flex-col gap-y-3">
      <div className="px-3 py-1.5 border-b border-border-8 flex items-center justify-between">
        <span>Interest Rate Model</span>
        <RangeSwitcher
          value={currentRange}
          onRangeChange={(value) => {
            setCurrentRange(value)
          }}
        />
      </div>

      <div className="px-11">
        <BaseChart
          seriesData={[
            {
              data: mockMarketDayData,
              options: {
                topColor: '#FFFFFF4D',
                bottomColor: '#235B8800'
              }
            }
          ]}
          className="h-[229px]"
        />
      </div>
    </div>
  )
}
