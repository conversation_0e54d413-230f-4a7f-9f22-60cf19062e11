import { BackButton } from '@/components/buttons'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl } from '@/lib/assets'

export const MultiplyDetailTitle = () => (
  <div className="relative flex items-center justify-between">
    <div className="flex items-center gap-x-1.5">
      <div className="relative mr-4">
        <Avatar src={getIconUrl('stSui')} alt="sui-logo" size={24} />
        <Avatar
          src={getIconUrl('sui')}
          alt="sui-logo"
          size={24}
          className="absolute left-4 top-0"
        />
      </div>
      <span className="text-2xl text-white">stSUI/SUI Multiply</span>
    </div>

    <div className="absolute top-0 right-[calc(100%+12px)] h-8 w-8">
      <BackButton className="h-8 w-8" />
    </div>
  </div>
)
