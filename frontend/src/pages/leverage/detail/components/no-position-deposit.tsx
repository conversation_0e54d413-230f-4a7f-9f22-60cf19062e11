import { Slider } from '@/components/ui/slider'
import {
  mockLeverageDetailDepositData,
  mockNoPositionLeverageDetailPredictionData
} from '../../mock'
import { Button } from '@/components/ui/button'
import { SettingIcon } from '@/assets/icons'
import { useState } from 'react'
import { BalanceInput } from '@/components/balance-input'
import { DepositCollapsibleItem } from '@/components/deposit-collapsible-item'

export const NoPositionDeposit = () => {
  const [balanceInputValue, setBalanceInputValue] = useState<string>()
  const [multiplier, setMultiplier] = useState<number>(
    mockLeverageDetailDepositData.leverageMin
  )

  const renderPrediction = () => {
    if (
      !balanceInputValue ||
      isNaN(Number(balanceInputValue)) ||
      !multiplier ||
      isNaN(Number(multiplier))
    )
      return null

    return (
      <div className="flex flex-col gap-y-3 text-xs">
        <span className="flex items-center justify-between">
          <span className="opacity-60">Transaction Settings</span>
          <Button variant="icon" className="h-4!">
            <SettingIcon className="size-[18px]" />
          </Button>
        </span>
        {mockNoPositionLeverageDetailPredictionData.map((item) => (
          <DepositCollapsibleItem key={item.id} data={item} />
        ))}
      </div>
    )
  }

  return (
    <div className="border border-border-8 pt-0 rounded-[10px] w-[377px] h-full flex flex-col gap-y-6 bg-deposit-gradient">
      <div className="py-3 border-b border-border-5 px-6">Deposit</div>
      <div className="px-6 pb-6 flex flex-col gap-y-6">
        <BalanceInput
          value={balanceInputValue}
          onChange={(value) => setBalanceInputValue(value)}
          tokens={mockLeverageDetailDepositData.tokens}
          title="Deposit asset"
        />
        <div className="flex flex-col">
          <div className="mb-5">Multiply</div>
          <Slider
            value={[multiplier ?? 0]}
            onValueChange={(value) => setMultiplier(value[0])}
            min={mockLeverageDetailDepositData.leverageMin}
            max={mockLeverageDetailDepositData.leverageMax}
            step={0.1}
            className="w-full"
          />
          <div className="mt-3 text-xs flex items-center justify-between">
            <span>{mockLeverageDetailDepositData.leverageMin}x</span>
            <span>{mockLeverageDetailDepositData.leverageMax}x</span>
          </div>
        </div>
        <Button variant="secondary">Setup Account</Button>
        {renderPrediction()}
      </div>
    </div>
  )
}
