import { cn } from '@/lib/utils'
import { useMemo } from 'react'
import { LeverageDetailTabEnum } from '../../types'
import {
  mockLeverageDetailMyPositionOverviewData,
  mockLeverageDetailOverviewData
} from '../../mock'

export const Overview: React.FC<{ tab: LeverageDetailTabEnum }> = ({ tab }) => {
  const overviewData = useMemo(() => {
    if (tab === LeverageDetailTabEnum.MyPosition)
      return mockLeverageDetailMyPositionOverviewData
    return mockLeverageDetailOverviewData
  }, [tab])

  return (
    <div
      className={cn(
        'border border-border-8 rounded-[10px] items-center overflow-hidden grid w-full py-3 bg-overview-gradient',
        tab === LeverageDetailTabEnum.MyPosition
          ? 'grid-cols-4'
          : 'grid-cols-[260px_repeat(4,1fr)]'
      )}>
      {overviewData.map((item) => (
        <div
          key={item.id}
          className="flex items-center border-r-1 border-border-8 last:border-r-0 h-full justify-center py-3">
          <div className="flex flex-col items-center gap-y-3">
            <div className="text-sm opacity-60 text-nowrap">{item.label}</div>
            <div>{item.value}</div>
          </div>
        </div>
      ))}
    </div>
  )
}
