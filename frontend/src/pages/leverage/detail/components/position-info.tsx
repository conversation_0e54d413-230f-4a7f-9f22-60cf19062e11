import chunk from 'lodash/chunk'
import { mockLeverageDetailMyPositionData } from '../../mock'
import { getIllustrationUrl } from '@/lib/assets'

export const PositionInfo = ({ hasPosition }: { hasPosition: boolean }) => {
  const isEmpty = !hasPosition
  const [trunk1, trunk2] = chunk(mockLeverageDetailMyPositionData, 5)

  return (
    <div
      style={{ height: isEmpty ? 340 : 'fit-content' }}
      className="border relative border-border-8 rounded-[10px] flex-1 size-full bg-vault-info-gradient w-[725px] flex flex-col">
      <div className="border-b border-border-5 p-3 px-6">My Position Info</div>
      {isEmpty ? (
        <>
          <div className="flex-1 size-full flex items-center justify-center">
            <div className="flex items-center gap-x-4 justify-between">
              <div className="h-[1px] w-[52px] bg-foreground opacity-10" />
              <span className="opacity-30 text-center text-sm">
                You have no position yet
              </span>
              <div className="h-[1px] w-[52px] bg-foreground opacity-10" />
            </div>
          </div>
          <img
            src={getIllustrationUrl('charts-info-bg-layer', 'svg')}
            alt="charts-info-bg-layer"
            className="absolute top-0 left-0 size-full z-0 no-drag"
          />
        </>
      ) : (
        <div className="flex-1 flex items-center justify-between text-sm">
          <div className="flex flex-1 items-center justify-between p-6">
            <div className="flex flex-col items-start gap-y-[14px]">
              {trunk1.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between w-[282px]">
                  <span className="opacity-60">{item.label}</span>
                  <span className="text-white">{item.value}</span>
                </div>
              ))}
            </div>
            <div className="flex flex-col items-center gap-y-[14px]">
              {trunk2.map((item) => (
                <div
                  key={item.id}
                  className="flex items-center justify-between w-[282px]">
                  <span className="opacity-60">{item.label}</span>
                  <span className="text-white">{item.value}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
