import { Button } from '@/components/ui/button'
import { useMemo, useState } from 'react'
import {
  mockHasPositionLeverageDetailPredictionAdjustMultiplierData,
  mockHasPositionLeverageDetailPredictionDepositData,
  mockHasPositionLeverageDetailPredictionManageCollateralData,
  mockHasPositionLeverageDetailPredictionWithdrawData,
  mockLeverageDetailDepositData
} from '../../mock'
import { Slider } from '@/components/ui/slider'
import { DashboardRectIcon, SettingIcon } from '@/assets/icons'
import { BalanceInput } from '@/components/balance-input'
import {
  LeveragePositionActionEnum,
  ManageLeverageDepositManageCollateralTabEnum,
  ManageLeverageDepositManageDebtTabEnum
} from '../../types'
import { DepositCollapsibleItem } from '@/components/deposit-collapsible-item'
import { enumToArray } from '@/lib/enum'
import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useNavigate, useParams } from 'react-router'

export const ManagePositionDeposit = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const [managePositionAction, setManagePositionAction] =
    useState<LeveragePositionActionEnum>(LeveragePositionActionEnum.Deposit)
  const [manageCollateralTab, setManageCollateralTab] =
    useState<ManageLeverageDepositManageCollateralTabEnum>(
      ManageLeverageDepositManageCollateralTabEnum.AddCollateral
    )
  const [manageDebtTab, setManageDebtTab] =
    useState<ManageLeverageDepositManageDebtTabEnum>(
      ManageLeverageDepositManageDebtTabEnum.BorrowMore
    )
  const [balanceInputValue, setBalanceInputValue] = useState<string>()
  const [multiplier, setMultiplier] = useState<number>(
    mockLeverageDetailDepositData.leverageMin
  )

  const renderTabs = () => {
    if (managePositionAction === LeveragePositionActionEnum.ManageCollateral) {
      return (
        <div className="mb-6 px-6">
          <Tabs
            value={manageCollateralTab}
            orientation="vertical"
            onValueChange={(value) =>
              setManageCollateralTab(
                value as ManageLeverageDepositManageCollateralTabEnum
              )
            }>
            <TabsList
              className="pl-0 w-full grid grid-cols-2"
              aria-label="deposit tabs">
              <TabsTrigger
                value={
                  ManageLeverageDepositManageCollateralTabEnum.AddCollateral
                }>
                {ManageLeverageDepositManageCollateralTabEnum.AddCollateral}
              </TabsTrigger>
              <TabsTrigger
                value={
                  ManageLeverageDepositManageCollateralTabEnum.RemoveCollateral
                }>
                {ManageLeverageDepositManageCollateralTabEnum.RemoveCollateral}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      )
    }

    if (managePositionAction === LeveragePositionActionEnum.ManageDebt) {
      return (
        <div className="mb-6 px-6">
          <Tabs
            value={manageDebtTab}
            orientation="vertical"
            onValueChange={(value) =>
              setManageDebtTab(value as ManageLeverageDepositManageDebtTabEnum)
            }>
            <TabsList
              className="pl-0 w-full grid grid-cols-2"
              aria-label="deposit tabs">
              <TabsTrigger
                value={ManageLeverageDepositManageDebtTabEnum.BorrowMore}
                onClick={() =>
                  setManageDebtTab(
                    ManageLeverageDepositManageDebtTabEnum.BorrowMore
                  )
                }>
                {ManageLeverageDepositManageDebtTabEnum.BorrowMore}
              </TabsTrigger>
              <TabsTrigger
                value={ManageLeverageDepositManageDebtTabEnum.RepayDebt}
                onClick={() =>
                  setManageDebtTab(
                    ManageLeverageDepositManageDebtTabEnum.RepayDebt
                  )
                }>
                {ManageLeverageDepositManageDebtTabEnum.RepayDebt}
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      )
    }

    return null
  }

  const renderBalanceInput = () => {
    if (
      managePositionAction !== LeveragePositionActionEnum.Deposit &&
      managePositionAction !== LeveragePositionActionEnum.ManageCollateral &&
      managePositionAction !== LeveragePositionActionEnum.ManageDebt &&
      managePositionAction !== LeveragePositionActionEnum.Withdraw
    )
      return null

    const getTitle = () => {
      if (managePositionAction === LeveragePositionActionEnum.Deposit)
        return 'You deposit'
      if (managePositionAction === LeveragePositionActionEnum.ManageCollateral)
        return manageCollateralTab ===
          ManageLeverageDepositManageCollateralTabEnum.AddCollateral
          ? 'You deposit'
          : 'You withdraw'
      if (managePositionAction === LeveragePositionActionEnum.ManageDebt)
        return manageDebtTab ===
          ManageLeverageDepositManageDebtTabEnum.BorrowMore
          ? 'You borrow'
          : 'You repay'
      if (managePositionAction === LeveragePositionActionEnum.Withdraw)
        return 'You withdraw'
    }

    return (
      <BalanceInput
        value={balanceInputValue}
        onChange={(value) => setBalanceInputValue(value)}
        title={getTitle()}
        tokens={mockLeverageDetailDepositData.tokens}
      />
    )
  }

  const renderLeverageSettings = () => {
    if (managePositionAction !== LeveragePositionActionEnum.AdjustMultiplier)
      return null

    return (
      <div className="flex flex-col">
        <div className="mb-5">Multiply</div>
        <Slider
          value={[multiplier ?? 0]}
          onValueChange={(value) => setMultiplier(value[0])}
          min={mockLeverageDetailDepositData.leverageMin}
          max={mockLeverageDetailDepositData.leverageMax}
          step={0.1}
          className="w-full"
        />
        <div className="mt-3 text-xs flex items-center justify-between">
          <span>{mockLeverageDetailDepositData.leverageMin}x</span>
          <span>{mockLeverageDetailDepositData.leverageMax}x</span>
        </div>
      </div>
    )
  }

  const shouldShowPrediction = useMemo(() => {
    if (
      managePositionAction === LeveragePositionActionEnum.AdjustMultiplier &&
      (!multiplier || isNaN(Number(multiplier)))
    )
      return false
    if (
      managePositionAction === LeveragePositionActionEnum.Deposit ||
      managePositionAction === LeveragePositionActionEnum.Withdraw ||
      managePositionAction === LeveragePositionActionEnum.ManageCollateral ||
      managePositionAction === LeveragePositionActionEnum.ManageDebt
    )
      return !!balanceInputValue && !isNaN(Number(balanceInputValue))

    return true
  }, [balanceInputValue, managePositionAction, multiplier])

  const predictionData = useMemo(() => {
    switch (managePositionAction) {
      case LeveragePositionActionEnum.Deposit:
        return mockHasPositionLeverageDetailPredictionDepositData
      case LeveragePositionActionEnum.ManageCollateral:
        return mockHasPositionLeverageDetailPredictionManageCollateralData
      case LeveragePositionActionEnum.ManageDebt:
        return mockHasPositionLeverageDetailPredictionManageCollateralData
      case LeveragePositionActionEnum.AdjustMultiplier:
        return mockHasPositionLeverageDetailPredictionAdjustMultiplierData
      case LeveragePositionActionEnum.Withdraw:
        return mockHasPositionLeverageDetailPredictionWithdrawData
      default:
        return []
    }
  }, [managePositionAction])

  const renderPrediction = () => {
    if (!shouldShowPrediction) return null

    return (
      <div className="flex flex-col gap-y-3 text-xs">
        <span className="flex items-center justify-between">
          <span className="opacity-60">Transaction Settings</span>
          <Button variant="icon" className="h-4!">
            <SettingIcon className="size-[18px]" />
          </Button>
        </span>
        {predictionData.map((item) => (
          <DepositCollapsibleItem key={item.id} data={item} />
        ))}
      </div>
    )
  }

  const confirmButtonLabel = useMemo(() => {
    if (managePositionAction === LeveragePositionActionEnum.ManageCollateral) {
      if (
        manageCollateralTab ===
        ManageLeverageDepositManageCollateralTabEnum.AddCollateral
      )
        return 'Deposit'
      if (
        manageCollateralTab ===
        ManageLeverageDepositManageCollateralTabEnum.RemoveCollateral
      )
        return 'Withdraw'
    }
    if (managePositionAction === LeveragePositionActionEnum.ManageDebt) {
      if (manageDebtTab === ManageLeverageDepositManageDebtTabEnum.BorrowMore)
        return 'Borrow'
      if (manageDebtTab === ManageLeverageDepositManageDebtTabEnum.RepayDebt)
        return 'Repay'
    }
    if (managePositionAction === LeveragePositionActionEnum.Deposit)
      return 'Deposit'
    if (managePositionAction === LeveragePositionActionEnum.AdjustMultiplier)
      return 'Adjust'
    if (managePositionAction === LeveragePositionActionEnum.Withdraw)
      return 'Withdraw'
    if (managePositionAction === LeveragePositionActionEnum.ManageDebt)
      return 'Close Position'
  }, [managePositionAction, manageCollateralTab, manageDebtTab])

  return (
    <div className="border border-border-8 pt-0 rounded-[10px] w-[377px] h-full flex flex-col bg-deposit-gradient">
      <div
        className={cn(
          'mb-6 px-6 py-3 border-b border-border-5',
          (managePositionAction ===
            LeveragePositionActionEnum.ManageCollateral ||
            managePositionAction === LeveragePositionActionEnum.ManageDebt) &&
            'mb-3'
        )}>
        <Select
          value={managePositionAction}
          onValueChange={(key) => {
            if (key === 'dashboard') {
              navigate(`/multiply/dashboard/${id}`)
              return
            }
            setManagePositionAction(key as LeveragePositionActionEnum)
          }}>
          <SelectTrigger className="text-sm">
            <SelectValue>{managePositionAction}</SelectValue>
          </SelectTrigger>
          <SelectContent className="rounded-md">
            {enumToArray(LeveragePositionActionEnum).map(({ key, value }) => (
              <SelectItem className="w-[165px]" key={key} value={value}>
                {value}
              </SelectItem>
            ))}
            <SelectItem
              onClick={() => {}}
              className="w-[165px] gap-x-1.5"
              key="dashboard"
              value="dashboard">
              <DashboardRectIcon />
              Dashboard
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      {renderTabs()}
      <div className="px-6 pb-6 flex flex-col gap-y-6">
        {renderBalanceInput()}
        {renderLeverageSettings()}
        <Button variant="secondary">{confirmButtonLabel}</Button>
        {renderPrediction()}
      </div>
    </div>
  )
}
