import { getIllustrationUrl } from '@/lib/assets'
import { mockLeverageDetailMoreVaultInfoData } from '../../mock'

export const VaultInfo = () => (
  <div className="flex flex-col gap-y-6 w-[725px]">
    <div className="border border-border-8 rounded-[10px] bg-vault-info-gradient">
      <div className="border-b border-border-5 p-3 px-6">More Vault Info</div>
      <div className="py-6 px-6 flex items-center justify-between text-sm">
        {mockLeverageDetailMoreVaultInfoData.map((item) => (
          <span key={item.id} className="flex items-center gap-x-3">
            <span className="opacity-60">{item.label}:</span>{' '}
            <span className="opacity-100">{item.value}</span>
          </span>
        ))}
      </div>
    </div>
    <div className="border relative border-border-8 rounded-[10px] h-[316px] bg-vault-info-gradient flex flex-col">
      <div className="border-b border-border-5 p-3 px-6">Charts & Info</div>
      <div className="flex flex-1 items-center justify-center h-full">
        <div className="flex items-center gap-x-4">
          <div className="h-[1px] w-[52px] bg-foreground opacity-10" />
          <span className="opacity-30 text-center">Coming soon</span>
          <div className="h-[1px] w-[52px] bg-foreground opacity-10" />
        </div>
      </div>
      <img
        src={getIllustrationUrl('charts-info-bg-layer', 'svg')}
        alt="charts-info-bg-layer"
        className="absolute top-0 left-0 size-full z-0 no-drag"
      />
    </div>
  </div>
)
