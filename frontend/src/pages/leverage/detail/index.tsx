import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { LeverageDetailTabEnum } from '../types'
import { Overview } from './components/overview'
import { MultiplyDetailTitle } from './components/multiply-detail-title'
import { VaultInfo } from './components/vault-info'
import { PositionInfo } from './components/position-info'
import { ManagePositionDeposit } from './components/manage-position-deposit'
import { NoPositionDeposit } from './components/no-position-deposit'
import { mockLeverageData } from '../mock'
import { useParams } from 'react-router'

export default function LeverageDetail() {
  const { id } = useParams()
  const [tab, setTab] = useState<LeverageDetailTabEnum>(
    LeverageDetailTabEnum.Overview
  )
  const hasPosition = !!mockLeverageData.find((item) => item.id === id)
    ?.hasPosition

  return (
    <section className="relative mt-[90px] pb-10 max-w-6xl flex flex-col gap-y-6">
      <MultiplyDetailTitle />
      <Tabs
        value={tab}
        onValueChange={(item) => {
          setTab(item as LeverageDetailTabEnum)
        }}
        orientation="vertical">
        <TabsList className="pl-0" aria-label="market tabs">
          <TabsTrigger value={LeverageDetailTabEnum.Overview}>
            {LeverageDetailTabEnum.Overview}
          </TabsTrigger>
          <TabsTrigger value={LeverageDetailTabEnum.MyPosition}>
            {LeverageDetailTabEnum.MyPosition}
          </TabsTrigger>
        </TabsList>
      </Tabs>
      <Overview tab={tab} />
      <div className="flex gap-x-6">
        {tab === LeverageDetailTabEnum.Overview && <VaultInfo />}
        {tab === LeverageDetailTabEnum.MyPosition && (
          <PositionInfo hasPosition={hasPosition} />
        )}
        {hasPosition ? <ManagePositionDeposit /> : <NoPositionDeposit />}
      </div>
    </section>
  )
}
