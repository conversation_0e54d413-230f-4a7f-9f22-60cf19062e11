export type LeverageItem = {
  id: string
  strategy: string
  maxApy: string
  multiplier: string
  liquidity: string
  supplied: string
  borrowing: string
  hasPosition: boolean
}

export type LeverageOverviewItem = {
  id: string
  strategy: string
  netValue: string
  netApy: string
  pnL: string
  multiplier: string
  ltv: string
}

export type MainMarketItem = {
  id: string
  assets: string
  totalSupply: string
  totalBorrow: string
  liquidityLtv: string
  supplyApy: string
  borrowApy: string
}

export enum SuppliedTableActionEnum {
  Supply = 'Supply',
  Withdraw = 'Withdraw'
}

export enum BorrowedTableActionEnum {
  RepayFromWallet = 'Repay From Wallet',
  RepayWithCollateral = 'Repay With Collateral',
  Borrow = 'Borrow'
}

export enum LeverageDetailTabEnum {
  Overview = 'Overview',
  MyPosition = 'My Position'
}

export enum LeveragePositionActionEnum {
  Deposit = 'Deposit',
  ManageCollateral = 'Manage Collateral',
  ManageDebt = 'Manage Debt',
  AdjustMultiplier = 'Adjust Multiplier',
  Withdraw = 'Withdraw'
}

export enum ManageLeverageDepositManageCollateralTabEnum {
  AddCollateral = 'Add Collateral',
  RemoveCollateral = 'Remove Collateral'
}

export enum ManageLeverageDepositManageDebtTabEnum {
  BorrowMore = 'Borrow More',
  RepayDebt = 'Repay Debt'
}
