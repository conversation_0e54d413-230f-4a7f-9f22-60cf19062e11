import type { ColumnDef } from '@tanstack/react-table'
import type { LeverageItem } from '../types'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl } from '@/lib/assets'
import { Button } from '@/components/ui/button'
import { useNavigate } from 'react-router'

const SPACEING = 80.67

export const useLeverageColumns = () => {
  const navigate = useNavigate()

  const columns: ColumnDef<LeverageItem>[] = [
    {
      id: 'strategy',
      header: 'Strategy',
      accessorKey: 'strategy',
      size: 120 + SPACEING + 24,
      cell: ({ row }) => (
        <div className="flex items-center gap-1.5">
          <Avatar src={getIconUrl('stSui')} alt={row.original.strategy} />
          <span>{row.original.strategy}</span>
        </div>
      )
    },
    {
      id: 'maxApy',
      header: 'Max APY',
      accessorKey: 'maxApy',
      size: 100 + SPACEING,
      cell: ({ row }) => (
        <span className="text-sm text-green">{row.original.maxApy}</span>
      )
    },
    {
      id: 'multiplier',
      header: 'Multiplier',
      accessorKey: 'multiplier',
      size: 80 + SPACEING
    },
    {
      id: 'liquidity',
      header: 'Liquidity',
      accessorKey: 'liquidity',
      size: 80 + SPACEING
    },
    {
      id: 'supplied',
      header: 'Supplied',
      accessorKey: 'supplied',
      size: 80 + SPACEING
    },
    {
      id: 'borrowing',
      header: 'Borrowing',
      accessorKey: 'borrowing',
      size: 60 + SPACEING,
      cell: ({ row }) => (
        <div className="flex items-center gap-1.5">
          <Avatar src={getIconUrl('sui')} alt={row.original.borrowing} />
          <span className="text-sm">{row.original.borrowing}</span>
        </div>
      )
    },
    {
      id: 'deposit',
      header: '',
      accessorKey: 'deposit',
      enableSorting: false,
      size: 124 + 24,
      cell: ({ row }) => (
        <div className="flex justify-end">
          <Button
            className="w-[124px] h-[42px]"
            onClick={() => {
              navigate(row.original.id)
            }}>
            {row.original.hasPosition ? 'Manage' : 'Deposit'}
          </Button>
        </div>
      )
    }
  ]
  return { columns }
}
