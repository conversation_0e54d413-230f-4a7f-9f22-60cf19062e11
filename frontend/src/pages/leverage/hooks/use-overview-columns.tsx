import type { ColumnDef } from '@tanstack/react-table'
import type { LeverageOverviewItem } from '../types'
import { Avatar } from '@/components/ui/avatar'
import { getIconUrl } from '@/lib/assets'
import { Button } from '@/components/ui/button'
import { useNavigate } from 'react-router'
import { ChevronRightIcon } from '@/assets/icons'

const SPACEING = 98.67

export const useOverviewColumns = () => {
  const navigate = useNavigate()

  const columns: ColumnDef<LeverageOverviewItem>[] = [
    {
      id: 'strategy',
      header: 'Strategy',
      accessorKey: 'strategy',
      size: 100 + SPACEING + 24,
      cell: ({ row }) => (
        <div className="flex items-center gap-1.5">
          <Avatar src={getIconUrl('stSui')} alt={row.original.strategy} />
          <span className="text-xs">{row.original.strategy}</span>
        </div>
      )
    },
    {
      id: 'netValue',
      header: 'Net Value',
      accessorKey: 'netValue',
      size: 140 + SPACEING,
      cell: ({ row }) => (
        <span className="text-xs font-medium underline">
          {row.original.netValue}
        </span>
      )
    },
    {
      id: 'netApy',
      header: 'Net APY',
      accessorKey: 'netApy',
      size: 80 + SPACEING,
      cell: ({ row }) => (
        <span className="text-xs text-green">{row.original.netApy}</span>
      )
    },
    {
      id: 'PnL',
      header: 'PnL',
      accessorKey: 'pnL',
      size: 100 + SPACEING,
      cell: ({ row }) => (
        <span className="text-xs text-green">{row.original.pnL}</span>
      )
    },
    {
      id: 'multiplier',
      header: 'Multiplier',
      accessorKey: 'multiplier',
      size: 60 + SPACEING,
      cell: ({ row }) => (
        <span className="text-xs text-green">{row.original.multiplier}</span>
      )
    },
    {
      id: 'ltv',
      header: 'LTV',
      accessorKey: 'ltv',
      size: 40 + SPACEING,
      cell: ({ row }) => (
        <span className="text-xs text-green">{row.original.ltv}</span>
      )
    },
    {
      id: 'navigate',
      header: '',
      accessorKey: 'navigate',
      enableSorting: false,
      size: 16 + 24,
      cell: ({ row }) => (
        <div className="flex justify-end">
          <Button
            variant="icon"
            className="size-4"
            onClick={() => {
              navigate(row.original.id)
            }}>
            <ChevronRightIcon className="size-4" />
          </Button>
        </div>
      )
    }
  ]
  return { columns }
}
