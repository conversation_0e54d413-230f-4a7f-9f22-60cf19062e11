import { Avatar } from '@/components/ui/avatar'
import type { LeverageItem } from './types'
import { getIconUrl } from '@/lib/assets'
import { SUI_COIN_TYPE } from '@/lib/coin'
import { SpinIcon } from '@/assets/icons'

export const mockLeverageData: LeverageItem[] = [
  {
    id: '1',
    strategy: 'StSui',
    maxApy: '45.2%',
    multiplier: '3.5x',
    liquidity: '$2.4M',
    supplied: '$150K',
    borrowing: 'SUI',
    hasPosition: true
  },
  {
    id: '2',
    strategy: 'StEth',
    maxApy: '38.7%',
    multiplier: '2.8x',
    liquidity: '$1.8M',
    supplied: '$200K',
    borrowing: 'SUI',
    hasPosition: false
  },
  {
    id: '3',
    strategy: 'StBtc',
    maxApy: '52.1%',
    multiplier: '4.2x',
    liquidity: '$3.2M',
    supplied: '$300K',
    borrowing: 'SUI',
    hasPosition: false
  },
  {
    id: '4',
    strategy: 'StSol',
    maxApy: '41.3%',
    multiplier: '3.1x',
    liquidity: '$1.5M',
    supplied: '$100K',
    borrowing: 'SUI',
    hasPosition: false
  }
]

export const mockLeverageDetailOverviewData = [
  {
    id: 'liquidity',
    label: 'SUI Liquidity Available',
    value: (
      <span className="flex items-center gap-x-1.5">
        <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />
        <span>3,891.29</span>
      </span>
    )
  },
  {
    id: 'maxAPY',
    label: 'Max APY',
    value: <span className="text-green">2.8%</span>
  },
  {
    id: 'maxMultiplier',
    label: 'Max Multiplier',
    value: '7.7x'
  },
  {
    id: 'avgMultiplier',
    label: 'Avg. Multiplier',
    value: '4.34x'
  },
  {
    id: 'totalSupply',
    label: 'Total XXX Supplied',
    value: '80.28M'
  }
]

export const mockLeverageDetailMyPositionOverviewData = [
  {
    id: 'netValue',
    label: 'Net Value',
    value: '$4893.1'
  },
  {
    id: 'suiEarned',
    label: 'SUI Earned',
    value: (
      <span className="flex items-center gap-x-3">
        <Avatar src={getIconUrl('sui')} alt="sui-logo" size={18} />
        <span>1.1</span>
      </span>
    )
  },
  {
    id: 'netAPY',
    label: 'Net APY',
    value: <span className="text-green">2.8%</span>
  },
  {
    id: 'myMultiplier',
    label: 'My Leverage',
    value: '4.34x'
  }
]

export const mockLeverageDetailMyPositionData = [
  { id: 'collateralDebt', label: 'Collateral/Debt', value: 'stSUI/SUI' },
  { id: 'totalDeposits', label: 'Total Deposits', value: '$2,839.19' },
  { id: 'currentNetValue', label: 'Current Net Value', value: '$2,239.19' },
  {
    id: 'suiEarned',
    label: 'SUI Earned',
    value: <span className="text-green">1.1</span>
  },
  { id: 'liquidationPrice', label: 'Liquidation Price', value: '213.13' },
  { id: 'liquidationPrice1', label: 'Liquidation Price', value: '213.13' },
  { id: 'stSuiSupplied', label: 'stSUI supplied', value: '3.13' },
  { id: 'suiBorrowed', label: 'SUI borrowed', value: '213.13' },
  {
    id: 'positionLTV',
    label: 'Position LTV',
    value: <span className="text-green">50%</span>
  },
  { id: 'liquidationLTV', label: 'Liquidation LTV', value: '80%' }
]

export const mockLeverageDetailMoreVaultInfoData = [
  {
    id: 'maxLtv',
    label: 'Max LTV',
    value: '80%'
  },
  {
    id: 'borrowRate',
    label: 'SUI Borrow Rate',
    value: '3.53%'
  },
  {
    id: 'availableLiquidity',
    label: 'Available Liquidity',
    value: '$384k'
  }
]

export const mockLeverageDetailDepositData = {
  leverageMin: 1.1,
  leverageMax: 7.7,
  tokens: {
    token0: {
      symbol: 'SUI',
      icon: getIconUrl('sui'),
      coinType: SUI_COIN_TYPE
    },
    token1: {
      symbol: 'SUI',
      icon: getIconUrl('stSui'),
      coinType: SUI_COIN_TYPE
    }
  }
}

export const mockLeverageYourOverviewData = [
  { id: 'activePositions', label: 'Active Positions', value: '1' },
  { id: 'positionValueUsd', label: 'Position Value ($)', value: '$16.07' },
  { id: 'positionValueSui', label: 'Positions Value (SUI)', value: '6.4' },
  { id: 'avgMultiplier', label: 'Avg. Multiplier', value: '1.3' },
  { id: 'positionAtRisk', label: 'Position At-Risk', value: '0' }
]

export const mockNoPositionLeverageDetailPredictionData = [
  {
    id: 'netSupplyApy',
    label: 'Net Supply APY',
    value: (
      <span>
        0.00% → <span className="text-primary">9.74%</span>
      </span>
    )
  },
  {
    id: 'leverageChange',
    label: 'Leverage Change',
    value: <span>1.0x → 2.0x</span>
  },
  {
    id: 'maxPriceImpact',
    label: 'Max Price Impact',
    value: (
      <span className="flex items-center gap-x-1.5">
        <span className="text-green">0.04%</span>
        <SpinIcon className="size-[11px] animate-spin" />
      </span>
    ),
    details: [
      { id: 'swapIn', label: 'Swap In', value: '0.184315 SUI' },
      { id: 'swapOut', label: 'Minimal Expected Swap Out', value: '0.8 USDC' },
      { id: 'fillPrice', label: 'Fill price', value: '3.21 SUI/USDC' },
      { id: 'router', label: 'Router', value: 'CETUS' }
    ]
  },
  {
    id: 'refundableFee',
    label: 'Refundable fee',
    value: <span>1.1x → 2.0x</span>
  },
  {
    id: 'exposure',
    label: 'Exposure',
    value: (
      <span className="flex items-center gap-x-1.5">
        <span className="flex items-center gap-x-0.5">
          <span>0.1699&nbsp;stSUI</span>
          <span className="opacity-40">($28.1)</span>
        </span>
      </span>
    ),
    details: [
      { id: 'newExposure', label: 'New Exposure', value: '0.12 → 0.16 SUI' },
      { id: 'newDebt', label: 'New Debt', value: '12.00 → 32.99 USDC' }
    ]
  }
]

export const mockHasPositionLeverageDetailPredictionWithdrawData = [
  {
    id: 'netSupplyApy',
    label: 'Net APY',
    value: <span className="text-green">1.35% → 2.13%</span>
  },
  {
    id: 'multiplier',
    label: 'Multiplier',
    value: '3.0x'
  },
  {
    id: 'net-value',
    label: 'Net Value',
    value: '0.021928 → 0 SUI',
    details: [
      {
        id: 'collateral',
        label: 'stSUI Collateral',
        value: '0.061575 → 0 stSUI'
      },
      { id: 'debt', label: 'SUI Debt', value: '0.061575 → 0 SUI' },
      { id: 'fill-price', label: 'Fill price', value: '3.2 SUI/USDC' },
      { id: 'router', label: 'Router', value: 'CETUS' }
    ]
  },
  {
    id: 'ltv',
    label: 'LTV',
    value: (
      <span className="text-green">
        12.4% → <span className="text-foreground">0%</span>
      </span>
    ),
    details: [
      {
        id: 'maxLtv',
        label: 'Max LTV',
        value: <span className="text-primary">56%</span>
      },
      {
        id: 'liquidationLTV',
        label: 'Liquidation LTV',
        value: <span className="text-red">75%</span>
      }
    ]
  },
  {
    id: 'maxPriceImpact',
    label: 'Max Price Impact',
    value: (
      <span className="flex items-center gap-x-1.5">
        <span className="text-green">0.04%</span>
        <SpinIcon className="size-[11px] animate-spin" />
      </span>
    ),
    details: [
      { id: 'swapIn', label: 'Swap In', value: '0.184315 SUI' },
      { id: 'swapOut', label: 'Minimal Expected Swap Out', value: '0.8 USDC' },
      { id: 'fillPrice', label: 'Fill price', value: '3.21 SUI/USDC' },
      { id: 'router', label: 'Router', value: 'CETUS' }
    ]
  }
]

export const mockHasPositionLeverageDetailPredictionAdjustMultiplierData = [
  {
    id: 'netSupplyApy',
    label: 'Net APY',
    value: <span className="text-green">1.35% → 2.13%</span>
  },
  { id: 'multiplierChange', label: 'Multiplier Change', value: '3x → 5.6x' },
  {
    id: 'maxPriceImpact',
    label: 'Max Price Impact',
    value: (
      <span className="flex items-center gap-x-1.5">
        <span className="text-green">0.04%</span>
        <SpinIcon className="size-[11px] animate-spin" />
      </span>
    ),
    details: [
      { id: 'swapIn', label: 'Swap In', value: '0.184315 SUI' },
      { id: 'swapOut', label: 'Minimal Expected Swap Out', value: '0.8 USDC' },
      { id: 'fillPrice', label: 'Fill price', value: '3.21 SUI/USDC' },
      { id: 'router', label: 'Router', value: 'CETUS' }
    ]
  },

  {
    id: 'exposure',
    label: 'Exposure',
    value: (
      <span className="flex items-center gap-x-1.5">
        <span className="flex items-center gap-x-0.5">
          <span>0.1699&nbsp;stSUI</span>
          <span className="opacity-40">($28.1)</span>
        </span>
      </span>
    ),
    details: [
      { id: 'newExposure', label: 'New Exposure', value: '0.12 → 0.16 SUI' },
      { id: 'newDebt', label: 'New Debt', value: '12.00 → 32.99 USDC' }
    ]
  }
]

export const mockHasPositionLeverageDetailPredictionManageCollateralData = [
  {
    id: 'dept',
    label: 'SUI Debt',
    value: '0.061575 → 1.061575'
  },
  {
    id: 'ltv',
    label: 'LTV',
    value: <span className="text-green">12.4% → 23.3%</span>,
    details: [
      {
        id: 'liquidationLTV',
        label: 'Liquidation LTV',
        value: <span className="text-red">75%</span>
      },
      {
        id: 'liquidationPrice',
        label: 'Liquidation Price',
        value: <span className="text-green">1.0954 → 0.0635</span>
      },
      {
        id: 'currentPrice',
        label: 'Current Price',
        value: '1.123'
      }
    ]
  }
]

export const mockHasPositionLeverageDetailPredictionDepositData = [
  {
    id: 'netSupplyApy',
    label: 'Net Supply APY',
    value: (
      <span>
        0.00% → <span className="text-primary">9.74%</span>
      </span>
    )
  },
  {
    id: 'leverageChange',
    label: 'Leverage Change',
    value: <span>1.0x → 2.0x</span>
  },
  {
    id: 'maxPriceImpact',
    label: 'Max Price Impact',
    value: (
      <span className="flex items-center gap-x-1.5">
        <span className="text-green">0.04%</span>
        <SpinIcon className="size-[11px] animate-spin" />
      </span>
    ),
    details: [
      { id: 'swapIn', label: 'Swap In', value: '0.184315 SUI' },
      { id: 'swapOut', label: 'Minimal Expected Swap Out', value: '0.8 USDC' },
      { id: 'fillPrice', label: 'Fill price', value: '3.21 SUI/USDC' },
      { id: 'router', label: 'Router', value: 'CETUS' }
    ]
  },
  {
    id: 'exposure',
    label: 'Exposure',
    value: (
      <span className="flex items-center gap-x-1.5">
        <span className="flex items-center gap-x-0.5">
          <span>0.1699&nbsp;stSUI</span>
          <span className="opacity-40">($28.1)</span>
        </span>
      </span>
    ),
    details: [
      { id: 'newExposure', label: 'New Exposure', value: '0.12 → 0.16 SUI' },
      { id: 'newDebt', label: 'New Debt', value: '12.00 → 32.99 USDC' }
    ]
  }
]

export const mockLeverageOverviewData = [
  {
    id: '1',
    strategy: 'StSui',
    netValue: '6.4 SUI',
    netApy: '3.5%',
    pnL: '+$23.23',
    multiplier: '1.3x',
    ltv: '23.5%'
  }
]

export const mockLeverageDetailDashboardLoanMarketOverviewProgressData = {
  ltv: 0.5,
  warningLtv: 0.7,
  maxLtv: 0.8,
  leftAmount: 1000
}

export const mockLeverageDetailDashboardLoanMarketOverviewData = [
  {
    id: '1',
    label: 'Net Value',
    value: '$16.07'
  },
  {
    id: '2',
    label: 'Total Supplied',
    value: '$38.21'
  },
  {
    id: '3',
    label: 'Total Borrowed',
    value: '$22.14'
  },
  {
    id: '4',
    label: 'Interest Earned',
    value: <span className="text-green">$1.1</span>
  },
  {
    id: '5',
    label: 'Net APY',
    value: <span className="text-green">4.34%</span>
  },
  {
    id: '6',
    label: 'Avg. LTV',
    value: <span className="text-green">53.32%</span>
  }
]

export const mockLeverageDetailDashboardLoanMarketOverviewSuppliedData = [
  {
    id: '1',
    asset: 'SUI',
    value: '$16.07',
    apy: '3.5%'
  }
]

export enum TransactionHistoryTypeEnum {
  Repay = 'Repay',
  Borrow = 'Borrow',
  Withdraw = 'Withdraw',
  Deposit = 'Deposit'
}

export const mockLeverageDetailDashboardMainMarketData = [
  {
    id: '1',
    assets: 'SUI',
    totalSupply: '$16.07',
    totalBorrow: '$16.07',
    liquidityLtv: '3.5%',
    supplyApy: '3.5%',
    borrowApy: '3.5%'
  },
  {
    id: '2',
    assets: 'suiUSDT',
    totalSupply: '$16.07',
    totalBorrow: '$16.07',
    liquidityLtv: '3.5%',
    supplyApy: '3.5%',
    borrowApy: '3.5%'
  },
  {
    id: '3',
    assets: 'USDC',
    totalSupply: '$16.07',
    totalBorrow: '$16.07',
    liquidityLtv: '3.5%',
    supplyApy: '3.5%',
    borrowApy: '3.5%'
  }
]

export const mockLeverageDetailDashboardTransactionHistoryData = [
  {
    id: 'tx-1',
    date: '11 Jul 2025 13:43',
    type: TransactionHistoryTypeEnum.Repay,
    asset: 'USDC',
    value: '$32.42',
    hash: '0x1234567890'
  },
  {
    id: 'tx-2',
    date: '12 Jul 2025 09:15',
    type: TransactionHistoryTypeEnum.Borrow,
    asset: 'SUI',
    value: '$32.42',
    hash: '0xabcdef123456'
  },
  {
    id: 'tx-3',
    date: '13 Jul 2025 17:22',
    type: TransactionHistoryTypeEnum.Deposit,
    asset: 'USDC',
    value: '$50.00',
    hash: '0x9876543210ab'
  },
  {
    id: 'tx-4',
    date: '14 Jul 2025 11:05',
    type: TransactionHistoryTypeEnum.Withdraw,
    asset: 'SUI',
    value: '$30.00',
    hash: '0x1234abcd5678'
  },
  {
    id: 'tx-5',
    date: '15 Jul 2025 14:30',
    type: TransactionHistoryTypeEnum.Borrow,
    asset: 'USDC',
    value: '$80.00',
    hash: '0xdeadbeefcafe'
  },
  {
    id: 'tx-6',
    date: '16 Jul 2025 08:45',
    type: TransactionHistoryTypeEnum.Repay,
    asset: 'SUI',
    value: '$40.00',
    hash: '0xbeadfeed1234'
  },
  {
    id: 'tx-7',
    date: '17 Jul 2025 19:10',
    type: TransactionHistoryTypeEnum.Deposit,
    asset: 'USDC',
    value: '$60.00',
    hash: '0xfaceb00c1234'
  },
  {
    id: 'tx-8',
    date: '18 Jul 2025 13:55',
    type: TransactionHistoryTypeEnum.Withdraw,
    asset: 'SUI',
    value: '$25.00',
    hash: '0xfeedface5678'
  },
  {
    id: 'tx-9',
    date: '19 Jul 2025 10:20',
    type: TransactionHistoryTypeEnum.Borrow,
    asset: 'USDC',
    value: '$100.00',
    hash: '0x9a8b7c6d5e4f'
  },
  {
    id: 'tx-10',
    date: '20 Jul 2025 16:40',
    type: TransactionHistoryTypeEnum.Repay,
    asset: 'SUI',
    value: '$50.00',
    hash: '0x1a2b3c4d5e6f'
  },
  {
    id: 'tx-11',
    date: '21 Jul 2025 09:15',
    type: TransactionHistoryTypeEnum.Deposit,
    asset: 'USDC',
    value: '$75.00',
    hash: '0xabcdefabcdef'
  },
  {
    id: 'tx-12',
    date: '22 Jul 2025 12:30',
    type: TransactionHistoryTypeEnum.Withdraw,
    asset: 'SUI',
    value: '$35.00',
    hash: '0x123456789abc'
  }
]
