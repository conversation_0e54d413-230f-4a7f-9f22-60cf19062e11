import { useEffect, useState } from 'react'
import { mockLeverageOverviewData } from '../mock'
import { useOverviewColumns } from '../hooks/use-overview-columns'
import { DataTable } from '@/components/data-table'
import { useNavigate } from 'react-router'

export function YourOverviewTable() {
  const navigate = useNavigate()
  const { columns } = useOverviewColumns()

  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<typeof mockLeverageOverviewData>([])

  useEffect(() => {
    setLoading(true)
    const timer = setTimeout(() => {
      setData(mockLeverageOverviewData)
      setLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="border border-border-8 py-3 rounded-[10px]">
      <DataTable
        className="table-fixed"
        size="sm"
        data={data}
        columns={columns}
        isLoading={loading}
        onPressRow={(row) => {
          navigate(row.original.id)
        }}
        classNames={{
          headerCell: 'pt-1.5 pb-4.5 h-7 px-0 first:pl-6 last:pr-6',
          contentCell: 'py-1.5 h-[30px] px-0 first:pl-6 last:pr-6'
        }}
      />
    </div>
  )
}
