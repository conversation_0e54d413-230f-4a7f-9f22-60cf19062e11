import { useEffect, useState } from 'react'

import { useLeverageColumns } from '../hooks/use-leverage-columns'
import { mockLeverageData } from '../mock'
import { DataTable } from '@/components/data-table'
import { useNavigate } from 'react-router'

export function LeverageTable() {
  const navigate = useNavigate()
  const { columns } = useLeverageColumns()

  const [loading, setLoading] = useState(true)
  const [data, setData] = useState<typeof mockLeverageData>([])

  useEffect(() => {
    setLoading(true)
    const timer = setTimeout(() => {
      setData(mockLeverageData)
      setLoading(false)
    }, 1000)
    return () => clearTimeout(timer)
  }, [])

  return (
    <DataTable
      className="table-fixed"
      onPressRow={(row) => {
        navigate(row.original.id)
      }}
      classNames={{
        headerCell: 'px-0 first:pl-6 last:pr-6',
        contentCell: 'px-0 first:pl-6 last:pr-6'
      }}
      data={data}
      columns={columns}
      isLoading={loading}
    />
  )
}
