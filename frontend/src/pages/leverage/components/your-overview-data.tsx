import { mockLeverageYourOverviewData } from '../mock'
import { UserIcon } from '@/assets/icons'

export const YourOverviewData = () => {
  return (
    <div className="flex flex-col items-start gap-y-3">
      <div className="flex items-center gap-x-1.5">
        <UserIcon />
        <span>Your Overview</span>
      </div>
      <div className="border border-border-8 rounded-[10px] items-center overflow-hidden grid grid-cols-5 w-full py-3 bg-overview-gradient">
        {mockLeverageYourOverviewData.map((item) => (
          <div
            key={item.id}
            className="flex flex-col items-center border-r-1 border-border-8 last:border-r-0 h-full justify-center py-3">
            <div className="flex flex-col items-center gap-y-3">
              <div className="text-sm opacity-60 text-nowrap">{item.label}</div>
              <div className="font-medium">{item.value}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
