import { LeverageTable } from './components/leverage-table'
import { YourOverviewData } from './components/your-overview-data'
import { YourOverviewTable } from './components/your-overview-table'
import { cn } from '@/lib/utils'
import { mockLeverageData } from './mock'
import { GradientPageTitle } from '@/components/gradient-page-title'

export default function Leverage() {
  const hasPosition = !!mockLeverageData.some((item) => item.hasPosition)
  return (
    <section
      className="relative pt-[53px] pb-10 flex flex-col"
      style={{ maxWidth: 1176 }}>
      <GradientPageTitle
        title="Leverage Farming"
        description="Effortlessly earn yields with automated compounding loops, powered by
        Pebble Market"
        className={cn(hasPosition ? 'mb-12' : 'mb-[70px]')}
      />

      {hasPosition && (
        <>
          <div className="flex flex-col gap-y-3">
            <YourOverviewData />
            <YourOverviewTable />
          </div>

          <div className="mt-16 mb-3 text-primary py-3 border-b border-border-8">
            Leverage Farming Strategies
          </div>
        </>
      )}

      <LeverageTable />
    </section>
  )
}
