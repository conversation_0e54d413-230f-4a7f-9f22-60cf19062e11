import type { ColumnDef } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { WebIcon } from '@/assets/icons'
import { getIconUrl } from '@/lib/assets'
import { Avatar } from '@/components/ui/avatar'
import {
  mockLeverageDetailDashboardTransactionHistoryData,
  TransactionHistoryTypeEnum
} from '@/pages/leverage/mock'
import { Badge } from '@/components/ui/badge'
import { NumberCell } from '@/components/number-cell'
import { SUI_EXPLORER_URL } from '@/config/networks'

export const useTransactionHistoryColumns = () => {
  const columns: ColumnDef<
    (typeof mockLeverageDetailDashboardTransactionHistoryData)[number]
  >[] = [
    {
      id: 'date',
      header: 'Date',
      accessorKey: 'date'
    },
    {
      id: 'type',
      header: 'Type',
      accessorKey: 'type',
      cell: ({ row }) => {
        const type = row.original.type
        const badgeVariant =
          type === TransactionHistoryTypeEnum.Withdraw ||
          type === TransactionHistoryTypeEnum.Borrow
            ? 'yellow'
            : 'green'
        return <Badge variant={badgeVariant}>{type}</Badge>
      }
    },
    {
      id: 'asset',
      header: 'Asset',
      accessorKey: 'value',
      cell: ({ row }) => {
        const asset = row.original.asset
        return (
          <div className="flex items-center gap-x-1.5">
            <Avatar src={getIconUrl('usdc', 'svg')} alt={asset} />
            <span>{asset}</span>
          </div>
        )
      }
    },
    {
      id: 'value',
      header: 'Value',
      accessorKey: 'value',
      cell: ({ row }) => {
        return <NumberCell value={row.original.value} prefix="$" />
      }
    },
    {
      id: 'hash',
      header: () => null,
      size: 14,
      enableSorting: false,
      cell: ({ row }) => {
        const hash = row.original.hash
        return (
          <div className="flex items-center justify-end">
            <Button
              onClick={() => {
                window.open(`${SUI_EXPLORER_URL}/${hash}`, '_blank')
              }}
              variant="icon"
              className="h-[14px] w-[14px]">
              <WebIcon className="size-[14px]" />
            </Button>
          </div>
        )
      }
    }
  ]

  return { columns }
}
