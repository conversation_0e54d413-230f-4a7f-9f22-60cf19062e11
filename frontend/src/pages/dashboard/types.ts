import { TransactionTypeEnum } from '@/types/user'
import { TransactionHistoryTypeEnum } from '../leverage/mock'

export enum DashboardTabEnum {
  LoanMarketOverview = 'Loan & Market Overview',
  TransactionHistory = 'Transaction History'
}

export enum DashboardMenuEnum {
  MyLoan = 'My Loan',
  MarketOverview = 'Market Overview'
}
export const TRANSACTION_TYPE_MAP: Record<
  TransactionTypeEnum,
  TransactionHistoryTypeEnum
> = {
  [TransactionTypeEnum.Deposit]: TransactionHistoryTypeEnum.Deposit,
  [TransactionTypeEnum.Withdraw]: TransactionHistoryTypeEnum.Withdraw,
  [TransactionTypeEnum.Borrow]: TransactionHistoryTypeEnum.Borrow,
  [TransactionTypeEnum.Repay]: TransactionHistoryTypeEnum.Repay
}
