import { Overview } from './overview'
import { useMarketOverview } from '@/pages/market/hooks/use-market-overview'
import { useMemo, type FC } from 'react'
import {
  AssetsList,
  PositionStatusBar
} from '@/pages/market/components/market-overview'
import { MarketAssetsEnum } from '@/pages/market/types'
import { useMakertObligationInfo } from '@/pages/market/hooks/use-market-obligation'
import { useInterestEarned } from '@/hooks/use-interest-earned'
import { useNetApy } from '@/hooks/use-net-Apy'

export const LoanMarketOverview: FC<{
  marketName?: string
}> = ({ marketName = '' }) => {
  const { marketOverview } = useMarketOverview()
  const obligatonInfo = useMakertObligationInfo(marketName)
  // const { netApy } = useNetApy(
  //   marketName,
  //   obligatonInfo?.obligationId ?? '',
  //   obligatonInfo?.obligation
  // )
  const { interestEarned } = useInterestEarned(
    marketName,
    obligatonInfo?.obligation
  )
  const { netApy } = useNetApy(marketName, obligatonInfo?.obligation)
  const data = useMemo(() => {
    return marketOverview?.markets.find(
      (market) => market.marketName === marketName
    )
  }, [marketOverview, marketName])

  const overviewData = useMemo(() => {
    return {
      marketName,
      netValue: data?.netValue ?? 0,
      supplied: data?.supplied ?? 0,
      borrowed: data?.borrowed ?? 0,
      ltv: data?.ltv ?? 0,
      warnLtv: data?.warnLtv ?? 0,
      maxLtv: data?.maxLtv ?? 0,
      netApy: netApy ?? 0,
      interestEarned: interestEarned ?? 0
    }
  }, [data, marketName, netApy, interestEarned])

  return (
    <div className="flex flex-col gap-y-6">
      <Overview data={overviewData} />
      <div className="flex gap-6 items-center pb-3">
        <div className="underline mt-1">Positions Status</div>
        <PositionStatusBar
          ltv={data?.ltv ?? 0}
          warningLtv={data?.warnLtv ?? 0}
          maxLtv={data?.maxLtv ?? 0}
          leftAmount={data?.surplus ?? 0}
        />
      </div>
      <div className="flex justify-between gap-6">
        <AssetsList
          marketName={marketName}
          marketType={data?.marketType ?? ''}
          assetsType={MarketAssetsEnum.Supplied}
          showOperation={false}
          total={data?.supplied ?? 0}
          data={data?.collaterals ?? []}
          className="border border-border-8 rounded-[10px]"
          innerClassName="px-6 py-3"
          tableClassName="px-6 pb-3"
        />
        <AssetsList
          marketName={marketName}
          marketType={data?.marketType ?? ''}
          assetsType={MarketAssetsEnum.Borrowed}
          showOperation={false}
          total={data?.borrowed ?? 0}
          data={data?.debts ?? []}
          className="border border-border-8 rounded-[10px]"
          innerClassName="px-6 py-3"
          tableClassName="px-6 pb-3"
        />
      </div>
    </div>
  )
}
