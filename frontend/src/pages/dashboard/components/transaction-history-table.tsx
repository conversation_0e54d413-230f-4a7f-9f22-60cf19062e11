import { useMemo, useState, useCallback, type FC } from 'react'
import { useTransactionHistoryColumns } from '../hooks/use-transaction-history-columns'
import { DataTable } from '@/components/data-table'
import { useGetMarketTransactionHistory } from '@/queries/history'
import { getShortName } from '@/utils/data'
import { formatTimestamp } from '@/lib/formatter'
import { TRANSACTION_TYPE_MAP } from '../types'
import { useInfiniteTableScroll } from '@/hooks/use-infinite-table-scroll'
import type { SortingState } from '@tanstack/react-table'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { TransactionProductEnum } from '@/types/user'
import { useGetAllMarketConfig } from '@/queries/config'
import { formatUnits } from '@/lib/units'
import BigNumber from 'bignumber.js'

export const TransactionHistoryTable: FC<{ marketName?: string }> = ({
  marketName
}) => {
  const { data: marketConfig } = useGetAllMarketConfig()
  const { userAddress } = usePebbleWallet()
  const [sorting, setSorting] = useState<SortingState>([])

  const marketType = useMemo(
    () => marketConfig?.find((v) => v.name === marketName)?.marketType,
    [marketConfig, marketName]
  )

  const sortParams = useMemo(() => {
    const params: Record<string, 'asc' | 'desc' | null> = {}

    if (sorting.length === 0) {
      params.chainTimestampMSSort = 'desc'
    } else {
      sorting.forEach((sort) => {
        switch (sort.id) {
          case 'date':
            params.chainTimestampMSSort = sort.desc ? 'desc' : 'asc'
            break
          case 'type':
            params.typeSort = sort.desc ? 'desc' : 'asc'
            break
          case 'value':
            params.amountSort = sort.desc ? 'desc' : 'asc'
            break
          default:
            break
        }
      })
    }

    return params
  }, [sorting])

  const { columns } = useTransactionHistoryColumns()

  const { flatData, isLoading, fetchMore, hasMore, setQuery } =
    useGetMarketTransactionHistory(marketType || '', {
      initialQuery: {
        chainTimestampMSSort: 'desc'
      }
    })

  const updateQueryWithSort = useCallback(() => {
    if (!userAddress || !marketType) return

    setQuery({
      userAddress,
      marketType,
      product: TransactionProductEnum.Market,
      ...sortParams
    })
  }, [setQuery, marketType, sortParams, userAddress])

  useMemo(() => {
    if (sorting.length > 0) {
      updateQueryWithSort()
    }
  }, [updateQueryWithSort, sorting.length])

  const data = useMemo(() => {
    if (!marketType) return []
    return flatData.map((v) => {
      const amount = BigNumber(
        formatUnits(v.amount, Number(v.tokenInfo.decimals))
      )
      const value = amount.multipliedBy(v.tokenInfo.price).toNumber()
      return {
        id: v.id,
        date: formatTimestamp(v.chainTimestampMS / 1000, 'MMM D HH:mm'),
        type: TRANSACTION_TYPE_MAP[v.type],
        asset: getShortName(v.token),
        value: value.toString(),
        hash: v.txDigest
      }
    })
  }, [flatData, marketType])

  const { handleScroll, tableContainerRef } = useInfiniteTableScroll(
    fetchMore,
    {
      enabled: hasMore && !isLoading
    }
  )

  if (!marketType) return null

  return (
    <div
      ref={tableContainerRef}
      onScroll={handleScroll}
      className="h-full overflow-auto">
      <DataTable
        data={data}
        columns={columns}
        isLoading={isLoading}
        size="md"
        sorting={sorting}
        onSortingChange={setSorting}
        serverSideSorting={true}
        classNames={{
          headerCell: 'px-3',
          contentCell: 'px-3'
        }}
      />
    </div>
  )
}
