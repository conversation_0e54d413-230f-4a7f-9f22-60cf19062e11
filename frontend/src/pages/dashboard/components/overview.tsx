import { useMemo, type FC } from 'react'
import { OverviewBar } from '@/components/overview-bar'
import { NumberCell } from '@/components/number-cell'

export const Overview: FC<{
  data: {
    marketName: string
    netValue: number
    supplied: number
    borrowed: number
    ltv: number
    netApy: number
    interestEarned: number
  }
}> = ({ data }) => {
  const overviewData = useMemo(
    () => [
      {
        label: 'Net Value',
        value: <NumberCell value={data?.netValue} prefix="$" />
      },
      {
        label: 'Total Supplied',
        value: <NumberCell value={data?.supplied} prefix="$" />
      },
      {
        label: 'Total Borrowed',
        value: <NumberCell value={data?.borrowed} prefix="$" />
      },
      {
        label: 'Interest Earned',
        value: <NumberCell value={data?.interestEarned} prefix="$" />,
        valueClassName: 'text-green'
      },
      {
        label: 'Net Apy',
        value:
          data?.netApy !== null ? `${(data?.netApy * 100).toFixed(2)}%` : '-',
        valueClassName: 'text-green'
      },
      {
        label: 'LTV',
        value: <NumberCell value={data?.ltv * 100} suffix="%" />,
        valueClassName: 'text-green'
      }
    ],
    [data]
  )
  return <OverviewBar data={overviewData} />
}
