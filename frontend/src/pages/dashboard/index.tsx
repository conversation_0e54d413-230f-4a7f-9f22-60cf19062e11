import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rumbI<PERSON>,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'
import { useMemo, useState } from 'react'
import { Link, matchPath, useLocation, useParams } from 'react-router'
import { DashboardMenuEnum, DashboardTabEnum } from './types'
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { AnchorMenu } from '@/components/anchor-menu'
import { MenuIcon, UserIcon } from '@/assets/icons'
import { LoanMarketOverview } from './components/loan-market-overview'
import { TransactionHistoryTable } from './components/transaction-history-table'
import { cn, getMarketName } from '@/lib/utils'
import { getIconUrl } from '@/lib/assets'
import { Avatar } from '@/components/ui/avatar'
import { BackButton } from '@/components/buttons'
import {
  ManageCollateralDialog,
  BorrowDialog,
  SupplyDialog,
  ManageDebtDialog
} from '@/components/dialogs'
import { MarketTable } from '../market/components/market-table'

const menu = [
  {
    id: DashboardMenuEnum.MyLoan,
    label: DashboardMenuEnum.MyLoan,
    icon: <UserIcon />
  },
  {
    id: DashboardMenuEnum.MarketOverview,
    label: DashboardMenuEnum.MarketOverview,
    icon: <MenuIcon />
  }
]

export default function Dashboard() {
  const { id } = useParams()
  const [tab, setTab] = useState(DashboardTabEnum.LoanMarketOverview)

  const location = useLocation()

  const isMarket = !!matchPath('/market/dashboard/:id', location.pathname)
  const isMultiply = !!matchPath('/multiply/dashboard/:id', location.pathname)

  const label = useMemo(() => {
    if (isMarket) return 'Market'
    if (isMultiply) return 'Multiply'
    return ''
  }, [isMarket, isMultiply])

  const title = useMemo(() => {
    if (isMarket) return 'Main Market Dashboard'
    if (isMultiply) return 'stSUI/SUI Multiply'
    return ''
  }, [isMarket, isMultiply])

  const link = useMemo(() => {
    if (isMarket) return '/market'
    if (isMultiply) return `/multiply/${id}`
    return ''
  }, [id, isMarket, isMultiply])

  const breadcrumbLabel = useMemo(() => {
    if (isMarket) return 'Main Market Dashboard'
    return 'Dashboard'
  }, [isMarket])

  return (
    <section
      className={cn(
        'relative pt-16 pb-10 w-[1244px] flex flex-col gap-y-6',
        tab === DashboardTabEnum.TransactionHistory && 'h-[calc(100vh-70px)]'
      )}>
      <BorrowDialog />
      <SupplyDialog />
      <ManageCollateralDialog />
      <ManageDebtDialog />

      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              asChild
              className="opacity-60 hover:opacity-100 transition-all">
              <Link to={link}>{label}</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <span>{breadcrumbLabel}</span>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="relative">
        <div className="flex items-center gap-x-1.5">
          {!isMarket && (
            <div className="relative mr-4">
              <Avatar src={getIconUrl('stSui')} alt="sui-logo" size={24} />
              <Avatar
                src={getIconUrl('sui')}
                alt="sui-logo"
                size={24}
                className="absolute left-4 top-0"
              />
            </div>
          )}
          <span className="text-2xl text-white">{title}</span>
        </div>

        <div className="absolute top-0 right-[calc(100%+12px)] h-8 w-8">
          <BackButton className="h-8 w-8" />
        </div>
      </div>

      <Tabs
        value={tab}
        onValueChange={(item) => {
          setTab(item as DashboardTabEnum)
        }}
        orientation="vertical">
        <TabsList className="pl-0" aria-label="market tabs">
          <TabsTrigger value={DashboardTabEnum.LoanMarketOverview}>
            {DashboardTabEnum.LoanMarketOverview}
          </TabsTrigger>
          <TabsTrigger value={DashboardTabEnum.TransactionHistory}>
            {DashboardTabEnum.TransactionHistory}
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {tab === DashboardTabEnum.LoanMarketOverview ? (
        <AnchorMenu
          menu={menu}
          defaultActiveMenuId={DashboardMenuEnum.MyLoan}
          offsetTop={84}
          menuClassName="w-[180px] mr-3">
          <div className="flex-1 flex flex-col gap-6">
            <div id={DashboardMenuEnum.MyLoan}>
              <LoanMarketOverview marketName={id} />
            </div>
            <div id={DashboardMenuEnum.MarketOverview}>
              <div className="flex items-center gap-x-1.5 border-b border-border-8 py-3 px-6">
                <MenuIcon className="size-6" />
                <span>{getMarketName(id ?? '')}</span>
              </div>
              <MarketTable
                marketName={id}
                size="md"
                showMarketNameInHeader={false}
                className="mx-6"
              />
            </div>
          </div>
        </AnchorMenu>
      ) : (
        <TransactionHistoryTable marketName={id} />
      )}
    </section>
  )
}
