export enum MarginTradeDepositTabEnum {
  Buy = 'Buy / Long',
  Sell = 'Sell / Short'
}

export enum MarginTradePositionTxnInfoTabEnum {
  Position = 'My position',
  Txn = 'Transactions'
}

export enum ManageMarginTradeDepositTabEnum {
  Deposit = 'Deposit',
  Withdraw = 'Withdraw'
}

export enum ManageSizeTabEnum {
  Increase = 'Increase',
  Decrease = 'Decrease'
}

export enum ManageMarginTradeClosePositionTabEnum {
  CloseToToken0 = 'Close to Token0',
  CloseToToken1 = 'Close to Token1'
}

export enum MarginTradePositionActionEnum {
  ManageSize = 'Manage Size',
  AdjustLeverage = 'Adjust Leverage',
  RepayDept = 'Repay Debt',
  TopUpCollateral = 'Top Up Collateral',
  ClosePosition = 'Close Position'
}

export enum TransactionTypeEnum {
  Liquidated = 1,
  DecreaseSize = 2,
  IncreaseLeverage = 3
}

export const LEVERAGE_MIN_MULTIPLIER = 1.0

export enum LeverageTransactionType {
  Deposit = 0,
  Repay = 1
}

export type TransactionDetailData = {
  netSupplyApy: {
    from: number
    to?: number
  }
  netBorrowApy?: {
    from: number
    to: number
  }
  leverage: number
  flashBorrowFee?: number
  exposure?: {
    amount: number
    symbol: string
    usdValue: number
    newExposure: { from: number; to: number; symbol: string }
    newDebt: { from: number; to: number; symbol: string }
  }
  liquidationPrice: {
    from: number
    to: number
    ltv: { from: number; to: number }
    liqLtv: number
  }
  netValue?: {
    from: number
    to: number
  }
  ltv?: {
    from: number
    to: number
    maxLtv: number
    liqLtv: number
  }
  refundableFee?: number
}
