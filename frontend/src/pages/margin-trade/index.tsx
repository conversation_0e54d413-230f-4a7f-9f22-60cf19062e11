import { Overview } from './components/overview'
import { Chart } from './components/chart'
import { NoPositionDeposit } from './components/no-position-deposit'
import { PositionTxnInfo } from './components/position-txn-info'
import { useEffect, useMemo, useState, useCallback } from 'react'
import { GradientPageTitle } from '@/components/gradient-page-title'
import { useGetMarginTradeList } from '@/queries/margin-trade'
import type { IMarginTradeItem } from '@/types/margin-trade'
import { useLeveragePosition } from './hooks/use-leverage-position'
import type { Token } from '@/components/balance-input'
import { ManagePositionDeposit } from './components/manage-position-deposit'

export default function MarginTrade() {
  const { data } = useGetMarginTradeList()
  const {
    data: positions,
    refetch: refetchPositions,
    isLoading
  } = useLeveragePosition()
  const [selectedItem, setSelectedItem] = useState<IMarginTradeItem>()
  const [selectedToken, setSelectedToken] = useState<Token>()
  const currentPosition = useMemo(() => {
    if (!positions) return undefined
    const position = positions.find(
      (item) =>
        item.position?.leverageObligation.info?.deposit ===
          selectedItem?.token0 &&
        item.position?.leverageObligation.info?.borrow === selectedItem?.token1
    )
    if (!position || !position.position?.lendingMarketObligation.isActive()) {
      return undefined
    }
    return position
  }, [positions, selectedItem])

  // 🔥 优化：使用useMemo缓存coinType计算，避免每次渲染都创建新字符串
  const coinType = useMemo(() => {
    if (
      !selectedItem?.tokenInfo0?.symbol ||
      !selectedItem?.tokenInfo1?.symbol
    ) {
      return ''
    }
    const result =
      selectedItem.tokenInfo0.symbol + selectedItem.tokenInfo1.symbol
    console.log('🔍 coinType calculated:', result)
    return result
  }, [selectedItem?.tokenInfo0?.symbol, selectedItem?.tokenInfo1?.symbol])

  // 🔥 关键修复：使用useCallback缓存onSelectChange函数，避免每次渲染都创建新函数
  const handleSelectChange = useCallback(
    (value: string) => {
      setSelectedItem(
        data?.data?.find((item) => item.id === Number(value)) ?? undefined
      )
    },
    [data?.data]
  )

  useEffect(() => {
    setSelectedItem(data?.data?.[0] ?? undefined)
  }, [data])

  return (
    <section className="relative pt-[53px] pb-10 flex flex-col gap-y-9">
      <GradientPageTitle
        title="Margin Trade"
        description="Amplify your positions with margin, powered by Pebble Market"
      />
      <div className="flex items-start gap-x-6">
        <div className="flex flex-col gap-y-6 flex-1">
          <Overview
            tokenPairList={data?.data ?? []}
            selectedToken={selectedToken}
            selectedItem={selectedItem}
            onSelectChange={handleSelectChange}
          />
          <Chart coinType={coinType} realtimeEnabled={true} useSSE={true} />
          <PositionTxnInfo
            marketName={selectedItem?.fromMarket ?? 'MainMarket'}
            positions={
              positions
                ?.map((item) => item.position)
                .filter((item) => item !== undefined) ?? []
            }
            isLoading={isLoading}
          />
        </div>
        {!currentPosition || !currentPosition.position ? (
          <NoPositionDeposit
            tradeItem={selectedItem}
            selectedToken={selectedToken}
            setSelectedToken={setSelectedToken}
            onConfirm={() => {
              console.log('onConfirm')
              refetchPositions()
            }}
          />
        ) : (
          <ManagePositionDeposit
            marketName={selectedItem?.fromMarket ?? 'MainMarket'}
            ownerCapId={currentPosition.ownerCapId}
            position={currentPosition.position}
            tradeItem={selectedItem}
            // onConfirm={() => {
            //   console.log('onConfirm')
            //   refetchPositions()
            // }}
          />
        )}
      </div>
    </section>
  )
}
