import { useGetMarginTradeKline } from '@/queries/kline'
import { useMemo, useEffect, useRef, useState, useCallback } from 'react'
import { ResolutionKey } from '@/components/chart/types'
import { useKlineSSE, type SSEError } from '@/hooks/use-kline-sse'
import type { IKLineInfo } from '@/types/kline'

export interface ILeveragePriceResult {
  currentPrice: number
  priceChange24h: number
  priceChangePercent24h: number
  isLoading: boolean
  error: string | null
  // SSE 相关状态
  sseConnectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  isRealtime: boolean
}

export const useLeveragePrice = (
  coinType: string,
  options?: {
    refetchInterval?: number | false
    enabled?: boolean
    useSSE?: boolean // 新增：是否启用 SSE 实时更新
    autoReconnect?: boolean // 新增：是否自动重连
  }
): ILeveragePriceResult => {
  // 🔍 调试：追踪hook调用
  console.log('🔄 useLeveragePrice called with:', {
    coinType,
    enabled: options?.enabled,
    useSSE: options?.useSSE
  })

  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // 🔥 新增：SSE 相关状态
  const [initialDataLoaded, setInitialDataLoaded] = useState(false)
  const [realtimeData, setRealtimeData] = useState<IKLineInfo | null>(null)
  const lastUpdateTimeRef = useRef<number>(0)

  // 使用 useState 管理时间戳，初始计算一次
  const [timeRange, setTimeRange] = useState(() => {
    const now = Date.now()
    const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000
    return {
      fromTime: twentyFourHoursAgo,
      toTime: now
    }
  })

  // 🔥 获取初始K线数据
  const {
    data: klineData,
    isLoading,
    error,
    isSuccess
  } = useGetMarginTradeKline(
    {
      address: coinType,
      fromTime: timeRange.fromTime,
      toTime: timeRange.toTime,
      size: 50,
      bar: ResolutionKey.Min15 // 使用15分钟K线数据，与图表保持一致
    },
    {
      enabled: options?.enabled !== false && !!coinType,
      refetchInterval: false, // 禁用 TanStack Query 的自动刷新
      staleTime: 10000 // 10秒内认为数据是新鲜的
    }
  )

  // 🔥 监听初始数据加载完成
  useEffect(() => {
    if (isSuccess && klineData?.data && klineData.data.length > 0) {
      console.log('📊 Initial kline data loaded, enabling SSE...')
      setInitialDataLoaded(true)
    } else if (!isLoading && !klineData?.data) {
      setInitialDataLoaded(false)
    }
  }, [isSuccess, klineData, isLoading])

  // 🔥 SSE 实时数据更新回调
  const handleKlineUpdate = useCallback((klineData: IKLineInfo) => {
    // 防止重复更新：检查时间戳
    if (klineData.t > lastUpdateTimeRef.current) {
      console.log('📈 Received realtime kline update:', klineData)
      lastUpdateTimeRef.current = klineData.t
      setRealtimeData(klineData)
    }
  }, [])

  // 🔥 SSE 错误处理
  const handleSSEError = useCallback((error: SSEError) => {
    console.error('❌ SSE connection error:', error)
  }, [])

  // 🔥 SSE Hook - 只有在初始数据加载完成且启用SSE时才连接
  const {
    connectionStatus: sseConnectionStatus,
    disconnect: sseDisconnect,
    lastError: sseError
  } = useKlineSSE({
    symbol: coinType,
    bar: ResolutionKey.Min15, // 使用15分钟K线数据，与图表保持一致
    enabled: options?.useSSE !== false && initialDataLoaded && !!coinType,
    onKlineUpdate: handleKlineUpdate,
    onError: handleSSEError,
    throttleMs: 1000, // 1秒节流，避免过于频繁的更新
    autoReconnect: options?.autoReconnect !== false // 默认启用自动重连
  })

  // 🔥 优化：coinType变化时重置所有状态
  useEffect(() => {
    if (coinType) {
      const now = Date.now()
      const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000
      setTimeRange({
        fromTime: twentyFourHoursAgo,
        toTime: now
      })

      // 重置 SSE 相关状态
      setInitialDataLoaded(false)
      setRealtimeData(null)
      lastUpdateTimeRef.current = 0
    }
  }, [coinType])

  // 🔥 设置定时器进行手动刷新 - 只在未启用SSE时使用
  useEffect(() => {
    // 清除之前的定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }

    // 🔥 关键逻辑：只有在未启用SSE且启用了定时刷新时才使用定时器
    // 避免SSE和定时器同时工作造成冲突
    const shouldUseTimer =
      options?.refetchInterval &&
      options.refetchInterval > 0 &&
      coinType &&
      (options?.useSSE === false || !initialDataLoaded) // 未启用SSE或初始数据未加载完成

    if (
      shouldUseTimer &&
      options?.refetchInterval &&
      typeof options.refetchInterval === 'number'
    ) {
      console.log('⏰ Starting timer-based refresh (SSE disabled or not ready)')
      intervalRef.current = setInterval(() => {
        // 🔥 优化：直接更新时间戳，避免频繁的函数式更新检查
        const now = Date.now()
        const twentyFourHoursAgo = now - 24 * 60 * 60 * 1000
        setTimeRange({
          fromTime: twentyFourHoursAgo,
          toTime: now
        })
      }, options.refetchInterval)
    } else if (options?.useSSE !== false && initialDataLoaded) {
      console.log('📡 Using SSE for real-time updates, timer disabled')
    }

    // 清理函数
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [options?.refetchInterval, options?.useSSE, coinType, initialDataLoaded])

  // 🔥 增强的价格数据计算 - 支持实时数据更新
  const priceData = useMemo(() => {
    // 🔍 调试：追踪价格数据计算
    console.log('🔄 Calculating price data for:', coinType, {
      initialDataLength: klineData?.data?.length,
      hasRealtimeData: !!realtimeData,
      sseStatus: sseConnectionStatus
    })

    // 检查数据结构 - 与 Chart() 组件保持一致
    if (
      !klineData?.data ||
      !Array.isArray(klineData.data) ||
      klineData.data.length === 0
    ) {
      return {
        currentPrice: 0,
        priceChange24h: 0,
        priceChangePercent24h: 0
      }
    }

    // 🔥 优先使用实时数据作为当前价格
    let currentPrice: number
    if (realtimeData && realtimeData.address === coinType) {
      // 使用 SSE 实时数据
      currentPrice = realtimeData.c
      console.log('📈 Using realtime price:', currentPrice)
    } else {
      // 使用初始数据的最新价格
      const latestKline = klineData.data[klineData.data.length - 1]
      currentPrice = latestKline.c
      console.log('📊 Using initial data price:', currentPrice)
    }

    // 获取24小时前的价格（第一个K线数据的开盘价）
    // 注意：使用15分钟K线数据，24小时 = 96个15分钟周期
    const oldestKline = klineData.data[0]
    const price24hAgo = oldestKline.o

    // 计算价格变化
    const priceChange24h = currentPrice - price24hAgo
    const priceChangePercent24h =
      price24hAgo > 0 ? (priceChange24h / price24hAgo) * 100 : 0

    const result = {
      currentPrice,
      priceChange24h,
      priceChangePercent24h
    }

    console.log('💰 Price data calculated:', result)
    return result
  }, [klineData, coinType, realtimeData, sseConnectionStatus]) // 🔥 添加实时数据依赖

  // 🔥 组件卸载时清理 SSE 连接
  useEffect(() => {
    return () => {
      console.log('🧹 Cleaning up leverage price hook')
      if (sseDisconnect) {
        sseDisconnect()
      }
    }
  }, [sseDisconnect])

  return {
    currentPrice: priceData.currentPrice,
    priceChange24h: priceData.priceChange24h,
    priceChangePercent24h: priceData.priceChangePercent24h,
    isLoading,
    error: error?.message || sseError?.message || null,
    // 🔥 新增：SSE 相关状态
    sseConnectionStatus,
    isRealtime: sseConnectionStatus === 'connected' && !!realtimeData
  }
}
