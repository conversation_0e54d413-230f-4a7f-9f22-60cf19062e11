import type { ColumnDef } from '@tanstack/react-table'
import { getIconUrl } from '@/lib/assets'
import { Avatar } from '@/components/ui/avatar'
import { NumberCell } from '@/components/number-cell'
import useMarketTokenInfo from '@/pages/market/hooks/use-market-token-info'
import { Badge } from '@/components/ui/badge'

interface IPosition {
  id: string
  marketName: string
  leftAsset: string
  rightAsset: string
  leverage: number
  leverageType: string
  size?: number
  principle?: number
  pnl?: number
  avgPositionPrice?: number
  liqPrice?: number
  liqPricePercent?: string
}

// eslint-disable-next-line react-refresh/only-export-components
const AssetCell = ({ position }: { position: IPosition }) => {
  const { marketTokenInfo: leftMarketTokenInfo } = useMarketTokenInfo(
    position.marketName,
    position.leftAsset
  )

  const { marketTokenInfo: rightMarketTokenInfo } = useMarketTokenInfo(
    position.marketName,
    position.rightAsset
  )

  return (
    <div className="flex items-center gap-x-1.5">
      <div className="flex items-center">
        <div className="relative mr-4">
          <Avatar
            fallback={leftMarketTokenInfo?.tokenInfo.symbol.slice(0, 1)}
            src={getIconUrl(leftMarketTokenInfo?.tokenInfo.symbol ?? '')}
            alt="left-asset-logo"
            size={18}
          />
          <Avatar
            fallback={rightMarketTokenInfo?.tokenInfo.symbol.slice(0, 1)}
            src={getIconUrl(rightMarketTokenInfo?.tokenInfo.symbol ?? '')}
            alt="right-asset-logo"
            size={18}
            className="absolute left-3 top-0"
          />
        </div>
        <span className="text-sm">
          {leftMarketTokenInfo?.tokenInfo.symbol}/
          {rightMarketTokenInfo?.tokenInfo.symbol}
        </span>
      </div>
      <Badge
        className="w-fit px-1.5 py-1 text-xs"
        variant={position.leverageType === 'Short' ? 'red' : 'green'}>
        <NumberCell
          value={position.leverage}
          options={{
            maximumFractionDigitsGreater1: 1,
            minimumFractionDigitsGreater1: 1,
            maximumFractionDigitsLess1: 1,
            minimumFractionDigitsLess1: 1
          }}
        />
        x {position.leverageType}
      </Badge>
    </div>
  )
}

AssetCell.displayName = 'AssetCell'

export const useMyPositionColumns = () => {
  const columns: ColumnDef<IPosition>[] = [
    {
      id: 'position',
      header: 'Position',
      accessorKey: 'id',
      cell: ({ row }) => {
        const position = row.original
        return <AssetCell position={position} />
      }
    },
    {
      id: 'size',
      header: 'Size',
      accessorKey: 'size',
      cell: ({ row }) => {
        const position = row.original
        return <NumberCell value={position.size} prefix="$" />
      },
      size: 120
    },
    {
      id: 'pciciple',
      header: 'Principle',
      accessorKey: 'principle',
      cell: ({ row }) => {
        const position = row.original
        return <NumberCell value={position.principle} prefix="$" />
      },
      size: 120
    },
    {
      id: 'pnl',
      header: 'PnL',
      accessorKey: 'pnl',
      cell: ({ row }) => {
        const position = row.original
        return (
          <NumberCell value={position.pnl} className="text-green" prefix="$" />
        )
      },
      size: 120
    },
    {
      id: 'avgPositionPrice',
      header: 'Avg. Position Price',
      accessorKey: 'avgPositionPrice',
      cell: ({ row }) => {
        const position = row.original
        return <NumberCell value={position.avgPositionPrice} prefix="$" />
      },
      size: 120
    },
    {
      id: 'liqPrice',
      header: 'Liq. Price',
      accessorKey: 'liqPrice',
      cell: ({ row }) => {
        const position = row.original
        return (
          <span>
            <NumberCell
              value={position.liqPrice}
              prefix="$"
              className="text-red"
            />
            {' ('}
            {position.liqPricePercent ? `${position.liqPricePercent}%` : '-'}
            {' )'}
          </span>
        )
      },
      size: 120
    }
  ]

  return {
    columns
  }
}
