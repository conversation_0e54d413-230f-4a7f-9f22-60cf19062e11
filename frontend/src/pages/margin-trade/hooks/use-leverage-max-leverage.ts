import { useMemo } from 'react'
import { useLeverageMarket } from './use-leverage-market'
import { type IMarginTradeItem } from '@/types/margin-trade'
import type { LeverageObligation } from '@pebble-protocol/pebble-sdk'
import { LeverageType } from './use-leverage'
import { useLeverageOperationQuery } from './use-leverage-operation'
import { LEVERAGE_MIN_MULTIPLIER } from '../types'

export const useLeverageMaxLeverage = (
  position: LeverageObligation,
  slippage: number,
  tradeItem?: IMarginTradeItem
) => {
  const { data: leverageMarket } = useLeverageMarket()

  const leftCoinType = useMemo(() => {
    if (!tradeItem) return undefined
    return tradeItem.tokenInfo0
  }, [tradeItem])

  const inputCoinType = useMemo(() => {
    if (!position || !tradeItem) return undefined
    return position.principleCoinType()
  }, [position, tradeItem])

  const rightCoinType = useMemo(() => {
    if (!tradeItem) return undefined
    return tradeItem.tokenInfo1
  }, [tradeItem])

  const params = useMemo(() => {
    if (
      !leverageMarket ||
      !leftCoinType ||
      !inputCoinType ||
      !rightCoinType ||
      !tradeItem
    )
      return undefined
    const isLong = position.isLong(tradeItem?.token0 ?? '')
    const targetLTV = isLong ? tradeItem?.longMaxLTV : tradeItem?.shortMaxLTV
    return {
      market: leverageMarket,
      targetLTV,
      swapSlippage: slippage,
      leverageType: isLong ? LeverageType.Long : LeverageType.Short,
      leftCoinType: leftCoinType.address,
      inputCoinType: inputCoinType,
      rightCoinType: rightCoinType.address,
      amount: 1n,
      leverage: position.leverage().asNumber()
    }
  }, [
    inputCoinType,
    leftCoinType,
    leverageMarket,
    rightCoinType,
    slippage,
    tradeItem,
    position
  ])

  const { data: leverageOperation } = useLeverageOperationQuery(params)

  const maxLeverage = useMemo(() => {
    if (!leverageOperation) return LEVERAGE_MIN_MULTIPLIER
    return leverageOperation.maxLeverage.asNumber()
  }, [leverageOperation])

  return maxLeverage
}
