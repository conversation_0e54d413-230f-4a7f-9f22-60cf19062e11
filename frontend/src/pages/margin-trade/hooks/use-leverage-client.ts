import { useMemo } from 'react'
import { usePebbleSDK } from '@/hooks/use-pebble-sdk'
import { NETWORK } from '@/config/networks'
import {
  getNetworkConfig,
  LeverageClient,
  getMarket
} from '@pebble-protocol/pebble-sdk'
import { AggregatorClient } from '@cetusprotocol/aggregator-sdk'

export const useLeverageClient = () => {
  const { pebbleClient } = usePebbleSDK()

  const leverageClient = useMemo(() => {
    if (!pebbleClient) return null

    try {
      // Get network configuration
      const networkConfig = getNetworkConfig(NETWORK)

      // Check if leverage markets are available
      if (
        !networkConfig.leverageMarkets ||
        networkConfig.leverageMarkets.length === 0
      ) {
        console.warn('No leverage markets configured for network:', NETWORK)
        return null
      }

      // Use the first leverage market for now
      const leverageMarket = networkConfig.leverageMarkets[0]
      const market = getMarket(NETWORK, 'MainMarket')
      const marketId = market.objectId
      const marketType = market.type

      // Create leverage market config
      const leverageConfig = {
        leverageMarketId: leverageMarket.objectId,
        leveragePackageId: networkConfig.leveragePackageId,
        lendingMarketId: marketId,
        lendingMarketType: marketType
      }

      // Create AggregatorClient for swap functionality
      const aggregatorClient = new AggregatorClient({})

      // Create and return LeverageClient instance
      return new LeverageClient(aggregatorClient, pebbleClient, leverageConfig)
    } catch (error) {
      console.error('Failed to create LeverageClient:', error)
      return null
    }
  }, [pebbleClient])

  return leverageClient
}
