import { useQuery } from '@tanstack/react-query'
import { useLeverageClient } from './use-leverage-client'
import { useCallback } from 'react'

export const useLeverageMarket = () => {
  const leverageClient = useLeverageClient()
  const queryFn = useCallback(async () => {
    if (!leverageClient) {
      return undefined
    }
    return await leverageClient.obtainUnderlyingMarket()
  }, [leverageClient])
  return useQuery({
    queryKey: ['fetchLeverageMarket'],
    queryFn,
    enabled: !!leverageClient,
    refetchOnWindowFocus: false,
    refetchOnMount: false
  })
}
