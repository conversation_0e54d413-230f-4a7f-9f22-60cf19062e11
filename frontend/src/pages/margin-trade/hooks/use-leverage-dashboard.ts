import { useMemo } from 'react'
import { useLeveragePosition } from './use-leverage-position'
import { getShortName } from '@/utils/data'
import type { AssetsInfo } from '@/pages/market/types'
import { useGetAllMarketConfig } from '@/queries/config'
import { useMarketInfo } from '@/hooks/use-market'
import { Decimal } from '@pebble-protocol/pebble-sdk'
import { useInterestEarned } from '@/hooks/use-interest-earned'
import { useNetApy } from '@/hooks/use-net-Apy'

export const useLeverageDashboard = (
  marketName: string,
  leftToken: string,
  rightToken: string
) => {
  const { data: positions } = useLeveragePosition()
  const marketInfo = useMarketInfo(marketName)
  const { data: marketConfig } = useGetAllMarketConfig()
  const position = useMemo(() => {
    if (!positions) return undefined
    return positions.find(
      (item) =>
        getShortName(item.position?.leverageObligation.info?.deposit ?? '') ===
          leftToken &&
        getShortName(item.position?.leverageObligation.info?.borrow ?? '') ===
          rightToken
    )?.position
  }, [positions, leftToken, rightToken])

  const { netApy } = useNetApy(marketName, position?.lendingMarketObligation)
  const { interestEarned } = useInterestEarned(
    marketName,
    position?.lendingMarketObligation
  )

  const data = useMemo(() => {
    let supplied = 0
    let borrowed = 0
    const collaterals: AssetsInfo[] = []
    const debts: AssetsInfo[] = []
    position?.lendingMarketObligation.depositAssets().forEach((v) => {
      const deposit = position?.lendingMarketObligation.getDeposit(v)
      collaterals.push({
        coinType: deposit.coinType,
        assets: getShortName(deposit.coinType),
        value: deposit.usdValue.asNumber(),
        apy: 0 // calculate apy in useMarketColumns
      })
      supplied += deposit.usdValue.asNumber()
    })
    position?.lendingMarketObligation.borrowedAssets().forEach((v) => {
      const borrow = position?.lendingMarketObligation.getBorrow(v)
      debts.push({
        coinType: borrow.coinType,
        assets: getShortName(borrow.coinType),
        value: borrow.usdValue.asNumber(),
        apy: 0 // calculate apy in useMarketColumns
      })
      borrowed += borrow.usdValue.asNumber()
    })
    const ltv =
      marketInfo?.market && position?.lendingMarketObligation
        ? position.lendingMarketObligation
            .currentLTV(marketInfo.market)
            .asNumber()
        : 0
    const config = marketConfig?.find((v) => v.name === marketName)
    const warnLtv = config?.warnLTV ?? 0
    const maxLtv = config?.maxLTV ?? 0
    return {
      marketName: marketName,
      marketType: marketInfo?.market.typeName ?? '',
      netValue: position?.lendingMarketObligation.netValue().asNumber() ?? 0,
      surplus: marketInfo?.market
        ? position?.lendingMarketObligation
            .surplus(marketInfo.market, Decimal.fromNumber(maxLtv))
            .asNumber()
        : 0,
      supplied: supplied,
      borrowed: borrowed,
      ltv: ltv,
      maxLtv: maxLtv,
      warnLtv: warnLtv,
      collaterals: collaterals,
      debts: debts,
      atRisk: ltv >= warnLtv,
      netApy: netApy ?? 0,
      interestEarned: interestEarned ?? 0
    }
  }, [position, marketInfo, marketConfig, marketName, netApy, interestEarned])

  return {
    data
  }
}
