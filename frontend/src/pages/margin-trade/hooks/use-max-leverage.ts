import { useCallback } from 'react'
import { Decimal, LeverageOperation, Market } from '@pebble-protocol/pebble-sdk'
import { useLeverageClient } from './use-leverage-client'
import { LeverageType } from './use-leverage'
import { useQuery } from '@tanstack/react-query'

export interface IMaxLeverageParams {
  market: Market
  targetLTV: number
  swapSlippage: number
  leverageType: LeverageType
  leftCoinType: string
  inputCoinType: string
  rightCoinType: string
}
export const useMaxLeverage = (params?: IMaxLeverageParams) => {
  const leverageClient = useLeverageClient()

  const queryFn = useCallback(async () => {
    if (!leverageClient || !params) {
      return 0
    }
    const {
      market,
      targetLTV,
      swapSlippage,
      leverageType,
      leftCoinType,
      inputCoinType,
      rightCoinType
    } = params
    const amount = 1n
    return leverageType === LeverageType.Long
      ? LeverageOperation.maxLeverageForLong(
          market,
          Decimal.fromNumber(targetLTV),
          leftCoinType,
          amount,
          inputCoinType,
          rightCoinType,
          Decimal.fromNumber(swapSlippage)
        )
      : LeverageOperation.maxLeverageForShort(
          market,
          Decimal.fromNumber(targetLTV),
          leftCoinType,
          amount,
          inputCoinType,
          rightCoinType,
          Decimal.fromNumber(swapSlippage)
        )
  }, [leverageClient, params])
  return useQuery({
    queryKey: [
      'fetchMaxLeverage',
      params?.market?.id,
      params?.targetLTV,
      params?.swapSlippage,
      params?.leverageType,
      params?.leftCoinType,
      params?.inputCoinType,
      params?.rightCoinType
    ],
    queryFn,
    enabled: !!leverageClient,
    refetchOnWindowFocus: false,
    refetchOnMount: false
  })
}
