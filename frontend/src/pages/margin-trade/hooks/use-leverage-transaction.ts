import { usePebbleSDK } from '@/hooks/use-pebble-sdk'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { useContractMutation } from '@/hooks/use-contract-mutation'
import { ContractError } from '@/types/error'
import {
  LeverageObligation,
  prepareCoinsForAmount
} from '@pebble-protocol/pebble-sdk'
import { Transaction } from '@mysten/sui/transactions'
import { parseUnits } from '@/lib/units'
import { useAllBalance } from '@/hooks/use-all-balance'
import { useLeverageClient } from './use-leverage-client'
import { LeverageTransactionType } from '../types'
import { useLeveragePosition } from './use-leverage-position'

interface ILeverageTransactionParams {
  operationType: LeverageTransactionType
  amount: string
  coinType: string
  decimals: number
  position: LeverageObligation
}

export const useLeverageTransaction = (
  marketName: string,
  ownerCapId: string
) => {
  const { userAddress } = usePebbleWallet()
  const { pebbleClient, walletAdapter } = usePebbleSDK()
  const leverageClient = useLeverageClient()
  const { refetch: refetchAllBalance } = useAllBalance()
  const { refetch: refetchLeveragePosition } = useLeveragePosition()

  const mutationFn = async (params: ILeverageTransactionParams) => {
    if (!leverageClient) {
      throw new ContractError('❌ Failed to fetch obligationInfo')
    }
    const { operationType, amount, coinType, position } = params
    console.log('OperationParams', operationType, amount, coinType)

    let confirmed
    const amountBigInt = parseUnits(String(amount), params.decimals)
    try {
      // Transaction mode: build and execute transaction
      const tx = new Transaction()
      const coinObjectId = await prepareCoinsForAmount(
        tx,
        pebbleClient.provider,
        userAddress,
        coinType,
        amountBigInt
      )
      if (operationType === LeverageTransactionType.Repay) {
        await leverageClient.populateRepayTransaction(
          tx,
          ownerCapId,
          position,
          coinObjectId,
          walletAdapter
        )
      } else if (operationType === LeverageTransactionType.Deposit) {
        await leverageClient.populateDepositTransaction(
          tx,
          ownerCapId,
          position,
          coinObjectId
        )
      }
      confirmed = await walletAdapter.signAndExecuteTransaction(tx)
    } catch (error) {
      console.error(error)
      throw new ContractError('Failed to execute operation')
    }

    if (confirmed.effects?.status.status === 'failure') {
      throw new ContractError('', confirmed.digest)
    }

    refetchAllBalance()
    refetchLeveragePosition()
    return confirmed
  }

  return {
    operation: useContractMutation({
      mutationKey: [marketName, ownerCapId, userAddress],
      mutationFn: mutationFn
    })
  }
}
