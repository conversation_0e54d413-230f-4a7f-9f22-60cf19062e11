import { usePebbleSDK } from '@/hooks/use-pebble-sdk'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { useContractMutation } from '@/hooks/use-contract-mutation'
import { ContractError } from '@/types/error'
import { Decimal, LeverageObligation } from '@pebble-protocol/pebble-sdk'
import { Transaction } from '@mysten/sui/transactions'
import { useAllBalance } from '@/hooks/use-all-balance'
import { useLeverageClient } from './use-leverage-client'
import { useLeveragePosition } from './use-leverage-position'
import { useMarketInfo } from '@/hooks/use-market'

interface ILeverageDecreaseParams {
  leftCoinType: string
  percentage: number
  swapSlippage: number
  position: LeverageObligation
}

export const useLeverageDecrease = (marketName: string, ownerCapId: string) => {
  const { userAddress } = usePebbleWallet()
  const { walletAdapter } = usePebbleSDK()
  const leverageClient = useLeverageClient()
  const { refetch: refetchAllBalance } = useAllBalance()
  const { refetch: refetchLeveragePosition } = useLeveragePosition()
  const marketInfo = useMarketInfo(marketName)

  const mutationFn = async (params: ILeverageDecreaseParams) => {
    if (!leverageClient || !marketInfo) {
      throw new ContractError('❌ Failed to fetch obligationInfo')
    }
    const { percentage, swapSlippage, leftCoinType, position } = params
    console.log('useLeverageDecrease', percentage, swapSlippage, leftCoinType)
    let confirmed
    try {
      // Transaction mode: build and execute transaction
      const tx = new Transaction()
      await leverageClient.reduceLeverageSize(
        tx,
        leftCoinType,
        marketInfo.market,
        ownerCapId,
        position,
        Decimal.fromNumber(percentage),
        swapSlippage,
        walletAdapter
      )
      confirmed = await walletAdapter.signAndExecuteTransaction(tx)
    } catch (error) {
      console.error(error)
      throw new ContractError('Failed to execute operation')
    }

    if (confirmed.effects?.status.status === 'failure') {
      throw new ContractError('', confirmed.digest)
    }

    refetchAllBalance()
    refetchLeveragePosition()
    return confirmed
  }

  return {
    operation: useContractMutation({
      mutationKey: [marketName, ownerCapId, userAddress],
      mutationFn: mutationFn
    })
  }
}
