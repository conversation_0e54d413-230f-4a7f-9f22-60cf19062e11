import { useCallback } from 'react'
import { Transaction } from '@mysten/sui/transactions'
import { useLeverageClient } from './use-leverage-client'
import { usePebbleSDK } from '@/hooks/use-pebble-sdk'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { useContractMutation } from '@/hooks/use-contract-mutation'
import { ContractError } from '@/types/error'
import { prepareCoinsForAmount } from '@pebble-protocol/pebble-sdk'
import { useAllBalance } from '@/hooks/use-all-balance'
import type { SuiTransactionBlockResponse } from '@mysten/sui/client'
import type { LeverageOperation } from '@pebble-protocol/pebble-sdk'
import { useLeveragePosition } from './use-leverage-position'

export enum LeverageType {
  Long = 'long',
  Short = 'short'
}

export interface ILeverageParams {
  leverageOperation: LeverageOperation
  amount: bigint
  leverage: number
  swapSlippage?: number
  leverageType: LeverageType
  inputCoinType: string
  ownerCapId?: string
}

export interface ILeverageResult {
  isLoading: boolean
  error: string | null
  execute: (params: ILeverageParams) => Promise<string | null>
}

export const useLeverage = (): ILeverageResult => {
  const leverageClient = useLeverageClient()
  const { pebbleClient, walletAdapter } = usePebbleSDK()
  const { userAddress } = usePebbleWallet()
  const { refetch: refetchPositions } = useLeveragePosition()
  const { refetch: refetchAllBalance } = useAllBalance()

  const mutationFn = useCallback(
    async (params: ILeverageParams): Promise<SuiTransactionBlockResponse> => {
      if (!leverageClient || !pebbleClient || !userAddress || !walletAdapter) {
        throw new ContractError(
          '❌ LeverageClient, PebbleClient, userAddress, or walletAdapter not available'
        )
      }

      const {
        leverageOperation,
        amount,
        leverage,
        swapSlippage = 0.01,
        leverageType,
        inputCoinType,
        ownerCapId
      } = params

      console.log(
        'LeverageParams:',
        'leverageType=',
        leverageType,
        'swapSlippage=',
        swapSlippage,
        'amount=',
        amount.toString(),
        'leverage=',
        leverage,
        'inputCoinType=',
        inputCoinType,
        'ownerCapId=',
        ownerCapId
      )

      console.log(
        `Maximum leverage available: ${leverageOperation.maxLeverage.asNumber()}x`
      )

      if (leverage > leverageOperation.maxLeverage.asNumber()) {
        throw new ContractError(
          `❌ Requested leverage ${leverage}x exceeds maximum ${leverageOperation.maxLeverage.asNumber().toFixed(1)}x`
        )
      }
      // Create transaction
      const tx = new Transaction()
      tx.setSender(userAddress)
      console.log('prepareCoinsForAmount', amount)
      // Prepare coin for transaction
      const coinObjectId = await prepareCoinsForAmount(
        tx,
        pebbleClient.provider,
        userAddress,
        `0x${inputCoinType}`,
        amount
      )

      // Execute the leverage transaction
      if (ownerCapId) {
        // manage size
        await leverageClient.populateLeverageOperation(
          tx,
          ownerCapId,
          leverageOperation,
          swapSlippage,
          walletAdapter,
          coinObjectId
        )
      } else {
        await leverageClient.openAndApplyOperation(
          tx,
          leverageOperation,
          swapSlippage,
          walletAdapter,
          coinObjectId
        )
      }
      console.log('tx=', tx.getData().commands)
      // Execute transaction using wallet adapter
      const result = await walletAdapter.signAndExecuteTransaction(tx)

      if (result.effects?.status.status === 'failure') {
        throw new ContractError('❌ Transaction failed', result.digest)
      }

      // Refresh data after successful transaction
      refetchPositions()
      refetchAllBalance()

      return result
    },
    [
      leverageClient,
      pebbleClient,
      userAddress,
      walletAdapter,
      refetchPositions,
      refetchAllBalance
    ]
  )

  const operation = useContractMutation({
    mutationKey: ['leverage', userAddress],
    mutationFn: mutationFn
  })

  const execute = useCallback(
    async (params: ILeverageParams): Promise<string | null> => {
      try {
        const result = await operation.mutateAsync(params)
        return result.digest
      } catch (error) {
        console.error('Leverage operation failed:', error)
        return null
      }
    },
    [operation]
  )

  return {
    isLoading: operation.isPending,
    error: operation.error?.message || null,
    execute
  }
}
