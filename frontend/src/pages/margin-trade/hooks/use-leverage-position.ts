import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { useLeverageClient } from './use-leverage-client'
import { useQuery } from '@tanstack/react-query'

export const useLeveragePosition = () => {
  const leverageClient = useLeverageClient()
  const { userAddress } = usePebbleWallet()

  const queryFn = async () => {
    const obligations =
      await leverageClient?.getObligationsFromWallet(userAddress)
    if (!obligations || obligations.length === 0) {
      return []
    }
    return await Promise.all(
      obligations.map(async (ownerCap) => {
        const position = await leverageClient?.getObligationSummary(ownerCap)
        return {
          ownerCapId: ownerCap,
          position: position
        }
      })
    )
  }

  return useQuery({
    queryKey: ['leverage-position', userAddress],
    queryFn,
    enabled: !!userAddress
  })
}
