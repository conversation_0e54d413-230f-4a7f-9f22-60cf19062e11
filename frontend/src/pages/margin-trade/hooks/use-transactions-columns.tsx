import type { ColumnDef } from '@tanstack/react-table'
import type { mockTransactionsData } from '../mock'
import { TransactionTypeEnum } from '../types'
import { Button } from '@/components/ui/button'
import { WebIcon } from '@/assets/icons'
import { getIconUrl } from '@/lib/assets'
import { Avatar } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'

export const useTransactionsColumns = () => {
  const columns: ColumnDef<(typeof mockTransactionsData)[number]>[] = [
    {
      id: 'date',
      header: 'Date',
      accessorKey: 'date'
    },
    {
      id: 'type',
      header: 'Type',
      accessorKey: 'type',
      cell: ({ row }) => {
        const type = row.original.type
        const badgeVariant =
          type === TransactionTypeEnum.Liquidated ? 'red' : 'yellow'

        return (
          <Badge variant={badgeVariant}>
            {type === TransactionTypeEnum.Liquidated
              ? 'Liquidated'
              : type === TransactionTypeEnum.DecreaseSize
                ? 'Decrease Size'
                : 'Increase Leverage'}
          </Badge>
        )
      }
    },
    {
      id: 'asset',
      header: 'Asset',
      accessorKey: 'asset',
      cell: ({ row }) => {
        const asset = row.original.asset
        return (
          <div className="flex items-center gap-x-1.5">
            <Avatar src={getIconUrl('usdc', 'svg')} alt={asset} />
            <span className="text-xs opacity-80">{asset}</span>
          </div>
        )
      }
    },
    {
      id: 'amount',
      header: 'Amount',
      accessorKey: 'amount'
    },
    {
      id: 'positionSize',
      header: 'Position Size',
      accessorKey: 'positionSize'
    },
    {
      id: 'hash',
      header: () => null,
      accessorKey: 'hash',
      size: 14,
      enableSorting: false,
      cell: ({ row }) => {
        const hash = row.original.hash
        return (
          <div className="flex items-center justify-end">
            <Button
              onClick={() => {
                window.open(hash, '_blank')
              }}
              variant="icon"
              className="h-[14px] w-[14px]">
              <WebIcon className="size-[14px]" />
            </Button>
          </div>
        )
      }
    }
  ]

  return {
    columns
  }
}
