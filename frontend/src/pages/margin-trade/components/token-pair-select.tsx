import { Avatar } from '@/components/ui/avatar'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger
} from '@/components/ui/select'
import { getIconUrl } from '@/lib/assets'
import type { IMarginTradeItem } from '@/types/margin-trade'

export const TokenPairSelect = ({
  tokenPairList,
  selectedItem,
  onSelectChange
}: {
  tokenPairList: IMarginTradeItem[]
  selectedItem: IMarginTradeItem
  onSelectChange: (value: string) => void
}) => {
  return (
    <Select value={selectedItem.id.toString()} onValueChange={onSelectChange}>
      <SelectTrigger className="[&_svg]:size-2 gap-3 *:data-[slot=select-value]:gap-3">
        {selectedItem && <TokenPairItem item={selectedItem} />}
      </SelectTrigger>
      <SelectContent>
        {tokenPairList.map((item) => (
          <SelectItem
            className="w-[174px]"
            value={item.id.toString()}
            key={item.id}>
            <TokenPairItem item={item} />
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

const TokenPairItem = ({ item }: { item: IMarginTradeItem }) => {
  return (
    <div className="flex items-center gap-x-1.5">
      <div className="relative mr-4">
        <Avatar src={getIconUrl('sui')} alt="sui-logo" size={24} />
        <Avatar
          src={getIconUrl('usdc', 'svg')}
          alt="usdc-logo"
          size={24}
          className="absolute left-4 top-0"
        />
      </div>
      <div className="flex flex-col items-start">
        <div className="text-2xl">
          {item.tokenInfo0.symbol}/{item.tokenInfo1.symbol}
        </div>
        <div className="text-xs opacity-60">
          {item.fromMarket}{' '}
          {item.leverageMultiplier > 0 ? ` ${item.leverageMultiplier}x` : ''}
        </div>
      </div>
    </div>
  )
}
