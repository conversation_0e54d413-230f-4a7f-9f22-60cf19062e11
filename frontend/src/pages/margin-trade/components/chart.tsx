import { CandlestickChart } from '@/components/chart/candlestick-chart'
import { useGetMarginTradeKline } from '@/queries/kline'
import type { IKLineInfo } from '@/types/kline'
import { ResolutionKey, BAR_DURATION_MAP } from '@/components/chart/types'
import { useState, useMemo, useCallback, useEffect, useRef } from 'react'
import type {
  UTCTimestamp,
  LogicalRange,
  CandlestickData
} from 'lightweight-charts'
import KLineService from '@/services/kline'
import type { IChartApi } from 'lightweight-charts'
import { SUI_COIN_TYPE } from '@/lib/coin'
import {
  useKlineSSE,
  type SSEError,
  type ConnectionQuality
} from '@/hooks/use-kline-sse'

interface ChartProps {
  realtimeEnabled?: boolean
  realtimeInterval?: number // 已废弃，保持向后兼容
  coinType?: string
  useSSE?: boolean // 是否使用SSE进行实时更新
}

export function Chart({
  realtimeEnabled = false,
  coinType = SUI_COIN_TYPE,
  useSSE = true
}: ChartProps) {
  // 🔍 调试：只在coinType变化时记录
  const prevCoinTypeRef = useRef<string | undefined>(undefined)
  if (prevCoinTypeRef.current !== coinType) {
    console.log(
      '🔄 Chart component coinType changed:',
      prevCoinTypeRef.current,
      '->',
      coinType
    )
    prevCoinTypeRef.current = coinType
  }

  const [bar, setBar] = useState<ResolutionKey>(ResolutionKey.Min15)

  // 🔥 新的业务逻辑状态管理
  const [initialDataLoaded, setInitialDataLoaded] = useState(false)
  const [isLoadingHistory, setIsLoadingHistory] = useState(false)
  const [historicalData, setHistoricalData] = useState<IKLineInfo[]>([])

  // 图表相关refs
  const chartRef = useRef<IChartApi | null>(null)
  const seriesRef = useRef<{
    update: (data: CandlestickData<UTCTimestamp>) => void
  } | null>(null)

  // 数据管理refs
  const lastFromTime = useRef<number>(0)
  const hasMoreData = useRef<boolean>(true)
  const lastUpdateTime = useRef<number>(0)
  const visibleRangeRef = useRef<{ from: number; to: number } | null>(null)

  // 常量配置
  const initialSize = 300
  const fetchSize = initialSize + 1
  const additionalSize = 50

  // 🔥 计算初始数据的时间范围
  const { toTime, fromTime } = useMemo(() => {
    const toTime = Date.now()
    const fromTime = toTime - initialSize * BAR_DURATION_MAP[bar]
    return { toTime, fromTime }
  }, [bar])

  // 🔥 初始数据查询 - 只获取15分钟数据
  const { data, isSuccess } = useGetMarginTradeKline(
    {
      address: coinType,
      bar,
      fromTime,
      toTime,
      size: fetchSize
    },
    {
      enabled: !!coinType && !initialDataLoaded // 只有在coinType存在且初始数据未加载时才查询
    }
  )

  // 🔥 监听初始数据加载完成
  useEffect(() => {
    if (isSuccess && data?.data && data.data.length > 0 && coinType) {
      console.log(
        '📊 Initial K-line data loaded, ready to start SSE connection - coinType:',
        coinType
      )
      setInitialDataLoaded(true)

      // 设置最后更新时间为最新数据的时间
      const latestData = data.data[data.data.length - 1]
      if (latestData) {
        lastUpdateTime.current = latestData.t
      }
    } else if (!coinType && initialDataLoaded) {
      // coinType为空时重置状态
      setInitialDataLoaded(false)
      console.log('📊 coinType is empty, disabling SSE connection')
    }
  }, [isSuccess, data, coinType, initialDataLoaded])

  // 🔥 SSE连接 - 只有在初始数据加载完成后才连接
  const {
    connectionStatus,
    disconnect: sseDisconnect,
    connectionQuality,
    lastError,
    isOnline,
    browserSupported
  } = useKlineSSE({
    symbol: coinType,
    bar,
    enabled: realtimeEnabled && useSSE && initialDataLoaded && !!coinType,
    onKlineUpdate: useCallback((klineData: IKLineInfo) => {
      // 🔥 只更新比当前最新数据更新的数据
      if (
        klineData.t > lastUpdateTime.current &&
        seriesRef.current &&
        chartRef.current
      ) {
        lastUpdateTime.current = klineData.t

        // 更新图表数据
        const newCandleData: CandlestickData<UTCTimestamp> = {
          time: (klineData.t / 1000) as UTCTimestamp,
          open: klineData.o,
          high: klineData.h,
          low: klineData.l,
          close: klineData.c
        }

        try {
          seriesRef.current.update(newCandleData)
          console.log(
            '✅ Chart data updated successfully - time:',
            newCandleData.time,
            'close:',
            newCandleData.close
          )
        } catch (error) {
          console.error('❌ Chart data update failed:', error)
        }
      } else if (!seriesRef.current || !chartRef.current) {
        console.warn('⚠️ Chart not ready, skipping data update')
      } else {
        console.log(
          '📊 Skipping old data - received:',
          klineData.t,
          'current latest:',
          lastUpdateTime.current
        )
      }
    }, []),
    onConnected: useCallback((connectionId: string) => {
      console.log('🔗 K-line SSE connected successfully, ID:', connectionId)
    }, []),
    onError: useCallback((error: SSEError) => {
      console.error('❌ K-line SSE connection error:', {
        type: error.type,
        message: error.message,
        retryable: error.retryable,
        timestamp: new Date(error.timestamp).toISOString()
      })
    }, []),
    onQualityChange: useCallback((quality: ConnectionQuality) => {
      // 连接质量监控日志
      if (quality.messageCount % 10 === 0 && quality.messageCount > 0) {
        console.log('📊 Connection quality stats:', {
          latency: `${quality.latency}ms`,
          messages: quality.messageCount,
          errors: quality.errorCount,
          reconnects: quality.reconnectCount,
          lastMessage: new Date(quality.lastMessageTime).toISOString()
        })
      }
    }, [])
  })

  // 🔥 历史数据加载函数 - 只调用API，不启动SSE
  const loadMoreHistoricalData = useCallback(async () => {
    if (
      isLoadingHistory ||
      !hasMoreData.current ||
      !coinType ||
      !initialDataLoaded
    ) {
      return
    }

    console.log(
      '📈 Loading more historical data - coinType:',
      coinType,
      'bar:',
      bar
    )
    setIsLoadingHistory(true)

    if (lastFromTime.current === 0) {
      lastFromTime.current = fromTime
    }

    const historicalFromTime =
      lastFromTime.current - additionalSize * BAR_DURATION_MAP[bar]

    try {
      const result = await KLineService.getMarginTradeKline.call({
        address: coinType,
        bar,
        fromTime: historicalFromTime,
        toTime: lastFromTime.current,
        size: additionalSize
      })

      lastFromTime.current = historicalFromTime

      if (result && result.data && result.data.length > 0) {
        const currentVisibleRange = chartRef.current
          ?.timeScale()
          .getVisibleLogicalRange()
        if (currentVisibleRange) {
          visibleRangeRef.current = {
            from: currentVisibleRange.from + result.data.length,
            to: currentVisibleRange.to + result.data.length
          }
        }

        setHistoricalData((prev) => [...(result.data || []), ...prev])

        if (result.data.length < additionalSize) {
          hasMoreData.current = false
          console.log('📊 All historical data loaded')
        } else {
          console.log('📊 Historical data loaded, count:', result.data.length)
        }
      } else {
        hasMoreData.current = false
        console.log('📊 No more historical data available')
      }
    } catch (error) {
      console.error('❌ Failed to load historical data:', error)
      hasMoreData.current = false
    } finally {
      setIsLoadingHistory(false)
    }
  }, [bar, fromTime, coinType, initialDataLoaded, isLoadingHistory])

  // 🔥 处理可见范围变化，触发历史数据加载
  const handleVisibleRangeChange = useCallback(
    (logicalRange: LogicalRange) => {
      if (
        Number(logicalRange.from) < 10 &&
        !isLoadingHistory &&
        hasMoreData.current &&
        initialDataLoaded // 确保初始数据已加载
      ) {
        loadMoreHistoricalData()
      }
    },
    [loadMoreHistoricalData, isLoadingHistory, initialDataLoaded]
  )

  // 🔥 图表回调函数
  const handleChartReady = useCallback((chart: IChartApi) => {
    console.log('📊 Chart ready callback triggered')
    chartRef.current = chart
  }, [])

  const handleSeriesReady = useCallback(
    (series: { update: (data: CandlestickData<UTCTimestamp>) => void }) => {
      console.log('📊 Series ready callback triggered')
      seriesRef.current = series
    },
    []
  )

  // 🔥 计算图表数据 - 合并初始数据和历史数据
  const chartData = useMemo((): CandlestickData<UTCTimestamp>[] => {
    const historicalDataArray = Array.isArray(historicalData)
      ? historicalData
      : []
    const apiDataArray = Array.isArray(data?.data) ? data.data : []

    // 去重逻辑：避免重复数据
    const allData = [...historicalDataArray, ...apiDataArray]
    const uniqueData = allData.filter(
      (item, index, arr) => arr.findIndex((i) => i.t === item.t) === index
    )

    return uniqueData.map((item: IKLineInfo) => ({
      time: (item.t / 1000) as UTCTimestamp,
      open: item.o,
      high: item.h,
      low: item.l,
      close: item.c
    }))
  }, [data?.data, historicalData])

  // 🔥 处理历史数据加载后的可见范围调整
  useEffect(() => {
    if (visibleRangeRef.current && chartRef.current) {
      setTimeout(() => {
        chartRef.current
          ?.timeScale()
          .setVisibleLogicalRange(visibleRangeRef.current!)
        visibleRangeRef.current = null
      }, 0)
    }
  }, [historicalData])

  // 🔥 当时间周期变化时，重置所有状态
  useEffect(() => {
    console.log('🔄 Resetting chart state for bar change - bar:', bar)

    // 断开SSE连接
    sseDisconnect()

    // 重置所有状态
    setInitialDataLoaded(false)
    setHistoricalData([])
    setIsLoadingHistory(false)

    // 重置ref状态
    lastFromTime.current = 0
    hasMoreData.current = true
    lastUpdateTime.current = 0
    visibleRangeRef.current = null
    chartRef.current = null
    seriesRef.current = null
  }, [bar, sseDisconnect])

  // 🔥 当coinType变化时，重置所有状态
  useEffect(() => {
    if (coinType && prevCoinTypeRef.current !== coinType) {
      console.log(
        '🔄 coinType changed, resetting chart state:',
        prevCoinTypeRef.current,
        '->',
        coinType
      )

      // 断开SSE连接
      sseDisconnect()

      // 重置所有状态
      setInitialDataLoaded(false)
      setHistoricalData([])
      setIsLoadingHistory(false)

      // 重置ref状态
      lastFromTime.current = 0
      hasMoreData.current = true
      lastUpdateTime.current = 0
      visibleRangeRef.current = null
      chartRef.current = null
      seriesRef.current = null
    }
  }, [coinType, sseDisconnect])

  // 🔥 组件卸载时清理所有状态和连接
  useEffect(() => {
    return () => {
      console.log('🧹 Chart component unmounting, cleaning up...')
      sseDisconnect()
      setInitialDataLoaded(false)
      setHistoricalData([])
      setIsLoadingHistory(false)
    }
  }, [sseDisconnect])

  return (
    <div className="relative">
      {/* SSE连接状态指示器 */}
      {realtimeEnabled && useSSE && (
        <div className="absolute top-2 right-2 z-10 flex flex-col gap-1">
          {/* 主连接状态 */}
          <div className="flex items-center gap-2 px-2 py-1 bg-black/50 rounded text-xs text-white">
            <div
              className={`w-2 h-2 rounded-full ${
                !browserSupported
                  ? 'bg-gray-400'
                  : !isOnline
                    ? 'bg-orange-500'
                    : connectionStatus === 'connected'
                      ? 'bg-green-500'
                      : connectionStatus === 'connecting'
                        ? 'bg-yellow-500 animate-pulse'
                        : connectionStatus === 'error'
                          ? 'bg-red-500'
                          : 'bg-gray-500'
              }`}
            />
            <span>
              {!browserSupported
                ? 'Browser Unsupported'
                : !isOnline
                  ? 'Network Offline'
                  : connectionStatus === 'connected'
                    ? 'Real-time Data'
                    : connectionStatus === 'connecting'
                      ? 'Connecting...'
                      : connectionStatus === 'error'
                        ? 'Connection Error'
                        : 'Disconnected'}
            </span>
            {/* 连接质量指示器 */}
            {connectionStatus === 'connected' &&
              connectionQuality.latency > 0 && (
                <span className="text-gray-300">
                  ({connectionQuality.latency}ms)
                </span>
              )}
          </div>

          {/* 错误信息显示 */}
          {lastError && connectionStatus === 'error' && (
            <div className="px-2 py-1 bg-red-500/80 rounded text-xs text-white max-w-xs">
              <div className="font-medium">{lastError.type}</div>
              <div className="text-gray-200 truncate">{lastError.message}</div>
            </div>
          )}
        </div>
      )}

      <CandlestickChart
        data={chartData}
        currentResolution={bar}
        onResolutionChange={setBar}
        onVisibleRangeChange={handleVisibleRangeChange}
        onChartReady={handleChartReady}
        onSeriesReady={handleSeriesReady}
        isLoadingMore={isLoadingHistory}
        className="border border-border-8 rounded-[10px] w-[936px] h-[400px] flex flex-col items-center justify-center p-4"
      />
    </div>
  )
}
