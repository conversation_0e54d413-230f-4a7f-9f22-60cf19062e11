import { useMemo, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import type { LeverageObligation } from '@pebble-protocol/pebble-sdk'
import type { IMarginTradeItem } from '@/types/margin-trade'
import { Button } from '@/components/ui/button'
import { useLeverageDecrease } from '../hooks/use-leverage-decrease'
import { SlippageSetting } from '@/components/dialogs/slippage-setting'
import { getIconUrl } from '@/lib/assets'
import { Avatar } from '@/components/ui/avatar'
import { formatNumberWithSuperscriptZeros } from '@/lib/number'
import { useMarketInfo } from '@/hooks/use-market'

export const ClosePosition = ({
  marketName,
  ownerCapId,
  position,
  tradeItem
}: {
  marketName: string
  ownerCapId: string
  position: LeverageObligation
  tradeItem?: IMarginTradeItem
}) => {
  const marketInfo = useMarketInfo(marketName)
  const leftCoinType = useMemo(() => {
    if (!tradeItem) return undefined
    return tradeItem.tokenInfo0
  }, [tradeItem])
  const rightCoinType = useMemo(() => {
    if (!tradeItem) return undefined
    return tradeItem.tokenInfo1
  }, [tradeItem])
  const [closePositionTab, setClosePositionTab] = useState<string>('left')
  const { operation: leverageDecrease } = useLeverageDecrease(
    marketName,
    ownerCapId
  )
  const [slippage, setSlippage] = useState<number>(0.01)
  const inputCoinType = useMemo(() => {
    if (closePositionTab === 'left') {
      return leftCoinType
    }
    return rightCoinType
  }, [closePositionTab, leftCoinType, rightCoinType])

  const maxWithdrawAmount = useMemo(() => {
    if (!marketInfo?.market) return 0
    return Number(
      (
        position.netValueUSD(marketInfo?.market).asNumber() /
        Number(inputCoinType?.price)
      ).toFixed(6)
    )
  }, [position, inputCoinType, marketInfo])

  const handleClosePosition = () => {
    const selectedCoinType =
      closePositionTab === 'left'
        ? (tradeItem?.token0 ?? '')
        : (tradeItem?.token1 ?? '')
    leverageDecrease.mutate({
      leftCoinType: selectedCoinType,
      percentage: 1,
      swapSlippage: slippage,
      position
    })
  }
  return (
    <>
      <div className="mb-6 px-6">
        <Tabs
          value={closePositionTab}
          orientation="vertical"
          onValueChange={(value) => setClosePositionTab(value)}>
          <TabsList
            className="pl-0 w-full grid grid-cols-2"
            aria-label="deposit tabs">
            <TabsTrigger value="left">
              Close to {leftCoinType?.symbol}
            </TabsTrigger>
            <TabsTrigger value="right">
              Close to {rightCoinType?.symbol}
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <div className="px-6 pb-6 flex flex-col gap-y-6">
        <div className="flex overflow-hidden rounded-xl border border-border-8 bg-border-2 justify-between items-center p-4">
          <div className="flex flex-col gap-y-2">
            <span className="text-xs opacity-60">You Receive</span>
            <div className="flex items-center gap-x-2">
              <Avatar
                size={24}
                src={getIconUrl('sui')}
                alt={inputCoinType?.symbol ?? ''}
                fallback={inputCoinType?.symbol ?? ''}
              />
              <span className="text-2xl">{inputCoinType?.symbol}</span>
            </div>
          </div>
          <div className="flex flex-col gap-y-2 items-end">
            <span className="text-2xl opacity-60">{maxWithdrawAmount}</span>
            <span className="text-xs opacity-40">
              ~$
              {formatNumberWithSuperscriptZeros(
                maxWithdrawAmount * Number(inputCoinType?.price)
              )}
            </span>
          </div>
        </div>
        <Button variant="secondary" onClick={handleClosePosition}>
          Close Position
        </Button>
        <div className="flex flex-col gap-y-3 text-xs">
          <span className="flex items-center justify-between">
            <span className="opacity-60">Transaction Settings</span>
            <SlippageSetting slippage={slippage} onConfirm={setSlippage} />
          </span>
        </div>
      </div>
    </>
  )
}
