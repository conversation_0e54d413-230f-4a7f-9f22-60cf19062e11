import { type FC } from 'react'
import {
  AssetsList,
  PositionStatusBar
} from '@/pages/market/components/market-overview'
import { MarketAssetsEnum } from '@/pages/market/types'
import { Overview } from '@/pages/dashboard/components/overview'
import { useParams } from 'react-router'
import { useLeverageDashboard } from '../hooks/use-leverage-dashboard'

export const DashboardOverview: FC<{
  marketName?: string
}> = ({ marketName = '' }) => {
  const { leftToken, rightToken } = useParams()
  const { data } = useLeverageDashboard(
    marketName ?? '',
    leftToken ?? '',
    rightToken ?? ''
  )

  return (
    <div className="flex flex-col gap-y-6">
      <Overview data={data} />
      <div className="flex gap-6 items-center pb-3">
        <div className="underline mt-1">Positions Status</div>
        <PositionStatusBar
          ltv={data?.ltv ?? 0}
          warningLtv={data?.warnLtv ?? 0}
          maxLtv={data?.maxLtv ?? 0}
          leftAmount={data?.surplus ?? 0}
        />
      </div>
      <div className="flex justify-between gap-6">
        <AssetsList
          marketName={marketName}
          marketType={data?.marketType ?? ''}
          assetsType={MarketAssetsEnum.Supplied}
          showOperation={false}
          total={data.supplied}
          data={data.collaterals}
          className="border border-border-8 rounded-[10px]"
          innerClassName="px-6 py-3"
          tableClassName="px-6 pb-3"
        />
        <AssetsList
          marketName={marketName}
          marketType={data?.marketType ?? ''}
          assetsType={MarketAssetsEnum.Borrowed}
          showOperation={false}
          total={data.borrowed}
          data={data.debts}
          className="border border-border-8 rounded-[10px]"
          innerClassName="px-6 py-3"
          tableClassName="px-6 pb-3"
        />
      </div>
    </div>
  )
}
