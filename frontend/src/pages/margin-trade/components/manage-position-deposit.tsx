import { Button } from '@/components/ui/button'
import { useMemo, useState } from 'react'
import {
  mockMarginTradeAdjustLeverageDetailPredictionData,
  mockMarginTradeClosePositionDetailPredictionData,
  mockMarginTradeDepositData,
  mockMarginTradeRepayDeptDetailPredictionData,
  mockMarginTradeTopupCollateralPredictionData
} from '../mock'
import { Slider } from '@/components/ui/slider'
import { DashboardRectIcon } from '@/assets/icons'
import { BalanceInput } from '@/components/balance-input'
import {
  LEVERAGE_MIN_MULTIPLIER,
  LeverageTransactionType,
  MarginTradePositionActionEnum
} from '../types'
import { DepositCollapsibleItem } from '@/components/deposit-collapsible-item'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select'
import { enumToArray } from '@/lib/enum'
import { cn } from '@/lib/utils'
import { Avatar } from '@/components/ui/avatar'
import { useNavigate } from 'react-router'
import { getIconUrl } from '@/lib/assets'
import { LeverageObligation } from '@pebble-protocol/pebble-sdk'
import { useLeverageTransaction } from '../hooks/use-leverage-transaction'
import useMarketTokenInfo from '@/pages/market/hooks/use-market-token-info'
import { getShortName } from '@/utils/data'
import { formatUnits } from '@/lib/units'
import { SlippageSetting } from '@/components/dialogs/slippage-setting'
import { ManageSize } from './manage-size'
import type { IMarginTradeItem } from '@/types/margin-trade'
import { ClosePosition } from './close-position'
import { useLeverageDecrease } from '../hooks/use-leverage-decrease'
import { useLeverageAdjust } from '../hooks/use-leverage-adjust'
import { useLeverageMaxLeverage } from '../hooks/use-leverage-max-leverage'

export const ManagePositionDeposit = ({
  marketName,
  ownerCapId,
  position,
  tradeItem
}: {
  marketName: string
  ownerCapId: string
  position: LeverageObligation
  tradeItem?: IMarginTradeItem
}) => {
  // console.log('ownerCapId', ownerCapId)
  const navigate = useNavigate()
  const [managePositionAction, setManagePositionAction] =
    useState<MarginTradePositionActionEnum>(
      MarginTradePositionActionEnum.ManageSize
    )

  const [slippage, setSlippage] = useState<number>(0.005)
  const [balanceInputValue, setBalanceInputValue] = useState<string>()
  const [multiplier, setMultiplier] = useState<number>(
    mockMarginTradeDepositData.leverageMin
  )

  const { operation: leverageTransaction } = useLeverageTransaction(
    marketName,
    ownerCapId
  )

  const { operation: leverageDecrease } = useLeverageDecrease(
    marketName,
    ownerCapId
  )

  const { operation: leverageAdjust } = useLeverageAdjust(
    marketName,
    ownerCapId
  )

  const tokenAddress = useMemo(() => {
    if (managePositionAction === MarginTradePositionActionEnum.RepayDept) {
      return position.leverageObligation.info?.borrow ?? ''
    }
    if (
      managePositionAction === MarginTradePositionActionEnum.TopUpCollateral
    ) {
      return position.leverageObligation.info?.deposit ?? ''
    }
    return ''
  }, [position, managePositionAction])

  const { marketTokenInfo: tokenInfo } = useMarketTokenInfo(
    marketName,
    tokenAddress
  )

  const tokens = useMemo(() => {
    if (!tokenInfo) return undefined
    return {
      token0: {
        symbol: tokenInfo.tokenInfo.symbol,
        icon: getIconUrl('sui'),
        coinType: `0x${tokenInfo.token}`
      }
    }
  }, [tokenInfo])

  const operationParams = useMemo(() => {
    return {
      amount: balanceInputValue ?? '0',
      coinType: `0x${tokenAddress}`,
      decimals: Number(tokenInfo?.tokenInfo?.decimals)
    }
  }, [balanceInputValue, tokenAddress, tokenInfo])

  const maxLeverage = useLeverageMaxLeverage(position, slippage, tradeItem)

  const renderLeverageSettings = () => {
    if (managePositionAction !== MarginTradePositionActionEnum.AdjustLeverage)
      return null

    return (
      <div className="flex flex-col">
        <div className="mb-5">Leverage</div>
        <Slider
          value={[multiplier ?? 0]}
          onValueChange={(value) => setMultiplier(value[0])}
          min={LEVERAGE_MIN_MULTIPLIER}
          max={maxLeverage}
          step={0.1}
          className="w-full"
        />
        <div className="mt-3 text-xs flex items-center justify-between">
          <span>{LEVERAGE_MIN_MULTIPLIER}x</span>
          <span>{maxLeverage.toFixed(1)}x</span>
        </div>
      </div>
    )
  }

  const renderBalanceInput = () => {
    if (
      (managePositionAction !== MarginTradePositionActionEnum.ManageSize &&
        managePositionAction !== MarginTradePositionActionEnum.RepayDept &&
        managePositionAction !== MarginTradePositionActionEnum.ClosePosition &&
        managePositionAction !==
          MarginTradePositionActionEnum.TopUpCollateral) ||
      !tokens
    )
      return null

    if (managePositionAction === MarginTradePositionActionEnum.ClosePosition) {
      return (
        <div className="border border-border-8 rgba(227,224,215,0.02) rounded-md flex p-3 items-center justify-between">
          <div className="flex flex-col items-start gap-y-3">
            <div className="text-xs opacity-60">You Receive</div>
            <div className="flex items-center gap-x-1.5">
              <Avatar
                src={mockMarginTradeDepositData.tokens.token0.icon}
                alt={mockMarginTradeDepositData.tokens.token0.symbol}
              />
              <div>{mockMarginTradeDepositData.tokens.token0.symbol}</div>
            </div>
          </div>
          <div className="flex flex-col items-end justify-between">
            <span className="opacity-60 text-2xl">39.3829</span>
            <span className="text-xs opacity-40">~$102.29</span>
          </div>
        </div>
      )
    }

    const available = undefined
    let balanceTitle = undefined
    let maxAmount = undefined
    if (managePositionAction === MarginTradePositionActionEnum.RepayDept) {
      if (!tokenInfo) return undefined
      const borrowedAmount = Number(
        formatUnits(
          position.lendingMarketObligation
            .getBorrow(position.leverageObligation.info?.borrow ?? '')
            .amount(),
          Number(tokenInfo?.tokenInfo?.decimals)
        )
      )
      const maxRepayAmount =
        borrowedAmount > 0
          ? borrowedAmount + 1 / Number(tokenInfo.tokenInfo.price)
          : 0
      maxAmount = Number(maxRepayAmount.toFixed(6))
      // console.log('maxAmount', maxAmount)
      balanceTitle = 'Available'
    }
    let title = ''
    if (managePositionAction === MarginTradePositionActionEnum.RepayDept) {
      title = 'Repay asset'
    } else if (
      managePositionAction === MarginTradePositionActionEnum.TopUpCollateral
    ) {
      title = 'Deposit asset'
    }

    return (
      <BalanceInput
        value={balanceInputValue}
        onChange={(value) => {
          setBalanceInputValue(value)
        }}
        tokens={tokens}
        price={
          tokenInfo?.tokenInfo?.price
            ? Number(tokenInfo.tokenInfo.price)
            : undefined
        }
        available={available}
        balanceTitle={balanceTitle}
        title={title}
        maxAmount={maxAmount}
      />
    )
  }

  const shouldShowPrediction = useMemo(() => {
    if (managePositionAction === MarginTradePositionActionEnum.AdjustLeverage) {
      if (!multiplier || isNaN(Number(multiplier))) return false
    }

    if (
      managePositionAction === MarginTradePositionActionEnum.ManageSize ||
      managePositionAction === MarginTradePositionActionEnum.RepayDept
    )
      return !!balanceInputValue && !isNaN(Number(balanceInputValue))

    return true
  }, [balanceInputValue, managePositionAction, multiplier])

  const predictionData = useMemo(() => {
    switch (managePositionAction) {
      case MarginTradePositionActionEnum.AdjustLeverage:
        return mockMarginTradeAdjustLeverageDetailPredictionData
      case MarginTradePositionActionEnum.RepayDept:
        return mockMarginTradeRepayDeptDetailPredictionData
      case MarginTradePositionActionEnum.ClosePosition:
        return mockMarginTradeClosePositionDetailPredictionData
      case MarginTradePositionActionEnum.TopUpCollateral:
        return mockMarginTradeTopupCollateralPredictionData
      default:
        return []
    }
  }, [managePositionAction])

  const renderPrediction = () => {
    if (!shouldShowPrediction) return null

    return (
      <div className="flex flex-col gap-y-3 text-xs">
        {(managePositionAction ===
          MarginTradePositionActionEnum.ClosePosition ||
          managePositionAction ===
            MarginTradePositionActionEnum.AdjustLeverage) && (
          <div className="flex items-center justify-between">
            <span className="opacity-60">Transaction Settings</span>
            <SlippageSetting slippage={slippage} onConfirm={setSlippage} />
          </div>
        )}
        {predictionData.map((item) => (
          <DepositCollapsibleItem key={item.id} data={item} />
        ))}
      </div>
    )
  }

  const confirmButtonLabel = useMemo(() => {
    if (
      managePositionAction === MarginTradePositionActionEnum.TopUpCollateral
    ) {
      return 'Deposit'
    }
    if (managePositionAction === MarginTradePositionActionEnum.AdjustLeverage)
      return 'Adjust'
    if (managePositionAction === MarginTradePositionActionEnum.RepayDept)
      return 'Repay'
    if (managePositionAction === MarginTradePositionActionEnum.ClosePosition)
      return 'Close Position'
  }, [managePositionAction])

  const handleConfirm = () => {
    if (managePositionAction === MarginTradePositionActionEnum.AdjustLeverage) {
      console.log('Adjust Leverage')
      leverageAdjust.mutate({
        leftCoinType: tradeItem?.token0 ?? '',
        newLeverage: multiplier,
        swapSlippage: slippage,
        position
      })
      return
    }
    if (managePositionAction === MarginTradePositionActionEnum.RepayDept) {
      console.log('Repay')
      leverageTransaction.mutate({
        operationType: LeverageTransactionType.Repay,
        ...operationParams,
        position
      })
      return
    }
    if (
      managePositionAction === MarginTradePositionActionEnum.TopUpCollateral
    ) {
      console.log('TopUpCollateral')
      leverageTransaction.mutate({
        operationType: LeverageTransactionType.Deposit,
        ...operationParams,
        position
      })
      return
    }
    if (managePositionAction === MarginTradePositionActionEnum.ClosePosition) {
      console.log('Close Position')
      leverageDecrease.mutate({
        leftCoinType: tradeItem?.token0 ?? '',
        percentage: 0.98,
        swapSlippage: slippage,
        position
      })
      return
    }
  }

  return (
    <div className="border border-border-8 pt-0 rounded-[10px] w-[377px] h-full flex flex-col bg-deposit-gradient">
      <div
        className={cn(
          'mb-6 px-6 py-3 border-b border-border-5',
          (managePositionAction === MarginTradePositionActionEnum.ManageSize ||
            managePositionAction ===
              MarginTradePositionActionEnum.ClosePosition) &&
            'mb-3'
        )}>
        <Select
          value={managePositionAction}
          onValueChange={(key) => {
            setBalanceInputValue(undefined)
            if (key === 'dashboard') {
              const leftToken = getShortName(
                position.leverageObligation.info?.deposit ?? ''
              )
              const rightToken = getShortName(
                position.leverageObligation.info?.borrow ?? ''
              )
              navigate(
                `/margin-trade/dashboard/${marketName}/${leftToken}/${rightToken}`
              )
              return
            }
            setManagePositionAction(key as MarginTradePositionActionEnum)
          }}>
          <SelectTrigger className="text-sm">
            <SelectValue>{managePositionAction}</SelectValue>
          </SelectTrigger>
          <SelectContent>
            {enumToArray(MarginTradePositionActionEnum).map(
              ({ key, value }) => (
                <SelectItem className="w-[165px]" key={key} value={value}>
                  {value}
                </SelectItem>
              )
            )}
            <SelectItem
              onClick={() => {}}
              className="w-[165px] gap-x-1.5"
              key="dashboard"
              value="dashboard">
              <DashboardRectIcon />
              Dashboard
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      {managePositionAction === MarginTradePositionActionEnum.ManageSize ? (
        <ManageSize
          marketName={marketName}
          ownerCapId={ownerCapId}
          position={position}
          tradeItem={tradeItem}
        />
      ) : managePositionAction ===
        MarginTradePositionActionEnum.ClosePosition ? (
        <ClosePosition
          marketName={marketName}
          ownerCapId={ownerCapId}
          position={position}
          tradeItem={tradeItem}
        />
      ) : (
        <>
          <div className="px-6 pb-6 flex flex-col gap-y-6">
            {renderBalanceInput()}
            {renderLeverageSettings()}
            <Button variant="secondary" onClick={handleConfirm}>
              {confirmButtonLabel}
            </Button>
            {renderPrediction()}
          </div>
        </>
      )}
    </div>
  )
}
