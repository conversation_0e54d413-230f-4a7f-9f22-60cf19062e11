import { useMemo, useState, useCallback } from 'react'
import { DataTable } from '@/components/data-table'
import { getShortName } from '@/utils/data'
import { formatTimestamp } from '@/lib/formatter'
import { useInfiniteTableScroll } from '@/hooks/use-infinite-table-scroll'
import type { SortingState } from '@tanstack/react-table'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { TransactionProductEnum } from '@/types/user'
import { formatUnits } from '@/lib/units'
import BigNumber from 'bignumber.js'
import { useGetAllTransactionHistory } from '@/queries/history'
import { useTransactionsColumns } from '../hooks/use-transactions-columns'
import { TransactionTypeEnum } from '../types'

export const Transactions = () => {
  const { userAddress } = usePebbleWallet()
  const [sorting, setSorting] = useState<SortingState>([])

  const sortParams = useMemo(() => {
    const params: Record<string, 'asc' | 'desc' | null> = {}

    if (sorting.length === 0) {
      params.chainTimestampMSSort = 'desc'
    } else {
      sorting.forEach((sort) => {
        switch (sort.id) {
          case 'date':
            params.chainTimestampMSSort = sort.desc ? 'desc' : 'asc'
            break
          case 'type':
            params.typeSort = sort.desc ? 'desc' : 'asc'
            break
          case 'value':
            params.amountSort = sort.desc ? 'desc' : 'asc'
            break
          default:
            break
        }
      })
    }

    return params
  }, [sorting])

  const { columns } = useTransactionsColumns()
  const { flatData, isLoading, fetchMore, hasMore, setQuery } =
    useGetAllTransactionHistory(TransactionProductEnum.MarginTrande, {
      initialQuery: {
        chainTimestampMSSort: 'desc'
      }
    })

  const updateQueryWithSort = useCallback(() => {
    if (!userAddress) return

    setQuery({
      userAddress,
      product: TransactionProductEnum.MarginTrande,
      ...sortParams
    })
  }, [setQuery, sortParams, userAddress])

  useMemo(() => {
    if (sorting.length > 0) {
      updateQueryWithSort()
    }
  }, [updateQueryWithSort, sorting.length])

  const data = useMemo(() => {
    return flatData.map((v) => {
      const amount = BigNumber(
        formatUnits(v.amount, Number(v.tokenInfo.decimals))
      )
      const value = amount.multipliedBy(v.tokenInfo.price).toNumber()
      return {
        id: v.id,
        date: formatTimestamp(v.chainTimestampMS / 1000, 'MMM D HH:mm'),
        type: TransactionTypeEnum.Liquidated,
        asset: getShortName(v.token),
        amount: value.toString(),
        positionSize: '-',
        hash: v.txDigest
      }
    })
  }, [flatData])

  const { handleScroll, tableContainerRef } = useInfiniteTableScroll(
    fetchMore,
    {
      enabled: hasMore && !isLoading
    }
  )

  return (
    <div
      ref={tableContainerRef}
      onScroll={handleScroll}
      className="h-full overflow-auto">
      <DataTable
        data={data}
        columns={columns}
        isLoading={isLoading}
        size="md"
        sorting={sorting}
        onSortingChange={setSorting}
        serverSideSorting={true}
        classNames={{
          headerCell: 'px-3',
          contentCell: 'px-3'
        }}
      />
    </div>
  )
}
