import {
  B<PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON><PERSON>bItem,
  Bread<PERSON>rumbLink,
  Bread<PERSON>rumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'
import { useState } from 'react'
import { Link, useParams } from 'react-router'
import { Ta<PERSON>, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { DashboardOverview } from './dashboard-overview'
import { cn } from '@/lib/utils'
import { BackButton } from '@/components/buttons'
import { Transactions } from '../components/transactions'
import { DashboardTabEnum } from '../../dashboard/types'

// const menu = [
//   {
//     id: DashboardMenuEnum.MyLoan,
//     label: DashboardMenuEnum.MyLoan,
//     icon: <UserIcon />
//   }
// ]
export default function MarginTradeDashboard() {
  const { id, leftToken, rightToken } = useParams()
  const [tab, setTab] = useState(DashboardTabEnum.LoanMarketOverview)

  return (
    <section
      className={cn(
        'relative pt-16 pb-10 w-[1244px] flex flex-col gap-y-6',
        tab === DashboardTabEnum.TransactionHistory && 'h-[calc(100vh-70px)]'
      )}>
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink
              asChild
              className="opacity-60 hover:opacity-100 transition-all">
              <Link to="/margin-trade">Margin Trade</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <span>Dashboard</span>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="relative">
        <div className="flex items-center gap-x-1.5">
          <span className="text-2xl text-white">
            {leftToken}/{rightToken} Margin Trade Dashboard
          </span>
        </div>

        <div className="absolute top-0 right-[calc(100%+12px)] h-8 w-8">
          <BackButton className="h-8 w-8" />
        </div>
      </div>

      <Tabs
        value={tab}
        onValueChange={(item) => {
          setTab(item as DashboardTabEnum)
        }}
        orientation="vertical">
        <TabsList className="pl-0" aria-label="market tabs">
          <TabsTrigger value={DashboardTabEnum.LoanMarketOverview}>
            {DashboardTabEnum.LoanMarketOverview}
          </TabsTrigger>
          <TabsTrigger value={DashboardTabEnum.TransactionHistory}>
            {DashboardTabEnum.TransactionHistory}
          </TabsTrigger>
        </TabsList>
      </Tabs>

      {tab === DashboardTabEnum.LoanMarketOverview ? (
        <DashboardOverview marketName={id} />
      ) : (
        <Transactions />
      )}
    </section>
  )
}
