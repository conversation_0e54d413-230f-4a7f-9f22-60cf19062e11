import { useRoutes, Navigate } from 'react-router'
import DefaultLayout from '@/layouts/default'
import { routes, getRouteElement, getRouteChildren } from '@/config/routes'

function App() {
  const routeElements = routes.map((route) => ({
    path: route.path,
    element: getRouteElement(route.path),
    children: getRouteChildren(route.path)
  }))

  const appRoutes = [
    {
      path: '/',
      element: <DefaultLayout />,
      children: [
        { index: true, element: <Navigate replace to="/market" /> },
        ...routeElements,
        {
          path: '*',
          element: <Navigate replace to="/" />
        }
      ]
    }
  ]

  return useRoutes(appRoutes)
}

export default App
