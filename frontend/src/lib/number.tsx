import type { ReactNode } from 'react'

export const NUMBER_INPUT_SCALE = 18

export const isScientificNotation = (value: string | number) => {
  const str = String(value).toLowerCase()

  return /^-?\d+(\.\d+)?e[+-]?\d+$/.test(str)
}

const THRESHOLD = 0.0001
export const formatNumberWithSuperscriptZeros = (
  value: number | bigint | string,
  options?: {
    minimumFractionDigitsGreater1?: number
    maximumFractionDigitsGreater1?: number
    minimumFractionDigitsLess1?: number
    maximumFractionDigitsLess1?: number
    compact?: boolean
    disableSuperscriptZeros?: boolean
  }
): ReactNode => {
  const numValue = Number(value)

  const minimumFractionDigitsGreater1 =
    options?.minimumFractionDigitsGreater1 ?? 2
  const maximumFractionDigitsGreater1 =
    options?.maximumFractionDigitsGreater1 ?? 2
  const minimumFractionDigitsLess1 = options?.minimumFractionDigitsLess1 ?? 3
  const maximumFractionDigitsLess1 = options?.maximumFractionDigitsLess1 ?? 3
  const compact = options?.compact ?? true

  if (isNaN(numValue)) return 'NaN'
  if (numValue === 0) return '0.00'
  if (Math.abs(numValue) >= THRESHOLD) {
    return formatNumberAsCompact(value, {
      minimumFractionDigitsGreater1,
      maximumFractionDigitsGreater1,
      minimumFractionDigitsLess1,
      maximumFractionDigitsLess1,
      compact
    })
  }

  const absValue = Math.abs(numValue)
  const str = absValue.toFixed(20)
  const decimalIndex = str.indexOf('.')

  if (decimalIndex === -1)
    return formatNumberAsCompact(value, {
      minimumFractionDigitsGreater1,
      maximumFractionDigitsGreater1,
      minimumFractionDigitsLess1,
      maximumFractionDigitsLess1,
      compact
    })

  const afterDecimal = str.substring(decimalIndex + 1)
  const leadingZeros = afterDecimal.match(/^0*/)?.[0]?.length || 0

  if (leadingZeros >= 3 && !options?.disableSuperscriptZeros) {
    const significantPart = afterDecimal
      .substring(leadingZeros)
      .replace(/0+$/, '')

    if (significantPart.length === 0) return '0'

    const displayDigits = significantPart.substring(0, 4)

    return (
      <span>
        {numValue < 0 ? '-' : ''}0.0
        <sup className="text-[0.6em] leading-none">{leadingZeros}</sup>
        {displayDigits}
      </span>
    )
  }

  return formatNumberAsCompact(value, {
    minimumFractionDigitsGreater1,
    maximumFractionDigitsGreater1,
    minimumFractionDigitsLess1,
    maximumFractionDigitsLess1,
    compact
  })
}

const formatNumberAsCompact = (
  value: number | bigint | string,
  options?: {
    minimumFractionDigitsGreater1?: number
    maximumFractionDigitsGreater1?: number
    minimumFractionDigitsLess1?: number
    maximumFractionDigitsLess1?: number
    compact?: boolean
  }
) => {
  const numValue = Number(value)
  if (isNaN(numValue)) return 'NaN'

  const minimumFractionDigitsGreater1 =
    options?.minimumFractionDigitsGreater1 ?? 2
  const maximumFractionDigitsGreater1 =
    options?.maximumFractionDigitsGreater1 ?? 2
  const minimumFractionDigitsLess1 = options?.minimumFractionDigitsLess1 ?? 3
  const maximumFractionDigitsLess1 = options?.maximumFractionDigitsLess1 ?? 3
  const compact = options?.compact ?? true

  if (numValue === 0) {
    return Intl.NumberFormat('en', {
      minimumFractionDigits: minimumFractionDigitsGreater1,
      maximumFractionDigits: maximumFractionDigitsGreater1,
      notation: compact ? 'compact' : undefined
    })
      .format(numValue)
      .toLowerCase()
  }

  if (numValue < 1) {
    return Intl.NumberFormat('en', {
      minimumSignificantDigits: minimumFractionDigitsLess1,
      maximumSignificantDigits: maximumFractionDigitsLess1,
      notation: compact ? 'compact' : undefined
    })
      .format(numValue)
      .toLowerCase()
  }

  return Intl.NumberFormat('en', {
    minimumFractionDigits: minimumFractionDigitsGreater1,
    maximumFractionDigits: maximumFractionDigitsGreater1,
    notation: compact ? 'compact' : undefined
  })
    .format(numValue)
    .toLowerCase()
}
