export const SUI_COIN_TYPE = '0x2::sui::SUI'
export const SUI_COIN_TYPE_LONG =
  '0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI'
export const USDC_COIN_TYPE =
  '0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC'

export function getCoinSymbol(coinType: string): string {
  const parts = coinType.split('::')
  return parts[parts.length - 1]
}
