type EnumToOptions<T extends Record<string, string | number>> = {
  [K in keyof T]: T[K] extends string | number ? { key: K; value: T[K] } : never
}[keyof T][]

export function enumToArray<T extends Record<string, string | number>>(
  enumObj: T
): EnumToOptions<T> {
  return Object.entries(enumObj)
    .filter(([key]) => isNaN(Number(key)))
    .map(([key, value]) => ({ key, value })) as EnumToOptions<T>
}
