import dayjs from 'dayjs'

export const displayAddress = (
  address = '',
  startOffset = 4,
  endOffset = 4
): string => {
  if (!address) return '--'

  const lowerAddress = address.toUpperCase()

  return (
    lowerAddress.slice(0, startOffset) + '...' + lowerAddress.slice(-endOffset)
  )
}

export const formatTimestamp = (
  timestamp: number,
  template: string = 'DD/MM/YYYY'
) => {
  return dayjs.unix(timestamp).format(template)
}
