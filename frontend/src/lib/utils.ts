import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'
import { ChartRangeEnum } from '@/components/chart/types'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getMarketName(marketName: string) {
  if (marketName === 'MainMarket') {
    return 'Main Market'
  }
  return marketName
}

export function calculateTimeRange(range: ChartRangeEnum): {
  from: number
  to: number
} {
  const now = Date.now()
  let from: number

  switch (range) {
    case ChartRangeEnum.Day7:
      from = now - 7 * 24 * 60 * 60 * 1000
      break
    case ChartRangeEnum.Day30:
      from = now - 30 * 24 * 60 * 60 * 1000
      break
    case ChartRangeEnum.Month3:
      from = now - 3 * 30 * 24 * 60 * 60 * 1000
      break
    case ChartRangeEnum.Month6:
      from = now - 6 * 30 * 24 * 60 * 60 * 1000
      break
    case ChartRangeEnum.Year1:
      from = now - 365 * 24 * 60 * 60 * 1000
      break
    default:
      from = now - 7 * 24 * 60 * 60 * 1000 // Default to 7 days
  }

  return { from, to: now }
}
