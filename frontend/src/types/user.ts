import type { PositionOverviewMenuEnum } from '@/pages/portfolio/types'
import type { ITokenInfo } from './config'

export interface IObligationInfo {
  borrowIndex: string
  borrowIndexTime: number
  exchangeRate: string
  exchangeRateTime: number
  id: number
  marketType: string
  obligationObject: string
  owner: string
  token: string
}
export enum TransactionTypeEnum {
  Deposit = 1,
  Withdraw = 2,
  Borrow = 3,
  Repay = 4
}
export enum TransactionProductEnum {
  Market = 1,
  Multiply = 2,
  MarginTrande = 3,
  All = -999
}
export interface ITransactionHistory {
  id: string
  type: TransactionTypeEnum
  product: TransactionProductEnum
  marketType: string
  txDigest: string
  amount: bigint
  owner: string
  token: string
  chainTimestampMS: number
  tokenInfo: ITokenInfo
}
export interface PebblePointsData {
  nowSeason: number
  totalPoints: number
  pointsDetails: {
    points: number
    season: number
    pointsSource: number
  }[]
}

export interface PebblePointsConfig {
  nowSeason: string
  [PositionOverviewMenuEnum.Market]: string
  [PositionOverviewMenuEnum.Multiply]: string
  [PositionOverviewMenuEnum.MarginTrade]: string
}

export interface IObligationSupplyItem {
  principleAmount: bigint
  tokenInfo: ITokenInfo
}

export interface IObligationSupplyResponse {
  [key: string]: IObligationSupplyItem[]
}
