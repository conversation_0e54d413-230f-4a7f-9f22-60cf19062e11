export interface IBorrowAPYHistoryChartItem {
  borrowAPY: bigint
  id: number
  marketType: string
  t: number
  token: string
}

export interface IBorrowHistoryChartItem {
  id: number
  marketType: string
  t: number
  token: string
  totalBorrow: bigint
  tokenPrice: number
}

export interface ISupplyAPYHistoryChartItem {
  id: number
  marketType: string
  supplyAPY: bigint
  t: number
  token: string
}

export interface ISupplyHistoryChartItem {
  id: number
  marketType: string
  t: number
  token: string
  totalSupply: bigint
  tokenPrice: number
}

export interface IUtilizationHistoryChartItem {
  id: number
  marketType: string
  t: number
  token: string
  utilization: bigint
}

export interface IChartParams {
  from: number
  to: number
  marketType: string
  tokenAddress: string
}

export interface ITokenPriceHistoryChartItem {
  id: number
  marketType: string
  t: number
  token: string
  tokenPrice: number
}
