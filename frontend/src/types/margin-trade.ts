import type { ITokenInfo } from './config'

export interface IMarginTradeItem {
  fromMarket: string
  id: number
  leverageMultiplier: number
  longLiquidity: number
  price: number
  priceChange: number
  shortLiquidity: number
  token0: string
  token0SupplyAPY: number
  token1: string
  token1BorrowAPY: number
  tokenInfo0: ITokenInfo
  tokenInfo1: ITokenInfo
  longMaxLTV: number
  shortMaxLTV: number
  slippageLeftToRight: number
  slippageRightToLeft: number
}
