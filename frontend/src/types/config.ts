export interface INetworkConfig {
  network: string
  contract: {
    protocol: string
    coinDecimalsRegistry: string
    suiXOracle: string
    x: string
    protocolQuery: string
    math: string
    marketType: string[]
  }
  rpc: string
}

export interface IWeb3Config {
  web3Config: {
    sui: INetworkConfig[]
  }
  nowTime: string
}

export interface ITokenInfo {
  address: string
  decimals: string
  description: string
  icon: string
  id: number
  name: string
  price: string
  symbol: string
}

export interface IMarketConfig {
  id: number
  marketID: string
  marketType: string
  maxLTV: number
  warnLTV: number
  name: string
}
