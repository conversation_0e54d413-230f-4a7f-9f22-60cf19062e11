import type { QueryObserverOptions } from '@tanstack/react-query'

export type QueryOptions<TData, TTransformed = IResponse<TData>> = Omit<
  QueryObserverOptions<IResponse<TData>, Error, TTransformed>,
  'queryKey' | 'queryFn'
>
export interface IResponse<T> {
  code?: number
  message?: string
  data?: T | null
}
export interface IPagination<T> {
  content: T[]
  empty: boolean
  first: boolean
  last: boolean
  size: boolean
  totalElements: number
  totalPages: number
  pageable: {
    offset: number
    pageNumber: number
    pageSize: number
    sort: { sorted: boolean; unsorted: boolean; empty: boolean }
    unpaged: boolean
  }
}
