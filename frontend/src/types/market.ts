import type { ITokenInfo } from './config'

export interface IMarketInfo {
  id: string
  name: string
  borrowAPY: bigint
  liqLTV: number
  marketID: string
  marketType: string
  supplyAPY: bigint
  token: string
  tokenInfo: ITokenInfo
  totalBorrow: bigint
  totalSupply: bigint
  liqAvailable: bigint
  borrowCap: bigint
  utilization: bigint
  supplyCap: bigint
  previousData?: IPreviousData
  avgBorrowAPY180D?: number
  avgBorrowAPY30D?: number
  avgBorrowAPY90D?: number
  avgSupplyAPY180D?: number
  avgSupplyAPY30D?: number
  avgSupplyAPY90D?: number
  borrowFactor: number
  liqPenalty: string
}

export interface IPreviousData {
  previousExchangeRate: string
  previousBorrowIndex: string
  previousTimestamp: number
}
