import { Suspense } from 'react'
import { Outlet } from 'react-router'
import { useMediaQuery } from 'usehooks-ts'

import { DeviceWarning } from '@/components/device-warning'
import { Navbar } from '@/components/navbar'
import { Toaster } from '@/components/ui/sonner'
import { PebblePointsDrawer } from '@/components/pebble-points'
import { ConnectWalletDrawer } from '@/components/connect-wallet'

export default function DefaultLayout() {
  const isDesktop = useMediaQuery('(min-width: 768px)')

  return (
    <div className="relative flex min-h-screen flex-col bg-custom-gradient">
      {!isDesktop && <DeviceWarning />}
      <Navbar />
      <PebblePointsDrawer />
      <ConnectWalletDrawer />
      <main className="mx-auto">
        <Suspense fallback={<div>Loading...</div>}>
          <Outlet />
        </Suspense>
      </main>
      <Toaster />
    </div>
  )
}
