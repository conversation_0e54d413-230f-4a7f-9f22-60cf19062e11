import { create } from 'zustand'
import { CollateralActionEnum, DebtActionEnum } from '@/pages/market/types'

interface BaseDialogState<T = void> {
  isOpen: boolean
  id: string | null
  openModal: (id: string, ...args: T[]) => void
  closeModal: () => void
}

interface MarketDialogState<T = void> {
  isOpen: boolean
  id: string | null
  marketName: string
  coinType: string
  openModal: (
    id: string,
    marketName: string,
    coinType: string,
    ...args: T[]
  ) => void
  closeModal: () => void
}

interface SupplyDialogState {
  isOpen: boolean
  id: string | null
  marketName: string
  coinType: string
  isLp?: boolean
  openModal: (
    id: string,
    marketName: string,
    coinType: string,
    isLp?: boolean
  ) => void
  closeModal: () => void
}

interface DialogState {
  borrow: MarketDialogState
  claim: BaseDialogState
  collateral: MarketDialogState<CollateralActionEnum> & {
    action: CollateralActionEnum
  }
  debt: MarketDialogState<DebtActionEnum> & {
    action: DebtActionEnum
  }
  supply: SupplyDialogState
}

export const useDialogStore = create<DialogState>((set) => ({
  borrow: {
    isOpen: false,
    id: null,
    marketName: '',
    coinType: '',
    openModal: (id: string, marketName: string, coinType: string) =>
      set((state) => ({
        borrow: { ...state.borrow, isOpen: true, id, marketName, coinType }
      })),
    closeModal: () =>
      set((state) => ({ borrow: { ...state.borrow, isOpen: false, id: null } }))
  },
  claim: {
    isOpen: false,
    id: null,
    openModal: (id: string) =>
      set((state) => ({
        claim: { ...state.claim, isOpen: true, id }
      })),
    closeModal: () =>
      set((state) => ({ claim: { ...state.claim, isOpen: false, id: null } }))
  },
  collateral: {
    isOpen: false,
    id: null,
    action: CollateralActionEnum.Supply,
    marketName: '',
    coinType: '',
    openModal: (
      id: string,
      marketName: string,
      coinType: string,
      action: CollateralActionEnum
    ) =>
      set((state) => ({
        collateral: {
          ...state.collateral,
          isOpen: true,
          id,
          action,
          marketName,
          coinType
        }
      })),
    closeModal: () =>
      set((state) => ({
        collateral: { ...state.collateral, isOpen: false, id: null }
      }))
  },
  debt: {
    isOpen: false,
    id: null,
    action: DebtActionEnum.Borrow,
    marketName: '',
    coinType: '',
    openModal: (
      id: string,
      marketName: string,
      coinType: string,
      action: DebtActionEnum
    ) =>
      set((state) => ({
        debt: { ...state.debt, isOpen: true, id, action, marketName, coinType }
      })),
    closeModal: () =>
      set((state) => ({ debt: { ...state.debt, isOpen: false, id: null } }))
  },
  supply: {
    isOpen: false,
    id: null,
    isLp: false,
    marketName: '',
    coinType: '',
    openModal: (
      id: string,
      marketName: string,
      coinType: string,
      isLp?: boolean
    ) =>
      set((state) => ({
        supply: {
          ...state.supply,
          isOpen: true,
          id,
          isLp,
          marketName,
          coinType
        }
      })),
    closeModal: () =>
      set((state) => ({ supply: { ...state.supply, isOpen: false, id: null } }))
  }
}))
