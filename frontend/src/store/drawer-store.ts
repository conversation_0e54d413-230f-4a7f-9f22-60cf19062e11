import { create, type StateCreator } from 'zustand'

interface DrawerState {
  isOpen: boolean
  open: () => void
  close: () => void
  onOpenChange: (open: boolean) => void
}

type DrawerKey = 'points' | 'wallet'

type DrawerStates = Record<DrawerKey, DrawerState>

function createDrawerHandlers(
  key: Drawer<PERSON>ey,
  set: Parameters<StateCreator<DrawerStates>>[0]
): DrawerState {
  return {
    isOpen: false,
    open: () =>
      set((state) => ({
        [key]: { ...state[key], isOpen: true }
      })),
    close: () =>
      set((state) => ({
        [key]: { ...state[key], isOpen: false }
      })),
    onOpenChange: (open: boolean) =>
      set((state) => ({
        [key]: { ...state[key], isOpen: open }
      }))
  }
}

export const useDrawerStore = create<DrawerStates>((set) => ({
  points: createDrawerHandlers('points', set),
  wallet: createDrawerHandlers('wallet', set)
}))
