<svg width="971" height="173" viewBox="20 20 930 160" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.4">
<path d="M21 27C262.856 185.851 683.849 201.585 949.662 27" stroke="url(#paint0_linear_340_7788)" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter0_f_340_7788)">
<path d="M21 27C262.856 185.851 683.849 201.585 949.662 27" stroke="url(#paint1_linear_340_7788)" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<g opacity="0.2">
<path d="M32 27.3691C268.127 177.664 679.146 192.551 938.662 27.3691" stroke="url(#paint2_linear_340_7788)" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter1_f_340_7788)">
<path d="M32 27.3691C268.127 177.664 679.146 192.551 938.662 27.3691" stroke="url(#paint3_linear_340_7788)" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<g opacity="0.1">
<path d="M32 21C268.127 171.295 679.146 186.182 938.662 21" stroke="url(#paint4_linear_340_7788)" stroke-linecap="round" stroke-linejoin="round"/>
<g filter="url(#filter2_f_340_7788)">
<path d="M32 21C268.127 171.295 679.146 186.182 938.662 21" stroke="url(#paint5_linear_340_7788)" stroke-linecap="round" stroke-linejoin="round"/>
</g>
</g>
<defs>
<filter id="filter0_f_340_7788" x="0.5" y="6.5" width="969.662" height="166.108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_340_7788"/>
</filter>
<filter id="filter1_f_340_7788" x="11.5" y="6.86914" width="947.662" height="159.369" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_340_7788"/>
</filter>
<filter id="filter2_f_340_7788" x="11.5" y="0.5" width="947.662" height="159.369" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_340_7788"/>
</filter>
<linearGradient id="paint0_linear_340_7788" x1="21" y1="34.8193" x2="60.2345" y2="266.781" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB300"/>
<stop offset="1" stop-color="#007DE1"/>
</linearGradient>
<linearGradient id="paint1_linear_340_7788" x1="485.331" y1="43.268" x2="485.331" y2="204.033" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB300"/>
<stop offset="1" stop-color="#007DE1"/>
</linearGradient>
<linearGradient id="paint2_linear_340_7788" x1="32" y1="34.7672" x2="68.035" y2="254.607" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB300"/>
<stop offset="1" stop-color="#007DE1"/>
</linearGradient>
<linearGradient id="paint3_linear_340_7788" x1="485.331" y1="42.7609" x2="485.331" y2="194.866" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB300"/>
<stop offset="1" stop-color="#007DE1"/>
</linearGradient>
<linearGradient id="paint4_linear_340_7788" x1="32" y1="28.3981" x2="68.035" y2="248.238" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB300"/>
<stop offset="1" stop-color="#007DE1"/>
</linearGradient>
<linearGradient id="paint5_linear_340_7788" x1="485.331" y1="36.3918" x2="485.331" y2="188.497" gradientUnits="userSpaceOnUse">
<stop stop-color="#FFB300"/>
<stop offset="1" stop-color="#007DE1"/>
</linearGradient>
</defs>
</svg>
