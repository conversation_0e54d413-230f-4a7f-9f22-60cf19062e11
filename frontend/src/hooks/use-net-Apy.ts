import { Decimal, type Obligation } from '@pebble-protocol/pebble-sdk'
import { useMemo } from 'react'
import { useGetMarketList } from '@/queries/market'

export const useNetApy = (marketName: string, obligation?: Obligation) => {
  const { data: marketList } = useGetMarketList(marketName)

  const supplyApyMap = useMemo(() => {
    return new Map(marketList?.map((v) => [v.token, Number(v.supplyAPY)]))
  }, [marketList])
  const borrowApyMap = useMemo(() => {
    return new Map(marketList?.map((v) => [v.token, Number(v.borrowAPY)]))
  }, [marketList])

  const supplyValue = useMemo(() => {
    if (!obligation) return null
    return obligation
      .depositAssets()
      .map((v) => {
        const deposit = obligation.getDeposit(v)
        const supplyApy = supplyApyMap.get(v)
        return {
          usdValue: deposit.usdValue,
          usdApy: deposit.usdValue.mul(
            supplyApy ? Decimal.fromNumber(supplyApy) : Decimal.zero()
          )
        }
      })
      .reduce(
        (acc, curr) => ({
          usdValue: acc.usdValue.add(curr.usdValue),
          usdApy: acc.usdApy.add(curr.usdApy)
        }),
        { usdValue: Decimal.zero(), usdApy: Decimal.zero() }
      )
  }, [obligation, supplyApyMap])

  const borrowValue = useMemo(() => {
    if (!obligation) return null
    return obligation
      .borrowedAssets()
      .map((v) => {
        const borrow = obligation.getBorrow(v)
        const borrowApy = borrowApyMap.get(v)
        return {
          usdValue: borrow.usdValue,
          usdApy: borrow.usdValue.mul(
            borrowApy ? Decimal.fromNumber(borrowApy) : Decimal.zero()
          )
        }
      })
      .reduce(
        (acc, curr) => ({
          usdValue: acc.usdValue.add(curr.usdValue),
          usdApy: acc.usdApy.add(curr.usdApy)
        }),
        { usdValue: Decimal.zero(), usdApy: Decimal.zero() }
      )
  }, [obligation, borrowApyMap])

  const netApy = useMemo(() => {
    if (supplyValue === null || borrowValue === null) return null
    return supplyValue.usdValue.equals(Decimal.zero())
      ? 0
      : supplyValue.usdApy
          .sub(borrowValue.usdApy)
          .divDecimal(supplyValue.usdValue)
          .asNumber()
  }, [supplyValue, borrowValue])

  return {
    netApy
  }
}
