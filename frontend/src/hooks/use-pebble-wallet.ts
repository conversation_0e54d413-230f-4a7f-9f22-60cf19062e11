import { useMemo } from 'react'
import {
  useAutoConnectWallet,
  useConnectWallet,
  useCurrentAccount,
  useCurrentWallet,
  useDisconnectWallet,
  useWallets
} from '@mysten/dapp-kit'
import { WalletEnum } from '@/config/wallet'

export function usePebbleWallet() {
  const currentAccount = useCurrentAccount()
  const { currentWallet, connectionStatus } = useCurrentWallet()

  const { mutateAsync: connect } = useConnectWallet()
  const { mutateAsync: disconnect } = useDisconnectWallet()

  const autoConnectionStatus = useAutoConnectWallet()

  const wallets = useWallets()
  const okxWallet = wallets.find((wallet) => wallet.name === WalletEnum.OKX)
  const slushWallet = wallets.find((wallet) => wallet.name === WalletEnum.Slush)
  const phantomWallet = wallets.find(
    (wallet) => wallet.name === WalletEnum.Phantom
  )

  const userAddress = useMemo(
    () => currentAccount?.address ?? '',
    [currentAccount]
  )

  const isLoading = useMemo(
    () => autoConnectionStatus === 'idle' || connectionStatus === 'connecting',
    [autoConnectionStatus, connectionStatus]
  )

  const isConnected = useMemo(
    () => connectionStatus === 'connected' && !!currentAccount?.address,
    [connectionStatus, currentAccount]
  )

  return {
    wallets,
    connect,
    disconnect,
    isLoading,
    isConnected,
    userAddress,
    currentWallet,
    supportedWallets: {
      [WalletEnum.OKX]: okxWallet,
      [WalletEnum.Slush]: slushWallet,
      [WalletEnum.Phantom]: phantomWallet
    }
  }
}
