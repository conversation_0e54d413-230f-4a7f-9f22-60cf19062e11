import { useState, useCallback, useMemo } from 'react'
import { useInfiniteQuery, useQueryClient } from '@tanstack/react-query'
import { type IPagination, type IResponse } from '@/types/data'

const DEFAULT_PAGE_SIZE = 20
const INITIAL_PAGE = 1

export function useInfinitePebbleQuery<
  TData,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  TParams extends Record<string, any>
>(options: {
  queryKey: unknown[]
  queryFn: (
    params: TParams & { page: number; size: number }
  ) => Promise<IResponse<IPagination<TData>>>
  initialQuery?: TParams
  pageSize?: number
  initialPage?: number
  enabled?: boolean
}) {
  const {
    initialQuery,
    queryKey,
    queryFn,
    pageSize = DEFAULT_PAGE_SIZE,
    initialPage = INITIAL_PAGE,
    enabled = true
  } = options

  const queryClient = useQueryClient()
  const [query, setQuery] = useState<TParams | undefined>(initialQuery)

  const queryKeyWithParams = useMemo(
    () => (query ? [...queryKey, query] : queryKey),
    [queryKey, query]
  )

  const queryResult = useInfiniteQuery({
    queryKey: queryKeyWithParams,
    queryFn: async ({ pageParam = initialPage }) => {
      const queryWithPage: TParams & { page: number; size: number } = {
        ...(query as TParams),
        page: Number(pageParam),
        size: pageSize
      }
      const response = await queryFn(queryWithPage)
      return response.data
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage?.pageable) return undefined
      const currentPage = lastPage.pageable.pageNumber ?? 0
      return lastPage.last ? undefined : currentPage + 1
    },
    initialPageParam: initialPage,
    enabled
  })

  const flatData = useMemo(
    () => queryResult.data?.pages.flatMap((page) => page?.content ?? []) ?? [],
    [queryResult.data?.pages]
  )

  const totalElements = useMemo(
    () => queryResult.data?.pages.at(-1)?.totalElements ?? 0,
    [queryResult.data?.pages]
  )

  const hasMore = flatData.length < totalElements

  const fetchMore = useCallback(() => {
    if (!queryResult.isFetching && hasMore) {
      return queryResult.fetchNextPage()
    }
  }, [hasMore, queryResult])

  const reset = useCallback(() => {
    queryClient.removeQueries({ queryKey: queryKeyWithParams })
    queryResult.refetch()
  }, [queryClient, queryKeyWithParams, queryResult])

  return {
    // Query state
    query,
    setQuery,

    // Computed data
    flatData,
    totalElements,
    hasMore,

    // Actions
    fetchMore,
    reset,

    // Original query result
    ...queryResult
  }
}
