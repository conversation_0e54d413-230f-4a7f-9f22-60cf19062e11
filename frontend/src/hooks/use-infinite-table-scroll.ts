import { useCallback, useEffect, useRef, useState } from 'react'

const SCROLL_THRESHOLD = 300

interface UseInfiniteTableScrollOptions {
  threshold?: number
  debounceMs?: number
  enabled?: boolean
}

interface UseInfiniteTableScrollReturn {
  handleScroll: (event: React.UIEvent<HTMLDivElement>) => void
  canScrollX: boolean
  isScrolledY: boolean
  isAtLeftEdge: boolean
  isAtRightEdge: boolean
  tableContainerRef: React.RefObject<HTMLDivElement | null>
  isNearBottom: boolean
  scrollProgress: number
}

export const useInfiniteTableScroll = (
  fetchMore: () => void,
  options: UseInfiniteTableScrollOptions = {}
): UseInfiniteTableScrollReturn => {
  const {
    threshold = SCROLL_THRESHOLD,
    debounceMs = 100,
    enabled = true
  } = options

  const tableContainerRef = useRef<HTMLDivElement>(null)
  const [isScrolledY, setIsScrolledY] = useState(false)
  const [canScrollX, setCanScrollX] = useState(false)
  const [isAtLeftEdge, setIsAtLeftEdge] = useState(false)
  const [isAtRightEdge, setIsAtRightEdge] = useState(false)
  const [isNearBottom, setIsNearBottom] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)

  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const debouncedFetchMore = useCallback(() => {
    if (timeoutRef.current) clearTimeout(timeoutRef.current)

    timeoutRef.current = setTimeout(() => {
      if (enabled) {
        fetchMore()
      }
    }, debounceMs)
  }, [fetchMore, debounceMs, enabled])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const handleFetchMoreOnEnd = useCallback(
    (element: HTMLDivElement) => {
      if (!enabled) return

      const { scrollHeight, scrollTop, clientHeight } = element
      if (scrollHeight < threshold || !scrollTop) return

      const distanceFromBottom = scrollHeight - scrollTop - clientHeight
      const newProgress = Math.max(
        0,
        Math.min(1, 1 - distanceFromBottom / scrollHeight)
      )

      if (scrollProgress !== newProgress) {
        setScrollProgress(newProgress)
      }

      const nearBottom = distanceFromBottom < threshold
      if (isNearBottom !== nearBottom) {
        setIsNearBottom(nearBottom)
      }

      if (nearBottom) {
        debouncedFetchMore()
      }
    },
    [enabled, threshold, debouncedFetchMore, scrollProgress, isNearBottom]
  )

  const handleScroll = useCallback(
    (event: React.UIEvent<HTMLDivElement>) => {
      const element = event.currentTarget
      const currentScrollTop = element.scrollTop
      const currentScrollLeft = element.scrollLeft
      const scrollWidth = element.scrollWidth
      const clientWidth = element.clientWidth

      setIsScrolledY(currentScrollTop > 0)
      handleFetchMoreOnEnd(element)

      if (canScrollX) {
        setIsAtLeftEdge(currentScrollLeft <= 1)
        setIsAtRightEdge(currentScrollLeft >= scrollWidth - clientWidth - 1)
      }
    },
    [canScrollX, handleFetchMoreOnEnd]
  )

  const updateScrollXStatus = useCallback(() => {
    const el = tableContainerRef.current
    if (el) {
      setCanScrollX(el.scrollWidth > el.clientWidth)
    }
  }, [])

  useEffect(() => {
    const element = tableContainerRef.current
    if (!element) return

    const resizeObserver = new ResizeObserver(() => {
      updateScrollXStatus()
    })

    resizeObserver.observe(element)
    return () => resizeObserver.disconnect()
  }, [updateScrollXStatus])

  useEffect(() => {
    updateScrollXStatus()
  }, [updateScrollXStatus])

  useEffect(() => {
    if (tableContainerRef.current && enabled) {
      handleFetchMoreOnEnd(tableContainerRef.current)
    }
  }, [handleFetchMoreOnEnd, enabled])

  return {
    handleScroll,
    canScrollX,
    isScrolledY,
    isAtLeftEdge,
    isAtRightEdge,
    tableContainerRef,
    isNearBottom,
    scrollProgress
  }
}
