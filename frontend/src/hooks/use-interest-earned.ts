import { Decimal, Obligation } from '@pebble-protocol/pebble-sdk'
import { useMarketDataMap } from './use-market-data-map'
import { useMemo } from 'react'

export const useInterestEarned = (
  marketName: string,
  obligation?: Obligation
) => {
  const { exchangeRates, borrowIndexes } = useMarketDataMap(marketName)
  const supplyValue = useMemo(() => {
    if (!obligation) return null

    return obligation
      .depositAssets()
      .map((v) => {
        const deposit = obligation.getDeposit(v)
        const exchangeRateSnap = exchangeRates.get(v) ?? Decimal.zero()
        // console.log('price', price.asNumber(), 'amount', amount)
        // console.log(
        //   getShortName(v),
        //   ': exchangeRate',
        //   deposit.exchangeRate().asNumber(),
        //   'exchangeRateSnap',
        //   exchangeRateSnap.asNumber(),
        //   'balanceUSD',
        //   balanceUSD.asNumber(),
        //   'supplyInterest',
        //   balanceUSD
        //     .mul(deposit.exchangeRate().sub(exchangeRateSnap))
        //     .asNumber()
        // )
        return {
          usdValue: deposit.usdValue,
          interest: deposit.usdValue.mul(
            deposit.exchangeRate().sub(exchangeRateSnap)
          )
        }
      })
      .reduce(
        (acc, curr) => ({
          usdValue: acc.usdValue.add(curr.usdValue),
          interest: acc.interest.add(curr.interest)
        }),
        { usdValue: Decimal.zero(), interest: Decimal.zero() }
      )
  }, [obligation, exchangeRates])

  const borrowInterest = useMemo(() => {
    if (!obligation) return null

    return obligation
      .borrowedAssets()
      .map((v) => {
        const borrow = obligation.getBorrow(v)
        const borrowIndexSnap = borrowIndexes.get(v) ?? Decimal.zero()
        // console.log(
        //   getShortName(v),
        //   ': borrowIndex',
        //   borrow.borrowIndex().asNumber(),
        //   'borrowIndexSnap',
        //   borrowIndexSnap.asNumber(),
        //   'borrowedUSD',
        //   usdValue,
        //   'borrowinterest',
        //   !borrowIndexSnap.equals(Decimal.zero())
        //     ? usdValue.mul(
        //       borrow
        //         .borrowIndex()
        //         .divDecimal(borrowIndexSnap)
        //         .sub(Decimal.fromNumber(1))
        //     )
        //     : Decimal.zero()
        // )
        return !borrowIndexSnap.equals(Decimal.zero())
          ? borrow.usdValue.mul(
              borrow
                .borrowIndex()
                .divDecimal(borrowIndexSnap)
                .sub(Decimal.fromNumber(1))
            )
          : Decimal.zero()
      })
      .reduce((acc, curr) => acc.add(curr), Decimal.zero())
  }, [obligation, borrowIndexes])

  const interestEarned = useMemo(() => {
    if (!supplyValue || !borrowInterest) return null
    // console.log(
    //   'supplyInterestSum:',
    //   supplyValue.interest.asNumber(),
    //   'borrowInterestSum:',
    //   borrowInterest.asNumber(),
    //   'InterestEarned:',
    //   supplyValue.interest.sub(borrowInterest).asNumber()
    // )
    return supplyValue.interest.sub(borrowInterest).asNumber()
  }, [supplyValue, borrowInterest])

  return {
    interestEarned
  }
}
