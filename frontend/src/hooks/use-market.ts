import { useQuery } from '@tanstack/react-query'
import { useCallback, useMemo } from 'react'
import { usePebbleSDK } from './use-pebble-sdk'
import { getMarket, Market } from '@pebble-protocol/pebble-sdk'
import { useGetWeb3Config } from '@/queries/config'
import { useSuiClient } from '@mysten/dapp-kit'
import { NETWORK } from '@/config/networks'

export const useMarket = () => {
  const client = useSuiClient()
  const { pebbleClient } = usePebbleSDK()
  const enabled = useMemo(() => !!pebbleClient, [pebbleClient])
  const { suiNetworkConfig: networks } = useGetWeb3Config()
  const markets = useMemo(() => {
    return networks?.contract?.marketType
  }, [networks])
  const queryFn = useCallback(async () => {
    if (!markets) {
      return []
    }
    const marketsData = await Promise.all(
      markets.map(async (marketName) => {
        const marketInfo = getMarket(NETWORK, marketName.toString())
        const marketData = await pebbleClient.getMarketDetailByName(marketName)
        const market = await Market.new(
          marketInfo.type,
          marketInfo.objectId,
          marketData,
          client
        )
        return {
          marketName,
          market: market
        }
      })
    )
    return marketsData
  }, [markets, pebbleClient, client])
  return useQuery({
    queryKey: [
      'fetchMarkets',
      markets?.sort((a, b) => a.localeCompare(b)).join(',')
    ],
    queryFn,
    enabled,
    refetchOnWindowFocus: false,
    refetchOnMount: false
  })
}

export const useMarketInfo = (marketName: string | undefined) => {
  const { data: marketsData } = useMarket()
  return useMemo(() => {
    return marketsData?.find((v) => v.marketName === marketName)
  }, [marketsData, marketName])
}
