import { useGetMarketList } from '@/queries/market'
import { useGetObligationList } from '@/queries/user'
import { Decimal } from '@pebble-protocol/pebble-sdk'
import { useMemo } from 'react'

export const useMarketDataMap = (marketName: string) => {
  const { data: marketList } = useGetMarketList(marketName)
  const { data: obligationList } = useGetObligationList()
  // const exchangeRates = useMemo(() => {
  //   return new Map(
  //     marketList?.map((v) => [
  //       v.token,
  //       Decimal.fromString(v.previousData?.previousExchangeRate ?? '0')
  //     ])
  //   )
  // }, [marketList])
  // const borrowIndexes = useMemo(() => {
  //   return new Map(
  //     marketList?.map((v) => [
  //       v.token,
  //       Decimal.fromString(v.previousData?.previousBorrowIndex ?? '0')
  //     ])
  //   )
  // }, [marketList])
  const exchangeRates = useMemo(() => {
    return new Map(
      obligationList?.data?.map((v) => [
        v.token,
        Decimal.fromString(v.exchangeRate ?? '0')
      ])
    )
  }, [obligationList])
  const borrowIndexes = useMemo(() => {
    return new Map(
      obligationList?.data?.map((v) => [
        v.token,
        Decimal.fromString(v.borrowIndex ?? '0')
      ])
    )
  }, [obligationList])
  const marketDecimals = useMemo(() => {
    return new Map(
      marketList?.map((v) => [v.token, Number(v.tokenInfo.decimals)])
    )
  }, [marketList])

  const previousTimestamp = useMemo(() => {
    if (!marketList || marketList.length === 0) return 0
    return Math.max(
      ...marketList.map((v) => v.previousData?.previousTimestamp ?? 0)
    )
  }, [marketList])

  return { exchangeRates, borrowIndexes, marketDecimals, previousTimestamp }
}
