import { nanoid } from 'nanoid'
import { toast } from '@/components/ui/sonner'
import { useMutation, type UseMutationOptions } from '@tanstack/react-query'
import type { SuiTransactionBlockResponse } from '@mysten/sui/client'
import type { ContractError } from '@/types/error'

export function useContractMutation<TVariables = void>({
  mutationFn,
  ...restOptions
}: {
  mutationFn: (variables: TVariables) => Promise<SuiTransactionBlockResponse>
} & Omit<
  UseMutationOptions<
    SuiTransactionBlockResponse,
    ContractError,
    TVariables,
    { toastId: string }
  >,
  'mutationFn'
>) {
  return useMutation<
    SuiTransactionBlockResponse,
    ContractError,
    TVariables,
    { toastId: string }
  >({
    mutationFn: mutationFn,
    onMutate: () => {
      const toastId = nanoid()
      toast.loading({ id: toastId, message: 'Pending...' })
      return { toastId }
    },
    onSuccess: (data, _variables, context) => {
      toast.success({ id: context?.toastId, hash: data.digest })
    },
    onError: (err, _variables, context) => {
      toast.error({
        id: context?.toastId,
        message: err.message,
        hash: err.hash
      })
    },
    ...restOptions
  })
}
