import { useCallback, useMemo } from 'react'
import { usePebbleSDK } from './use-pebble-sdk'
import { usePebbleWallet } from './use-pebble-wallet'
import { ContractError } from '@/types/error'
import { useQuery } from '@tanstack/react-query'
import { getShortName } from '@/utils/data'
import { Obligation } from '@pebble-protocol/pebble-sdk'

export const useObligationInfo = () => {
  const { userAddress } = usePebbleWallet()
  const { pebbleClient } = usePebbleSDK()
  const enabled = useMemo(
    () => !!pebbleClient && !!userAddress,
    [pebbleClient, userAddress]
  )

  const queryFn = useCallback(async () => {
    const obligations = []
    try {
      const obligationCaps =
        await pebbleClient.getObligationsFromWallet(userAddress)
      // console.log('obligationCaps', obligationCaps)

      if (obligationCaps.length > 0) {
        for (let i = 0; i < obligationCaps.length; i++) {
          const cap = obligationCaps[i]

          try {
            // Get obligation info
            const { obligationId, marketType, marketId } =
              await pebbleClient.getObligationOwnerCapDetail(cap)
            console.log('obligationId', obligationId)
            const marketName = getShortName(marketType)
            const details = await pebbleClient.getObligationDetail(
              marketType,
              marketId,
              obligationId
            )
            const obligation = new Obligation(details)
            obligations.push({
              marketId: marketId,
              marketType: marketType,
              marketName: marketName,
              obligationOwnerCapId: cap,
              obligationId: obligationId,
              obligation: obligation
            })
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : String(error)
            throw new ContractError(
              `Could not process obligation ${i + 1}: ${errorMessage}`
            )
          }
        }
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error)
      throw new ContractError(
        `Could not retrieve user obligations: ${errorMessage}`
      )
    }
    return obligations
  }, [pebbleClient, userAddress])

  return useQuery({
    queryKey: ['fetchObligationInfo', userAddress.toLowerCase()],
    queryFn,
    enabled,
    refetchOnWindowFocus: false,
    refetchOnMount: false
  })
}
