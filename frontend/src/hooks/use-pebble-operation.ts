import { usePebbleSDK } from '@/hooks/use-pebble-sdk'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { useSuiClient } from '@mysten/dapp-kit'
import { useContractMutation } from '@/hooks/use-contract-mutation'
import { ContractError } from '@/types/error'
import { useObligationInfo } from './use-obligation-info'
import {
  getMarket,
  OperationName,
  prepareCoinsForAmount
} from '@pebble-protocol/pebble-sdk'
import { Transaction } from '@mysten/sui/transactions'
import { parseUnits } from '@/lib/units'
import { useAllBalance } from './use-all-balance'
import { getShortName } from '@/utils/data'
import { NETWORK } from '@/config/networks'
import { useGetObligationSupply } from '@/queries/user'

export enum OperationMode {
  Transaction = 'transaction',
  Wallet = 'wallet'
}

export enum MarketName {
  MainMarket = 'MainMarket'
}

export interface OperationParams {
  amount: string
  coinType: string
  decimals: number
}

type PopulateTransactionFn = (
  tx: Transaction,
  marketId: string,
  marketType: string,
  obligationOwnerCapId: string,
  coinType: string,
  coinObjectId:
    | string
    | ({
        $kind: 'Result'
        Result: number
      } & [
        {
          $kind: 'NestedResult'
          NestedResult: [number, number]
        }
      ])
) => void

type WalletOperationFn = (
  marketName: string,
  coinType: string,
  amountBigInt: bigint,
  walletAdapter: unknown
) => Promise<string>

interface TransactionOperationConfig {
  mode: OperationMode.Transaction
  populateTransactionFn: PopulateTransactionFn
}

interface WalletOperationConfig {
  mode: OperationMode.Wallet
  walletOperationFn: WalletOperationFn
}

type OperationConfig = TransactionOperationConfig | WalletOperationConfig

export const usePebbleOperation = (
  operationType: OperationName,
  marketName: string,
  config: OperationConfig
) => {
  const client = useSuiClient()
  const { userAddress } = usePebbleWallet()
  const { pebbleClient, walletAdapter } = usePebbleSDK()
  const {
    data: obligationInfo,
    isFetching: isFetchingObligationInfo,
    refetch: refetchObligationInfo
  } = useObligationInfo()
  const { refetch: refetchObligationSupply } = useGetObligationSupply()
  const { refetch: refetchAllBalance } = useAllBalance()

  const mutationFn = async (params: OperationParams) => {
    if (!obligationInfo) {
      throw new ContractError('❌ Failed to fetch obligationInfo')
    }
    const { amount, coinType } = params
    console.log('OperationParams', operationType, amount, coinType)

    // Get the MainMarket from configuration
    const market = getMarket(NETWORK, marketName.toString())
    const marketId = market.objectId
    const marketType = market.type

    let obligationOwnerCapId = obligationInfo.find(
      (v) => v.marketName === marketName.toString()
    )?.obligationOwnerCapId
    if (!obligationOwnerCapId) {
      console.log('\n=== Creating Obligation ===')
      const enterMarketTx = await pebbleClient.enterMarket(
        marketId,
        marketType,
        walletAdapter
      )
      console.log('enterMarketTx', enterMarketTx)
      if (!enterMarketTx) {
        throw new ContractError('Failed to create obligation')
      }
      const obligationOwnerCaps =
        await pebbleClient.getObligationsFromWallet(userAddress)
      for (let i = 0; i < obligationOwnerCaps.length; i++) {
        const cap = obligationOwnerCaps[i]
        const { marketType } =
          await pebbleClient.getObligationOwnerCapDetail(cap)
        if (getShortName(marketType) === marketName.toString()) {
          obligationOwnerCapId = cap
          break
        }
      }
      if (!obligationOwnerCapId) {
        throw new ContractError('Failed to find obligation')
      }
    }
    console.log('obligationOwnerCapId', obligationOwnerCapId)

    let confirmed
    const amountBigInt = parseUnits(String(amount), params.decimals)
    try {
      if (config.mode === OperationMode.Transaction) {
        // Transaction mode: build and execute transaction
        const tx = new Transaction()
        // if (isSuiCoinType(coinType)) {
        //   // Build a dummy transaction to estimate gas
        //   const dummyTx = new Transaction()
        //   const dummyCoin = dummyTx.splitCoins(dummyTx.gas, [amountBigInt])
        //   dummyTx.setSender(userAddress)
        //   config.populateTransactionFn(
        //     dummyTx,
        //     marketId,
        //     marketType,
        //     obligationOwnerCapId,
        //     coinType,
        //     dummyCoin
        //   )
        //   // Estimate gas and set budget
        //   const estimatedGas = await estimateGasForTransaction(
        //     dummyTx,
        //     pebbleClient.provider
        //   )
        //   tx.setGasBudget(amountBigInt + estimatedGas)
        //   console.log(
        //     `Gas budget set to: ${amountBigInt + estimatedGas} (supply/repay: ${amountBigInt}, gas: ${estimatedGas})`
        //   )
        // }
        const coinObjectId = await prepareCoinsForAmount(
          tx,
          pebbleClient.provider,
          userAddress,
          coinType,
          amountBigInt
        )
        // Use the provided populate function
        config.populateTransactionFn(
          tx,
          marketId,
          marketType,
          obligationOwnerCapId,
          coinType,
          coinObjectId as string
        )
        confirmed = await walletAdapter.signAndExecuteTransaction(tx)
      } else {
        // Wallet mode: use wallet operation function
        // Execute the wallet operation
        const result = await config.walletOperationFn(
          marketName.toString(),
          coinType,
          amountBigInt,
          walletAdapter
        )
        // Check transaction confirmation
        confirmed = await client.waitForTransaction({
          digest: result
        })
      }
    } catch (error) {
      console.error(error)
      console.log(
        'coinType',
        coinType,
        'amountBigInt',
        amountBigInt,
        'decimals',
        params.decimals
      )
      refetchObligationInfo()
      throw new ContractError('Failed to execute operation')
    }

    if (confirmed.effects?.status.status === 'failure') {
      throw new ContractError('', confirmed.digest)
    }

    refetchObligationInfo()
    refetchAllBalance()
    refetchObligationSupply()
    return confirmed
  }

  return {
    isFetching: isFetchingObligationInfo,
    operation: useContractMutation({
      mutationKey: [operationType, userAddress],
      mutationFn: mutationFn
    })
  }
}
