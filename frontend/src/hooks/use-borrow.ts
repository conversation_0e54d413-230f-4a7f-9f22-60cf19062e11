import { Decimal, OperationName } from '@pebble-protocol/pebble-sdk'
import { OperationMode, usePebbleOperation } from './use-pebble-operation'
import { usePebbleSDK } from './use-pebble-sdk'
import { useMarketInfo } from './use-market'
import { useMemo } from 'react'
import { formatUnits } from '@/lib/units'
import { useGetAllMarketConfig } from '@/queries/config'
import type { IMarketInfo } from '@/types/market'
import BigNumber from 'bignumber.js'
import { useMarketObligation } from '@/pages/market/hooks/use-market-obligation'
import { useMarketTokenApy } from '@/pages/market/hooks/use-market-apy'

export const useBorrow = (
  marketName: string,
  coinType: string,
  marketInfo?: IMarketInfo | null
) => {
  const { pebbleClient } = usePebbleSDK()

  const { isFetching, operation } = usePebbleOperation(
    OperationName.Borrow,
    marketName,
    {
      mode: OperationMode.Wallet,
      walletOperationFn: (
        marketName,
        coinType,
        amountBigInt,
        walletAdapter
      ) => {
        return pebbleClient.borrowFromWallet(
          marketName,
          coinType,
          amountBigInt,
          walletAdapter
        )
      }
    }
  )

  const { data: marketConfig } = useGetAllMarketConfig()
  const maxLtv = useMemo(
    () => marketConfig?.find((v) => v.name === marketName)?.maxLTV,
    [marketConfig, marketName]
  )
  const market = useMarketInfo(marketName)
  const { obligation } = useMarketObligation(marketName)

  const maxBorrowAmount = useMemo(() => {
    if (!marketInfo || !maxLtv || !obligation) return 0
    if (!market) return 0
    return Number(
      (
        Number(
          formatUnits(
            obligation
              .maxBorrow(
                Decimal.fromNumber(maxLtv),
                market.market,
                marketInfo.token,
                Number(marketInfo.tokenInfo.decimals)
              )
              .asBigInt(),
            Number(marketInfo.tokenInfo.decimals)
          )
        ) * 0.99
      ).toFixed(6)
    )
  }, [marketInfo, market, obligation, maxLtv])

  const liqAvailableUSD = useMemo(() => {
    if (!marketInfo || !maxLtv) return 0
    const liqAvailable = formatUnits(
      marketInfo.liqAvailable,
      Number(marketInfo.tokenInfo.decimals)
    )
    const price = Number(marketInfo.tokenInfo.price)
    const liqAvailableUSD = BigNumber(liqAvailable)
      .multipliedBy(price)
      .toString()
    return liqAvailableUSD
  }, [marketInfo, maxLtv])

  const utilization = useMemo(() => {
    if (!marketInfo) return 0
    return Number(Number(marketInfo?.utilization).toFixed(4))
  }, [marketInfo])

  const liqLtv = useMemo(() => {
    if (!marketInfo) return 0
    return Number(Number(marketInfo?.liqLTV).toFixed(4))
  }, [marketInfo])

  const { borrowApy } = useMarketTokenApy(marketName, coinType, utilization)

  return {
    isFetching,
    borrow: operation,
    maxBorrowAmount,
    liqAvailableUSD,
    liqLtv,
    maxLtv,
    borrowApy
  }
}
