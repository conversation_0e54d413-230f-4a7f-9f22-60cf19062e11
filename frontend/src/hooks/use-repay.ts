import { OperationName } from '@pebble-protocol/pebble-sdk'
import { OperationMode, usePebbleOperation } from './use-pebble-operation'
import { usePebbleSDK } from './use-pebble-sdk'

export const useRepay = (marketName: string) => {
  const { pebbleClient } = usePebbleSDK()

  const { isFetching, operation } = usePebbleOperation(
    OperationName.Repay,
    marketName,
    {
      mode: OperationMode.Transaction,
      populateTransactionFn: (
        tx,
        marketId,
        marketType,
        obligationOwnerCapId,
        coinType,
        coinObjectId
      ) => {
        pebbleClient.populateRepayTxn(
          tx,
          marketId,
          marketType,
          obligationOwnerCapId,
          coinType,
          coinObjectId
        )
      }
    }
  )

  return {
    isFetching,
    repay: operation
  }
}
