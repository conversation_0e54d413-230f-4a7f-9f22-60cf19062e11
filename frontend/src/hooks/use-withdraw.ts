import { Decimal, getMarket, OperationName } from '@pebble-protocol/pebble-sdk'
import { type OperationParams } from './use-pebble-operation'
import { usePebbleSDK } from './use-pebble-sdk'
import { useMarketInfo } from './use-market'
import { useMarketObligation } from '@/pages/market/hooks/use-market-obligation'
import { useMemo } from 'react'
import type { IMarketInfo } from '@/types/market'
import { useGetAllMarketConfig } from '@/queries/config'
import { formatUnits, parseUnits } from '@/lib/units'
import { ContractError } from '@/types/error'
import { useSuiClient } from '@mysten/dapp-kit'
import { usePebbleWallet } from './use-pebble-wallet'
import { useObligationInfo } from './use-obligation-info'
import { useAllBalance } from './use-all-balance'
import { NETWORK } from '@/config/networks'
import { useContractMutation } from './use-contract-mutation'

export const useWithdraw = (
  marketName: string,
  marketInfo?: IMarketInfo | null
) => {
  const client = useSuiClient()
  const { userAddress } = usePebbleWallet()
  const { pebbleClient, walletAdapter } = usePebbleSDK()
  const {
    data: obligationInfo,
    isFetching: isFetchingObligationInfo,
    refetch: refetchObligationInfo
  } = useObligationInfo()
  const { refetch: refetchAllBalance } = useAllBalance()

  const mutationFn = async (params: OperationParams) => {
    if (!obligationInfo) {
      throw new ContractError('❌ Failed to fetch obligationInfo')
    }
    const { amount, coinType } = params
    console.log('useWithdraw', amount, coinType)
    // Get the MainMarket from configuration
    const market = getMarket(NETWORK, marketName.toString())
    const marketId = market.objectId
    const marketType = market.type

    const obligationData = obligationInfo.find(
      (v) => v.marketName === marketName.toString()
    )
    if (!obligationData) {
      throw new ContractError('Failed to find obligation')
    }
    console.log('obligationOwnerCapId', obligationData.obligationOwnerCapId)

    let confirmed
    const amountBigInt = parseUnits(String(amount), params.decimals)
    const deposit = obligationData.obligation.getDeposit(
      coinType.startsWith('0x') ? coinType.substring(2) : coinType
    )
    let ctokenAmount
    if (amountBigInt >= deposit.amount()) {
      ctokenAmount = deposit.ctokenAmount()
    } else {
      ctokenAmount = (deposit.ctokenAmount() * amountBigInt) / deposit.amount()
    }
    console.log('ctokenAmount', ctokenAmount)
    try {
      const result = await pebbleClient.withdraw(
        marketId,
        marketType,
        obligationData.obligationOwnerCapId,
        coinType,
        amountBigInt,
        walletAdapter
      )
      // Check transaction confirmation
      confirmed = await client.waitForTransaction({
        digest: result
      })
    } catch (error) {
      console.error(error)
      throw new ContractError('Failed to execute operation')
    }

    if (confirmed.effects?.status.status === 'failure') {
      throw new ContractError('', confirmed.digest)
    }

    refetchObligationInfo()
    refetchAllBalance()
    return confirmed
  }

  const { data: marketConfig } = useGetAllMarketConfig()
  const maxLtv = useMemo(
    () => marketConfig?.find((v) => v.name === marketName)?.maxLTV,
    [marketConfig, marketName]
  )
  const market = useMarketInfo(marketName)
  const { obligation } = useMarketObligation(marketName)

  const maxWithdrawAmount = useMemo(() => {
    if (!marketInfo || !maxLtv || !obligation) return 0
    if (!market) return 0
    return Number(
      formatUnits(
        obligation
          .maxWithdraw(
            market.market,
            marketInfo.token,
            Number(marketInfo.tokenInfo.decimals),
            Decimal.fromNumber(maxLtv)
          )
          .asBigInt(),
        Number(marketInfo.tokenInfo.decimals)
      )
    )
  }, [marketInfo, market, obligation, maxLtv])

  return {
    isFetching: isFetchingObligationInfo,
    withdraw: useContractMutation({
      mutationKey: [OperationName.Withdraw, userAddress],
      mutationFn: mutationFn
    }),
    maxWithdrawAmount
  }
}
