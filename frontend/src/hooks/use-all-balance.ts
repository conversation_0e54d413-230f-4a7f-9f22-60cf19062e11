import { useCallback, useMemo } from 'react'
import { useSuiClientQueries } from '@mysten/dapp-kit'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { SUI_COIN_TYPE, SUI_COIN_TYPE_LONG } from '@/lib/coin'

export const useAllBalance = () => {
  const { userAddress } = usePebbleWallet()

  const results = useSuiClientQueries({
    queries: [
      {
        method: 'getAllBalances',
        params: { owner: userAddress },
        options: { enabled: !!userAddress }
      }
    ]
  })

  const balanceRaw = useMemo(
    () =>
      results[0]?.data?.map((v) => ({
        coinType:
          v.coinType === SUI_COIN_TYPE ? SUI_COIN_TYPE_LONG : v.coinType,
        totalBalance: v.totalBalance
      })),
    [results]
  )

  const isLoading = results.some((r) => r.isLoading)
  const refetch = useCallback(() => {
    results[0]?.refetch()
  }, [results])

  return {
    balanceRaw,
    isLoading,
    refetch
  }
}
