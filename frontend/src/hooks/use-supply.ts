import { OperationName } from '@pebble-protocol/pebble-sdk'
import { OperationMode, usePebbleOperation } from './use-pebble-operation'
import { usePebbleSDK } from './use-pebble-sdk'
import type { IMarketInfo } from '@/types/market'
import { useMemo } from 'react'
import { useMarketTokenApy } from '@/pages/market/hooks/use-market-apy'

export const useSupply = (
  marketName: string,
  coinType: string,
  marketInfo?: IMarketInfo | null
) => {
  const { pebbleClient } = usePebbleSDK()
  const { isFetching, operation } = usePebbleOperation(
    OperationName.Deposit,
    marketName,
    {
      mode: OperationMode.Transaction,
      populateTransactionFn: (
        tx,
        marketId,
        marketType,
        obligationOwnerCapId,
        coinType,
        coinObjectId
      ) => {
        pebbleClient.populatedDepositTxn(
          tx,
          marketId,
          marketType,
          obligationOwnerCapId,
          coinType,
          coinObjectId
        )
      }
    }
  )

  const utilization = useMemo(() => {
    if (!marketInfo) return 0
    return Number(Number(marketInfo?.utilization).toFixed(4))
  }, [marketInfo])

  const { supplyApy } = useMarketTokenApy(marketName, coinType, utilization)

  return {
    isFetching,
    supply: operation,
    supplyApy
  }
}
