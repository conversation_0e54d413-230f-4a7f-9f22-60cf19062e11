import {
  Decimal,
  Obligation,
  Operation,
  OperationName
} from '@pebble-protocol/pebble-sdk'
import { useMarket } from './use-market'
import { useMemo } from 'react'
import { parseUnits } from '@/lib/units'
import { useGetAllMarketConfig } from '@/queries/config'
import { useMakertObligationInfo } from '@/pages/market/hooks/use-market-obligation'
import { useNetApy } from './use-net-Apy'

export const useTransactionDetail = (
  marketName: string,
  operationName: OperationName,
  asset: string,
  amount: string,
  decimals?: number
) => {
  const assetType = asset.substring(2)
  const obligatonInfo = useMakertObligationInfo(marketName)
  const { data: markets } = useMarket()
  const { data: marketConfig } = useGetAllMarketConfig()
  const { netApy } = useNetApy(marketName, obligatonInfo?.obligation)
  const market = useMemo(() => {
    return markets?.find((market) => market.marketName === marketName)?.market
  }, [markets, marketName])
  const maxLtv = useMemo(() => {
    return marketConfig?.find((market) => market.name === marketName)?.maxLTV
  }, [marketConfig, marketName])
  const obligation = useMemo(() => {
    if (!obligatonInfo) return null
    return obligatonInfo.obligation
  }, [obligatonInfo])
  const data = useMemo(() => {
    if (!market || !obligation || !decimals) return null
    let newObligation: Obligation | null = null
    if (amount && amount !== '' && Number(amount) > 0) {
      try {
        const operation = new Operation(
          operationName,
          assetType,
          Decimal.fromString(parseUnits(String(amount), decimals).toString())
        )
        newObligation = obligation.applyOperation(operation, market)
      } catch (error) {
        console.error('applyOperation error', error)
      }
    }
    const hasCollateral =
      operationName === OperationName.Deposit ||
      operationName === OperationName.Withdraw
    const hasDebt =
      operationName === OperationName.Borrow ||
      operationName === OperationName.Repay
    // console.log('newObligation', newObligation)
    // console.log('newObligation netvalue', newObligation?.netValue())
    return {
      netValue: {
        from: obligation.netValue().asNumber(),
        to: newObligation ? newObligation.netValue().asNumber() : 0
      },
      netApy: netApy,
      ltv: {
        from: obligation.currentLTV(market).asNumber() * 100,
        to: newObligation
          ? newObligation.currentLTV(market).asNumber() * 100
          : 0
      },
      maxLtv: maxLtv ?? 0,
      liqLtv: 1, //fixed value
      collateralValue: hasCollateral
        ? {
            from: obligation
              .depositAssets()
              .reduce(
                (acc, cur) =>
                  acc + obligation.getDeposit(cur).usdValue.asNumber(),
                0
              ),
            to: newObligation
              ? newObligation
                  .depositAssets()
                  .reduce(
                    (acc, cur) =>
                      acc + newObligation.getDeposit(cur).usdValue.asNumber(),
                    0
                  )
              : 0
          }
        : null,
      collateralAsset: hasCollateral
        ? {
            from: obligation.depositAssets().includes(assetType)
              ? obligation.getDeposit(assetType).usdValue.asNumber()
              : 0,
            to: newObligation
              ? newObligation.depositAssets().includes(assetType)
                ? newObligation.getDeposit(assetType).usdValue.asNumber()
                : 0
              : 0
          }
        : null,
      debtValue: hasDebt
        ? {
            from: obligation
              .borrowedAssets()
              .reduce(
                (acc, cur) =>
                  acc + obligation.getBorrow(cur).usdValue.asNumber(),
                0
              ),
            to: newObligation
              ? newObligation
                  .borrowedAssets()
                  .reduce(
                    (acc, cur) =>
                      acc + newObligation.getBorrow(cur).usdValue.asNumber(),
                    0
                  )
              : 0
          }
        : null,
      debtAsset: hasDebt
        ? {
            from: obligation.borrowedAssets().includes(assetType)
              ? obligation.getBorrow(assetType).usdValue.asNumber()
              : 0,
            to: newObligation
              ? newObligation.borrowedAssets().includes(assetType)
                ? newObligation.getBorrow(assetType).usdValue.asNumber()
                : 0
              : 0
          }
        : null
    }
  }, [
    obligation,
    market,
    operationName,
    assetType,
    amount,
    decimals,
    netApy,
    maxLtv
  ])
  return { data }
}
