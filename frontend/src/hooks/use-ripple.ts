import { useCallback, useRef, useEffect } from 'react'

const DEFAULT_DURATION = 600
const DEFAULT_SCALE = 1.5
const DEFAULT_COLOR = 'rgba(255, 255, 255, 0.15)'

interface RippleOptions {
  scale?: number
  duration?: number
}

const findFurthestPoint = (
  clickPointX: number,
  elementWidth: number,
  offsetX: number,
  clickPointY: number,
  elementHeight: number,
  offsetY: number
): number => {
  const x = clickPointX - offsetX > elementWidth / 2 ? 0 : elementWidth
  const y = clickPointY - offsetY > elementHeight / 2 ? 0 : elementHeight
  const z = Math.hypot(x - (clickPointX - offsetX), y - (clickPointY - offsetY))

  return z
}

const applyStyles = (
  element: HTMLElement,
  rect: DOMRect,
  radius: number,
  event: React.MouseEvent<HTMLElement>,
  color?: string
): void => {
  element.classList.add('ripple')
  element.style.borderRadius = '50%'
  element.style.pointerEvents = 'none'
  element.style.position = 'absolute'
  element.style.backgroundColor = color ?? DEFAULT_COLOR
  element.style.left = event.clientX - rect.left - radius + 'px'
  element.style.top = event.clientY - rect.top - radius + 'px'
  element.style.width = element.style.height = radius * 2 + 'px'
  element.style.zIndex = '1'
}

const applyAnimation = (
  element: HTMLElement,
  options: RippleOptions = {}
): void => {
  const { duration = DEFAULT_DURATION, scale = DEFAULT_SCALE } = options

  element.animate(
    [
      {
        transform: 'scale(0)',
        opacity: 1
      },
      {
        transform: `scale(${scale})`,
        opacity: 0
      }
    ],
    {
      duration,
      easing: 'linear'
    }
  )
}

export const useRipple = (options?: RippleOptions) => {
  const optionsRef = useRef(options)
  const timeoutsRef = useRef<Set<NodeJS.Timeout>>(new Set())

  optionsRef.current = options

  const cleanup = useCallback(() => {
    timeoutsRef.current.forEach((timeout) => clearTimeout(timeout))
    timeoutsRef.current.clear()
  }, [])

  useEffect(() => {
    return cleanup
  }, [cleanup])

  const createRipple = useCallback((event: React.MouseEvent<HTMLElement>) => {
    try {
      const element = event.currentTarget

      if (!element || !(element instanceof HTMLElement)) {
        console.warn('Ripple: Invalid element')
        return
      }

      const currentOptions = optionsRef.current

      if (!element.style.position || element.style.position !== 'relative') {
        element.style.position = 'relative'
        element.style.overflow = 'hidden'
      }

      const rect = element.getBoundingClientRect()

      if (rect.width === 0 || rect.height === 0) {
        console.warn('Ripple: Element has no dimensions')
        return
      }

      const radius = findFurthestPoint(
        event.clientX,
        element.offsetWidth,
        rect.left,
        event.clientY,
        element.offsetHeight,
        rect.top
      )

      const circle = document.createElement('span')
      applyStyles(circle, rect, radius, event, DEFAULT_COLOR)
      applyAnimation(circle, currentOptions)

      element.appendChild(circle)

      const duration = currentOptions?.duration ?? DEFAULT_DURATION
      const timeout = setTimeout(() => {
        if (circle.parentNode) {
          circle.remove()
        }
        timeoutsRef.current.delete(timeout)
      }, duration)

      timeoutsRef.current.add(timeout)
    } catch (error) {
      console.error('Ripple effect failed:', error)
    }
  }, [])

  return { createRipple, cleanup }
}
