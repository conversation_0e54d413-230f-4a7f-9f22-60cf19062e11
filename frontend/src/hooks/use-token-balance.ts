import BigNumber from 'bignumber.js'
import { useMemo } from 'react'
import { formatUnits } from '@/lib/units'
import { formatNumberWithSuperscriptZeros } from '@/lib/number'
import { useSuiClientQueries } from '@mysten/dapp-kit'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { SUI_COIN_TYPE } from '@/lib/coin'

export const MOCK_TOKEN_PRICE = 3.41

export const useTokenBalance = (coinType: string = SUI_COIN_TYPE) => {
  const { userAddress } = usePebbleWallet()

  const results = useSuiClientQueries({
    queries: [
      {
        method: 'getBalance',
        params: { owner: userAddress, coinType },
        options: { enabled: !!userAddress && !!coinType }
      },
      {
        method: 'getCoinMetadata',
        params: { coinType: coinType ?? '' },
        options: { enabled: !!userAddress && !!coinType }
      }
    ]
  })

  const balanceRaw = useMemo(() => results[0]?.data, [results])
  const metadata = useMemo(() => results[1]?.data, [results])

  const balance = useMemo(() => {
    if (!balanceRaw || !metadata) return '0'
    return formatUnits(BigInt(balanceRaw.totalBalance), metadata.decimals)
  }, [balanceRaw, metadata])

  const balanceUsd = useMemo(() => {
    return BigNumber(balance).multipliedBy(MOCK_TOKEN_PRICE).toString()
  }, [balance])

  const formattedBalance = useMemo(() => {
    return formatNumberWithSuperscriptZeros(balance ?? 0)
  }, [balance])

  const formattedBalanceUsd = useMemo(() => {
    return formatNumberWithSuperscriptZeros(balanceUsd)
  }, [balanceUsd])

  const isLoading = results.some((r) => r.isLoading)

  return {
    formattedBalance,
    formattedBalanceUsd,
    balance,
    metadata,
    isLoading
  }
}
