import { useSignAndExecuteTransaction, useSuiClient } from '@mysten/dapp-kit'
import type { Transaction } from '@mysten/sui/transactions'
import { usePebbleWallet } from '@/hooks/use-pebble-wallet'
import { PebbleClient } from '@pebble-protocol/pebble-sdk'
import { NETWORK, NetworkEnum } from '@/config/networks'

export const usePebbleSDK = () => {
  const client = useSuiClient()
  const { mutateAsync: signAndExecuteTransaction } =
    useSignAndExecuteTransaction()
  const { userAddress } = usePebbleWallet()
  const pebbleClient =
    NETWORK === NetworkEnum.Mainnet
      ? PebbleClient.mainnet()
      : PebbleClient.testnet()

  const walletAdapter = {
    toSuiAddress: () => userAddress,
    signAndExecuteTransaction: async (transaction: Transaction) => {
      const data = await signAndExecuteTransaction({ transaction: transaction })
      await client.waitForTransaction({ digest: data.digest })
      const result = await client.getTransactionBlock({
        digest: data.digest,
        options: {
          showEvents: true,
          showEffects: true,
          showObjectChanges: true,
          showBalanceChanges: true
        }
      })
      return {
        digest: result.digest,
        effects: result.effects,
        events: result.events,
        objectChanges: result.objectChanges,
        balanceChanges: result.balanceChanges
      }
    }
  }

  return { pebbleClient, walletAdapter }
}
