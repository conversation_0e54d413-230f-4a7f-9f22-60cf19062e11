@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-border-2: var(--border-2);
  --color-border-5: var(--border-5);
  --color-border-8: var(--border-8);
  --color-border-10: var(--border-10);
  --color-border-12: var(--border-12);
  --color-border-20: var(--border-20);
  --color-border-40: var(--border-40);
  --color-border-60: var(--border-60);
  --color-border-80: var(--border-80);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --color-green: var(--green);
  --color-red: var(--red);
  --color-black: var(--black);
  --color-blue: var(--blue);
}

:root {
  --radius: 0.625rem;
  --background: #171009;
  --foreground: #e3e0d7;
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: #150f08;
  --popover-foreground: oklch(0.985 0 0);
  --primary: #e5bc5b;
  --primary-foreground: oklch(0.205 0 0);
  --secondary: rgba(227, 224, 215, 0.05);
  --secondary-foreground: #e3e0d7;
  --muted: rgba(227, 224, 215, 0.02);
  --muted-foreground: oklch(0.708 0 0);
  --accent: rgba(227, 224, 215, 0.02);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: rgba(227, 224, 215);
  --border-2: rgba(227, 224, 215, 0.02);
  --border-5: rgba(227, 224, 215, 0.05);
  --border-8: rgba(227, 224, 215, 0.08);
  --border-10: rgba(227, 224, 215, 0.1);
  --border-12: rgba(227, 224, 215, 0.12);
  --border-20: rgba(227, 224, 215, 0.2);
  --border-40: rgba(227, 224, 215, 0.4);
  --border-60: rgba(227, 224, 215, 0.6);
  --border-80: rgba(227, 224, 215, 0.8);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);

  --green: #40ff9f;
  --red: #f93e41;
  --black: #150f08;
  --blue: #008bd5;

  --gradient-bg:
    radial-gradient(
      64.18% 71.9% at 50% 0%,
      rgba(25, 85, 9, 0.12) 2.8%,
      rgba(25, 67, 9, 0.09) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      104.47% 117.04% at 50% 100%,
      rgba(63, 46, 7, 0.18) 35%,
      rgba(13, 16, 25, 0.15) 72.91%,
      rgba(23, 16, 9, 0) 100%
    );
  --dialog-gradient-bg:
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.12) 2.8%,
      rgba(25, 67, 9, 0.09) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      104.47% 117.04% at 50% 100%,
      rgba(63, 46, 7, 0.18) 35%,
      rgba(13, 16, 25, 0.15) 72.91%,
      rgba(23, 16, 9, 0) 100%
    ),
    linear-gradient(0deg, #150f08 0%, #150f08 100%);
  --drawer-gradient-bg:
    radial-gradient(73.89% 83.69% at 49.93% 4.45%, #2a2414 0%, #1f1d16 100%),
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.12) 2.8%,
      rgba(25, 67, 9, 0.09) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      104.47% 117.04% at 50% 100%,
      rgba(63, 46, 7, 0.18) 35%,
      rgba(13, 16, 25, 0.15) 72.91%,
      rgba(23, 16, 9, 0) 100%
    ),
    linear-gradient(0deg, #150f08 0%, #150f08 100%);
  --deposit-gradient-bg:
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.12) 2.8%,
      rgba(25, 67, 9, 0.09) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      104.47% 117.04% at 50% 100%,
      rgba(63, 46, 7, 0.18) 35%,
      rgba(13, 16, 25, 0.15) 72.91%,
      rgba(23, 16, 9, 0) 100%
    );
  --navigation-active-linear-gradient-bg: linear-gradient(
    133deg,
    rgba(229, 188, 91, 0.1) 3.02%,
    rgba(35, 91, 136, 0.1) 100%
  );
  --vault-info-gradient-bg: radial-gradient(
    73.89% 83.69% at 49.93% 4.45%,
    #2a2414 0%,
    #1f1d16 100%
  );
  --vault-info-gradient-bg-2:
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.12) 2.8%,
      rgba(25, 67, 9, 0.09) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      104.47% 117.04% at 50% 100%,
      rgba(63, 46, 7, 0.18) 35%,
      rgba(13, 16, 25, 0.15) 72.91%,
      rgba(23, 16, 9, 0) 100%
    );
  --overview-gradient-bg:
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.04) 2.8%,
      rgba(25, 67, 9, 0.03) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      105.08% 117.73% at 50% 171.36%,
      rgba(63, 46, 7, 0.06) 35%,
      rgba(13, 16, 25, 0.05) 72.91%,
      rgba(23, 16, 9, 0) 100%
    );
  --tooltip-gradient-bg:
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.04) 2.8%,
      rgba(25, 67, 9, 0.03) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      105.08% 117.73% at 50% 171.36%,
      rgba(63, 46, 7, 0.06) 35%,
      rgba(13, 16, 25, 0.05) 72.91%,
      rgba(23, 16, 9, 0) 100%
    );
  --select-content-bg:
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.12) 2.8%,
      rgba(25, 67, 9, 0.09) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      104.47% 117.04% at 50% 100%,
      rgba(63, 46, 7, 0.18) 35%,
      rgba(13, 16, 25, 0.15) 72.91%,
      rgba(23, 16, 9, 0) 100%
    );
  --active-tab-bg: radial-gradient(
    59.43% 84.52% at 50% 103.57%,
    rgba(229, 188, 91, 0.1) 18.31%,
    rgba(23, 16, 9, 0) 100%
  );
  --disconnect-gradient-bg:
    radial-gradient(73.89% 83.69% at 49.93% 4.45%, #2a2414 0%, #1f1d16 100%),
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.12) 2.8%,
      rgba(25, 67, 9, 0.09) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      104.47% 117.04% at 50% 100%,
      rgba(63, 46, 7, 0.18) 35%,
      rgba(13, 16, 25, 0.15) 72.91%,
      rgba(23, 16, 9, 0) 100%
    ),
    linear-gradient(0deg, #150f08 0%, #150f08 100%);
  --disconnect-hover-gradient-bg:
    linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.05) 100%
    ),
    radial-gradient(73.89% 83.69% at 49.93% 4.45%, #2a2414 0%, #1f1d16 100%),
    radial-gradient(
      75.79% 84.91% at 49.87% -29.54%,
      rgba(25, 85, 9, 0.12) 2.8%,
      rgba(25, 67, 9, 0.09) 57.96%,
      rgba(23, 16, 9, 0) 100%
    ),
    radial-gradient(
      104.47% 117.04% at 50% 100%,
      rgba(63, 46, 7, 0.18) 35%,
      rgba(13, 16, 25, 0.15) 72.91%,
      rgba(23, 16, 9, 0) 100%
    ),
    linear-gradient(0deg, #150f08 0%, #150f08 100%);
  --gradient-bg-1: linear-gradient(133deg, #e5bc5b 3.02%, #235b88 100%);
  --gradient-bg-2: linear-gradient(129deg, #ffb300 2.77%, #007de1 87.33%);
  --gradient-bg-3: linear-gradient(
    90deg,
    rgba(227, 224, 215, 0.05) 0%,
    rgba(125, 123, 118, 0) 100%
  );
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Space Grotesk', system-ui, sans-serif;
  }
}

@layer utilities {
  .bg-custom-gradient {
    background: var(--gradient-bg), var(--background);
  }
  .bg-nav-active-gradient {
    background: var(--navigation-active-linear-gradient-bg);
  }
  .bg-dialog-gradient {
    background: var(--dialog-gradient-bg), var(--background) !important;
  }
  .bg-drawer-gradient {
    background: var(--drawer-gradient-bg), var(--background) !important;
  }
  .bg-deposit-gradient {
    background: var(--deposit-gradient-bg), var(--black) !important;
  }
  .bg-vault-info-gradient {
    background: var(--vault-info-gradient-bg), var(--black) !important;
  }
  .bg-vault-info-gradient-2 {
    background: var(--vault-info-gradient-bg-2), var(--black) !important;
  }
  .bg-overview-gradient {
    background: var(--overview-gradient-bg), #1d1911 !important;
  }
  .bg-tooltip-gradient {
    background: var(--tooltip-gradient-bg), #161410 !important;
  }
  .bg-select-content-bg {
    background: var(--select-content-bg), var(--background) !important;
  }
  .bg-disconnect-bg {
    background: var(--disconnect-gradient-bg), var(--background) !important;
  }
  .hover\:bg-disconnect-hover-bg:hover {
    background:
      var(--disconnect-hover-gradient-bg), var(--background) !important;
  }
  .bg-active-tab-bg {
    background: var(--active-tab-bg);
  }
  .bg-gradient-1 {
    background: var(--gradient-bg-1);
  }
  .bg-gradient-2 {
    background: var(--gradient-bg-2);
  }
  .bg-gradient-3 {
    background: var(--gradient-bg-3);
  }
  .hover\:bg-gradient-3:hover {
    background: var(--gradient-bg-3);
  }
  .shadow-select-content-shadow {
    box-shadow: 0px 0px 24px 6px rgba(23, 16, 9, 0.2);
  }
  .no-drag {
    -webkit-user-drag: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    pointer-events: auto;
  }

  /* For Chrome, Edge, Safari */
  ::-webkit-scrollbar {
    width: 8px; /* Scrollbar width */
    background: transparent; /* Scrollbar track background */
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-track:hover {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: #000000; /* Scrollbar thumb color */
    border-radius: 4px; /* Thumb border radius */
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #000000; /* Thumb hover color */
  }

  /* For Firefox */
  * {
    scrollbar-width: thin; /* Scrollbar width: auto | thin | none */
    scrollbar-color: #000000 #171009; /* thumb color  track color */
  }
}

input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
  appearance: textfield;
}

/* 全局字体粗细设置 */
* {
  font-weight: 300;
}
