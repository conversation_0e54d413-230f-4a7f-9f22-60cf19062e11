import { Transaction } from '@mysten/sui/transactions';
import { SuiClient, getFullnodeUrl } from '@mysten/sui/client';
import { getKeypair } from './utils';

// Treasury object IDs for test coins (you'll need to get these from deployment)
// These should be available in your deployment cache or object-ids file
const TREASURY_OBJECTS = {
  USDC: '0x64c2ca9fb1e58b835829d4eded57e32e38931c89583967ebbe883a76abd6825e',
  USDT: '0xe1e306bbf274155a98b8a87a52186c07c8d92b0fc43acd39619d62741b9020a9',
  BTC: '0x43011f2fd9de462a69d3a4c43bd720d2daeb3978504781cc0186bfde4b6f5e8a',
  ETH: '0x777872d76d44290cc26bdc77f0d270e951c55989996bc540d8c9d8a775770b9a',
};

// Package ID where test coins are deployed
const TEST_COIN_PACKAGE = '0x73a1c782178f77b3f10da48eac0eeab8217aac0d427fbead88e42f6caadcc15a';

async function mintTestCoins(
  coinType: 'USDC' | 'USDT' | 'BTC' | 'ETH',
  amount: bigint,
  recipient?: string
) {
  // Initialize client
  const client = new SuiClient({ url: getFullnodeUrl('testnet') });
  
  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  // Use sender as recipient if not specified
  const mintTo = recipient || sender;
  
  console.log(`Minting ${amount} ${coinType} to: ${mintTo}`);
  
  // Create transaction
  const tx = new Transaction();
  
  // Call the mint function from the test coin module
  const mintedCoin = tx.moveCall({
    target: `${TEST_COIN_PACKAGE}::${coinType.toLowerCase()}::mint`,
    arguments: [
      tx.object(TREASURY_OBJECTS[coinType]), // Treasury object
      tx.pure.u64(amount), // Amount to mint
    ],
  });
  
  // Transfer the minted coins to the recipient
  tx.transferObjects([mintedCoin], mintTo);
  
  // Sign and execute transaction
  const result = await client.signAndExecuteTransaction({
    transaction: tx,
    signer: keypair,
    options: {
      showEffects: true,
      showObjectChanges: true,
    },
  });
  
  console.log('\nTransaction result:', result.digest);
  
  // Find the created coin object
  if (result.objectChanges) {
    const createdCoin = result.objectChanges.find(
      change => change.type === 'created' && 
      change.objectType?.includes('::coin::Coin<')
    );
    
    if (createdCoin && 'objectId' in createdCoin) {
      console.log(`Minted coin object ID: ${createdCoin.objectId}`);
    }
  }
  
  return result;
}

// Example: Mint with specific decimals
async function mintWithDecimals(
  coinType: 'USDC' | 'USDT' | 'BTC' | 'ETH',
  amountInWholeUnits: number,
  recipient?: string
) {
  // Decimals for each test coin
  const DECIMALS = {
    USDC: 6, // As defined in the Move contract
    USDT: 6,
    BTC: 8,
    ETH: 8,
  };
  
  const decimals = DECIMALS[coinType];
  const amount = BigInt(amountInWholeUnits) * BigInt(10 ** decimals);
  
  return mintTestCoins(coinType, amount, recipient);
}

// Run examples
if (import.meta.url.startsWith('file:')) {
  // Example 1: Mint 100 USDC (with decimals)
  mintWithDecimals('BTC', 10000)
    .then(() => console.log('\nSuccessfully minted'))
    .catch(console.error);
}

export { mintTestCoins, mintWithDecimals };