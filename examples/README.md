# Pebble Protocol SDK Examples

This package contains examples demonstrating how to use the Pebble Protocol SDK.

## Setup

1. Install dependencies:
```bash
pnpm install
```

2. Create a `.env` file with your private key:
```bash
PRIVATE_KEY=your_private_key_here
```

## Running Examples

### Lending Examples

```bash
# Deposit tokens
pnpm example:deposit

# Borrow tokens
pnpm example:borrow

# Repay borrowed tokens
pnpm example:repay

# Withdraw deposited tokens
pnpm example:withdraw

# Enter a market
pnpm example:enter-market

# Liquidate a position
pnpm example:liquidate

# List all obligations
pnpm example:list-obligations

# Get market details
pnpm example:market-detail

# Get market asset details
pnpm example:market-asset-detail

# Get obligation details
pnpm example:obligation-detail

# Perform obligation operations
pnpm example:obligation-operation

# Get coin metadata
pnpm example:coin-metadata
```

### Leverage Examples

```bash
# Long SUI with SUI
pnpm example:long-sui

# Short SUI with SUI
pnpm example:short-sui

# Get leverage obligation details
pnpm example:leverage-obligation
```

### Other Examples

```bash
# Mint test tokens
pnpm example:mint-tokens
```

## Example Structure

Each example demonstrates a specific feature of the Pebble Protocol SDK:

- **Lending**: Basic lending operations like deposit, borrow, repay, and withdraw
- **Leverage**: Advanced leverage trading operations
- **Utils**: Helper functions like minting test tokens and getting coin metadata

## Customization

You can modify the examples by changing:
- The network (mainnet/testnet)
- The market name
- The coin types
- The amounts

Refer to individual example files for specific configuration options.