import { Transaction } from '@mysten/sui/transactions';
import { PebbleClient, getMarket } from '@pebble-protocol/pebble-sdk';
import { getKeypair, prepareCoinsForAmount, estimateGasForTransaction } from '../utils';

async function deposit(coinType: string, amount: bigint, network: string = "testnet", marketName: string = "MainMarket") {
  const client = PebbleClient.fromNetwork(network);

  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  // Get the MainMarket from configuration
  const market = getMarket(network, marketName);
  const marketId = market.objectId;
  const marketType = market.type;
 
  console.log(`Sender: ${sender}`);
    
  // Fetch obligation owner caps from user's wallet
  const obligationOwnerCaps = await client.getObligationsFromWallet(sender);

  if (obligationOwnerCaps.length === 0) {
    throw new Error('No obligation owner caps found in wallet. Please enter the market first.');
  }

  console.log(`Found ${obligationOwnerCaps.length} obligation owner cap(s):`);
  obligationOwnerCaps.forEach((cap, index) => console.log(`  ${index}: ${cap}`));
  
  // ALWAYS use the first obligation owner cap    
  const obligationOwnerCapId = obligationOwnerCaps[0];
  console.log(`Using obligation owner cap: ${obligationOwnerCapId}`);

  const tx = new Transaction();

  // For SUI deposits, estimate gas and set appropriate budget
  if (coinType === '0x2::sui::SUI') {
    // Build a dummy transaction to estimate gas
    const dummyTx = new Transaction();
    const dummyCoin = dummyTx.splitCoins(dummyTx.gas, [amount]);
    dummyTx.setSender(sender);
    client.populatedDepositTxn(dummyTx, marketId, marketType, obligationOwnerCapId, coinType, dummyCoin);
    
    // Estimate gas and set budget
    const estimatedGas = await estimateGasForTransaction(dummyTx, client.provider);
    tx.setGasBudget(amount + estimatedGas);
    console.log(`Gas budget set to: ${amount + estimatedGas} (deposit: ${amount}, gas: ${estimatedGas})`);
  }

  const coinObjectId = await prepareCoinsForAmount(tx, client.provider, sender, coinType, amount);
  client.populatedDepositTxn(tx, marketId, marketType, obligationOwnerCapId, coinType, coinObjectId);

  const result = await client.provider.signAndExecuteTransaction({
      transaction: tx,
      signer: keypair,
  });

  console.log(result);
  console.log(`\nDeposit transaction: ${result.digest}`);
}

// Run examples
if (import.meta.url.startsWith('file:')) {
  const amount = 1_000_000n;
  const coinType = `0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC`;
  deposit(coinType, amount, "mainnet").catch(console.error);
}
