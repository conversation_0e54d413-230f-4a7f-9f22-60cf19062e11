import { PebbleClient } from '@pebble-protocol/pebble-sdk';

async function getMarketDetail(network: string = "mainnet", marketName: string = "MainMarket") {
  const client = PebbleClient.fromNetwork(network);
  
  try {
    console.log('=== GET MARKET DETAIL ===');
    
    // Get market details for the main market
    console.log('\\n=== FETCHING MARKET INFORMATION ===');
    const marketData = await client.getMarketDetailByName(marketName);

    console.log(`\\n✅ Market detail fetched successfully!`);
    console.log(`Total assets in market: ${marketData.assets.length}`);

    // Display details for each asset
    console.log('\\n=== ASSET DETAILS ===');
    marketData.assets.forEach((asset, index) => {
      console.log(`\\n${index + 1}. Asset: ${getCoinSymbol(asset.coinType)}`);
      console.log(`   Full Type: ${asset.coinType}`);
      console.log(`   Utilization Rate: ${asset.utilizationRate.toString()}%`);
      
      // Deposit information
      const depositAmount = Number(asset.depositUsage.amount());
      console.log(`   Total Deposits: ${depositAmount.toFixed(6)} ${getCoinSymbol(asset.coinType)}`);
      console.log(`   Total Deposit Value: $${asset.depositUsage.usdValue.toString()}`);
      console.log(`   Total CToken Amount: ${asset.depositUsage.ctokenAmount().toString()}`);
      console.log(`   Exchange Rate: ${asset.depositUsage.exchangeRate().toString()}`);
      console.log(`   Can be Collateral: ${asset.depositUsage.canBeCollateral}`);
      
      // Borrow information  
      const borrowAmount = Number(asset.borrowUsage.amount());
      console.log(`   Total Borrows: ${borrowAmount.toFixed(6)} ${getCoinSymbol(asset.coinType)}`);
      console.log(`   Total Borrow Value: $${asset.borrowUsage.usdValue.toString()}`);
      console.log(`   Borrow Index: ${asset.borrowUsage.borrowIndex().toString()}`);
      
      // Price information
      console.log(`   Current Price: $${asset.depositUsage.price().toString()}`);
      
      // Interest model details
      console.log(`   Interest Model: ${asset.interestModel.type}`);
      console.log(`     Base Rate: ${asset.interestModel.params.baseBorrowRatePerSec.toString()}`);
      console.log(`     Mid Kink: ${asset.interestModel.params.midKink.toString()}%`);
      console.log(`     High Kink: ${asset.interestModel.params.highKink.toString()}%`);
      console.log(`     Max Borrow Rate: ${asset.interestModel.params.maxBorrowRate.toString()}`);
    });
    
    // Calculate total market metrics
    let totalDepositsUSD = 0n;
    let totalBorrowsUSD = 0n;
    
    marketData.assets.forEach(asset => {
      totalDepositsUSD += BigInt(asset.depositUsage.usdValue.toString().split('.')[0] || '0');
      totalBorrowsUSD += BigInt(asset.borrowUsage.usdValue.toString().split('.')[0] || '0');
    });
    
    console.log('\\n=== MARKET SUMMARY ===');
    console.log(`Total Deposits (USD): $${totalDepositsUSD.toString()}`);
    console.log(`Total Borrows (USD): $${totalBorrowsUSD.toString()}`);
    
    if (totalDepositsUSD > 0n) {
      const utilizationRate = (Number(totalBorrowsUSD) / Number(totalDepositsUSD) * 100).toFixed(2);
      console.log(`Overall Market Utilization: ${utilizationRate}%`);
    }
    
    return marketData;
  } catch (error) {
    console.error('❌ Error fetching market detail:', error);
    throw error;
  }
}

function getCoinSymbol(coinType: string): string {
  const parts = coinType.split('::');
  return parts[parts.length - 1];
}

// Run example
if (import.meta.url.startsWith('file:')) {
  console.log('Fetching market details...');
  console.log('ℹ️  Important Notes:');
  console.log('   - This fetches real-time market data with updated prices');
  console.log('   - All asset prices are refreshed via Pyth oracle before querying');
  console.log('   - Market utilization rates and liquidity are calculated in real-time');
  console.log('   - Exchange rates and borrow indexes reflect current market conditions\\n');
  
  getMarketDetail().catch(console.error);
}

export { getMarketDetail };
