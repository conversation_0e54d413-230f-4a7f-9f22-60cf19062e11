import { PebbleClient, TypeName, createMarketDisplay } from '@pebble-protocol/pebble-sdk';

async function getMarketAssetDetail(marketName: string, asset: TypeName, network: string = "testnet") {
  const client = PebbleClient.fromNetwork(network);

  try {
    const market = client.listMarkets().find(m => m.name === marketName);
    if (!market) {
      throw new Error(`Market '${marketName}' not found`);
    }
    
    const assetData = await client.getMarketAssetDetail(market.objectId, market.type, asset);

    // Create MarketDisplay object
    // Note: To calculate APYs, you would need to provide past asset data and timestamp
    // Example: const marketDetails = createMarketDisplay(assetData, pastAssetData, pastTimestamp);
    const marketDetails = createMarketDisplay(assetData);
    
    // Print all fields from MarketDisplay
    console.log('\n=== Market Asset Detail ===');
    console.log(`Market Type: ${market.name}`);
    console.log(`Market ID: ${market.objectId}`);
    console.log(`Token: ${marketDetails.token}`);
    console.log(`Total Supply: ${marketDetails.totalSupply}`);
    console.log(`Total Borrow: ${marketDetails.totalBorrow}`);
    console.log(`Liquidation LTV: ${marketDetails.liqLTV}`);
    console.log(`Supply APY: ${marketDetails.supplyAPY}`);
    console.log(`Borrow APY: ${marketDetails.borrowAPY}`);
    console.log(`Liquidity Available: ${marketDetails.liqAvailable}`);
    console.log(`Utilization: ${marketDetails.utilization}`);
    
    // Filled = Total Deposit / Max Deposit
    const filled = marketDetails.totalSupply / marketDetails.supplyCap;
    console.log(`Filled: ${filled}`);
    
    console.log(`Borrow Cap: ${marketDetails.borrowCap}`);
    console.log(`Supply Cap: ${marketDetails.supplyCap}`);
    
    console.log('\n=== Additional Asset Information ===');
    console.log(`Borrow Paused: ${assetData.borrowPaused}`);
    console.log(`Deposit Paused: ${assetData.depositPaused}`);
    console.log(`Withdraw Paused: ${assetData.withdrawPaused}`);
    console.log(`Min Borrow Amount: ${assetData.assetSetting.minBorrowAmount}`);
    console.log(`Flash Loan Fee Rate: ${assetData.assetSetting.flashLoanFeeRate.asNumber()}`);
    console.log(`Repay Fee Rate: ${assetData.assetSetting.repayFeeRate.asNumber()}`);
    
    if (assetData.collateralSetting) {
      console.log('\n=== Collateral Settings ===');
      console.log(`Collateral Factor: ${assetData.collateralSetting.collateralFactor.asNumber()}`);
      console.log(`Liquidation Incentive: ${assetData.collateralSetting.liquidationIncentive.asNumber()}`);
      console.log(`Liquidation Revenue Factor: ${assetData.collateralSetting.liquidationRevenueFactor.asNumber()}`);
    }
    
    console.log('\n=== Interest Model ===');
    console.log(`Type: ${assetData.interestModel.type}`);
    console.log(`Base Borrow Rate Per Sec: ${assetData.interestModel.params.baseBorrowRatePerSec.asNumber()}`);
    console.log(`Borrow Rate on Mid Kink: ${assetData.interestModel.params.borrowRateOnMidKink.asNumber()}`);
    console.log(`Mid Kink: ${assetData.interestModel.params.midKink.asNumber()}`);
    console.log(`Borrow Rate on High Kink: ${assetData.interestModel.params.borrowRateOnHighKink.asNumber()}`);
    console.log(`High Kink: ${assetData.interestModel.params.highKink.asNumber()}`);
    console.log(`Max Borrow Rate: ${assetData.interestModel.params.maxBorrowRate.asNumber()}`);

    return assetData;
  } catch (error) {
    throw error;
  }
}

// Run example
if (import.meta.url.startsWith('file:')) {
  const marketName = "MainMarket";
  const asset = "0000000000000000000000000000000000000000000000000000000000000002::sui::SUI";
  // const asset = "dba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC";
  getMarketAssetDetail(marketName, asset, "mainnet").catch(console.error);
}
