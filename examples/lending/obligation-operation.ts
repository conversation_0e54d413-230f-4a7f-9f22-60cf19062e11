import { Decimal, Market, Obligation, Operation, OperationName, PebbleClient, getMarket } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

export async function getObligationDetail(marketName: string, whichObligation: number = 0): Promise<void> {
  // Create client instance for testnet (provider is created automatically)
  const client = PebbleClient.testnet();

  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  // Get the MainMarket from configuration
  console.log('Fetching obligations from wallet...');
  console.log(`Sender: ${sender}`);

  // Fetch obligation owner caps from user's wallet
  const obligationOwnerCaps = await client.getObligationsFromWallet(sender);
  const obligationOwnerCapId = obligationOwnerCaps[whichObligation];

  const obligationId = await client.getObligationIdFromOwnerCapId(obligationOwnerCapId);

  const marketInfo = getMarket('testnet', marketName);
  const details = await client.getObligationDetail(marketInfo.type, marketInfo.objectId, obligationId);

  const marketData = await client.getMarketDetailByName(marketName);
  const market = await Market.new(marketInfo.type, marketInfo.objectId, marketData, client.provider);

  const obligation = new Obligation(details);

  console.log("net value", obligation.netValue().asNumber());
  console.log("suplus", obligation.surplus(market).asNumber());

  const ltv = Decimal.fromString("0.8");
  const price = Decimal.fromString("1.2");
  const decimals = 6;
  const assetType = "73a1c782178f77b3f10da48eac0eeab8217aac0d427fbead88e42f6caadcc15a::usdt::USDT";
  console.log("max borrow", obligation.maxBorrow(ltv, market, assetType, decimals).asNumber());

  const operation = new Operation(
    OperationName.Withdraw,
    "73a1c782178f77b3f10da48eac0eeab8217aac0d427fbead88e42f6caadcc15a::usdt::USDT",
    Decimal.fromString("100000"),
  );
  const outcome = obligation.applyOperation(operation, market);
  console.log(outcome.getDeposit("73a1c782178f77b3f10da48eac0eeab8217aac0d427fbead88e42f6caadcc15a::usdt::USDT").usdValue.asNumber());
}

// Run examples
if (import.meta.url.startsWith('file:')) {
  const market = "MainMarket";
  getObligationDetail(market).catch(console.error);
}
