import { PebbleClient } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

async function borrowAsset(coinType: string, borrowAmount: bigint, network: string = "testnet", marketName: string = "MainMarket") {
  const client = PebbleClient.fromNetwork(network);
  
  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  try {
    console.log('=== BORROW ASSET ===');
    console.log(`Sender: ${sender}`);
    console.log(`Coin Type: ${coinType}`);
    console.log(`Borrow Amount: ${borrowAmount}`);

    // Use the convenient borrowFromWallet method
    const txHash = await client.borrowFromWallet(
      marketName,
      coinType,
      borrowAmount,
      keypair
    );

    console.log(`\n✅ Borrow transaction successful!`);
    console.log(`Transaction: ${txHash}`);

    // Optional: Get updated obligation details
    console.log('\n=== UPDATED OBLIGATION STATUS ===');
    try {
      const obligations = await client.getObligationsFromWallet(sender);
      if (obligations.length > 0) {
        const obligationId = await client.getObligationIdFromOwnerCapId(obligations[0]);
        const market = client.listMarkets().find(m => m.name === 'MainMarket')!;
        const details = await client.getObligationDetail(market.type, market.objectId, obligationId);
        
        console.log(`Borrows: ${details.borrows.length}`);
        details.borrows.forEach((borrow, index) => {
          console.log(`  ${index}: ${borrow.coinType} - ${borrow.amount()} (USD: ${borrow.usdValue.toString()})`);
        });
        
        console.log(`Deposits: ${details.deposits.length}`);
        details.deposits.forEach((deposit, index) => {
          console.log(`  ${index}: ${deposit.coinType} - ${deposit.amount()} (USD: ${deposit.usdValue.toString()})`);
        });
      }
    } catch (detailError) {
      console.log('Could not fetch updated obligation details:', detailError);
    }
    
    return txHash;
  } catch (error) {
    console.error('❌ Error borrowing asset:', error);
    throw error;
  }
}

// Run examples
if (import.meta.url.startsWith('file:')) {
  // Example: Borrow 1 USDC (6 decimals, so 1000000 = 1 USDC)
  // Note: Minimum borrow amount is 1 USDC per the asset configuration
  const borrowAmount = 10_000_000n; // 1 USDC (meets minimum borrow requirement)
  const coinType = "0x73a1c782178f77b3f10da48eac0eeab8217aac0d427fbead88e42f6caadcc15a::usdc::USDC";
  
  borrowAsset(coinType, borrowAmount).catch(console.error);
}

export { borrowAsset };