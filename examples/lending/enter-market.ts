import { PebbleClient, getMarket } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

async function enterMarket(network: string = "testnet", marketName: string = "MainMarket") {
  const client = PebbleClient.fromNetwork(network);

  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  // Get the MainMarket from configuration
  const market = getMarket(network, marketName);
  const marketId = market.objectId;
  const marketType = market.type;

  try {
    console.log('Entering market...');
    console.log(`Market: ${market.name}`);
    console.log(`Market ID: ${marketId}`);
    console.log(`Market Type: ${marketType}`);
    console.log(`Sender: ${sender}`);

    const enterMarketTx = await client.enterMarket(marketId, marketType, keypair);
    console.log(`\nEnter market transaction: ${enterMarketTx}`);
    return enterMarketTx;
  } catch (error) {
    console.error('Error entering market:', error);
    throw error;
  }
}

enterMarket("mainnet").catch(console.error);
