import { getCoinMetadata, getMultipleCoinMetadata, formatCoinAmount, parseCoinAmount } from '@pebble-protocol/pebble-sdk';
import { PebbleClient } from '@pebble-protocol/pebble-sdk';

async function main() {
  const client = PebbleClient.testnet();
  
  // Example 1: Get metadata for SUI token
  console.log('Getting SUI token metadata...');
  const suiMetadata = await getCoinMetadata(client.provider, '0x2::sui::SUI');
  
  if (suiMetadata) {
    console.log('SUI Metadata:', {
      name: suiMetadata.name,
      symbol: suiMetadata.symbol,
      decimals: suiMetadata.decimals,
      description: suiMetadata.description,
    });

    // Example: Format amount
    const rawAmount = 1500000000n; // 1.5 SUI in raw units
    const formatted = formatCoinAmount(rawAmount, suiMetadata);
    console.log(`Formatted amount: ${formatted}`);

    // Example: Parse amount back
    const parsed = parseCoinAmount('1.5', suiMetadata.decimals);
    console.log(`Parsed amount: ${parsed}`);
  }

  // Example 2: Get multiple coin metadata
  console.log('\nGetting multiple coin metadata...');
  const coinTypes = [
    '0x2::sui::SUI',
    '0x73a1c782178f77b3f10da48eac0eeab8217aac0d427fbead88e42f6caadcc15a::usdc::USDC', // USDC
  ];

  const multipleMetadata = await getMultipleCoinMetadata(client.provider, coinTypes);
  
  multipleMetadata.forEach((metadata, coinType) => {
    console.log(`${coinType}:`, {
      symbol: metadata.symbol,
      decimals: metadata.decimals,
    });
  });
}

// Run the example
main().catch(console.error);