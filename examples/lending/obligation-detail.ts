import { Decimal, PebbleClient, getMarket } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

export async function getObligationDetail(marketName: string = "MainMarket", network: "mainnet" | "testnet", whichObligation: number = 0): Promise<void> {
  const client = PebbleClient.fromConfig({
    network,
    pythEndpoint: "https://hermes.pyth.network"
  });

  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  // Get the MainMarket from configuration
  console.log('Fetching obligations from wallet...');
  console.log(`Sender: ${sender}`);

  // // Fetch obligation owner caps from user's wallet
  const obligationOwnerCaps = await client.getObligationsFromWallet(sender);
  const obligationOwnerCapId = obligationOwnerCaps[whichObligation];

  const obligationId = await client.getObligationIdFromOwnerCapId(obligationOwnerCapId);
  const ownerCapDetail = await client.getObligationOwnerCapDetail(obligationOwnerCapId);
  console.log(ownerCapDetail);

  const marketInfo = getMarket(network, marketName);
  const details = await client.getObligationDetail(marketInfo.type, marketInfo.objectId, obligationId);

  console.log('\n=== FINAL RESULTS ===');
  console.log('Obligation Details:');
  console.log('  Borrows length:', details.borrows.length);
  console.log('  Deposits length:', details.deposits.length);

  if (details.borrows.length > 0) {
    console.log('\n  Borrows:');
    details.borrows.forEach((borrow, index) => {
      console.log(`    ${index}: ${borrow.coinType} - ${borrow.amount()} (USD: ${borrow.usdValue.toString()}) - apy ${borrow.apy(Decimal.one(), 1754386522)}`);
    });
  }

  if (details.deposits.length > 0) {
    console.log('\n  Deposits:');
    details.deposits.forEach((deposit, index) => {
      console.log(`    ${index}: ${deposit.coinType} - ${deposit.ctokenAmount()} ${deposit.amount()} (USD: ${deposit.usdValue.toString()}) [Collateral: ${deposit.canBeCollateral}]`);
    });
  }
}

// Run examples
if (import.meta.url.startsWith('file:')) {
  const market = "MainMarket";
  const network = "mainnet";
  getObligationDetail(market, network).catch(console.error);
}
