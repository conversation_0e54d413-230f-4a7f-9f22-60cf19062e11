import { Transaction } from '@mysten/sui/transactions';
import { PebbleClient, getMarket } from '@pebble-protocol/pebble-sdk';
import { getKeypair, prepareCoinsForAmount } from '../utils';

async function liquidate(network: string = "testnet", marketName: string = "MainMarket") {
  const client = PebbleClient.fromNetwork(network);

  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  // Get the MainMarket from configuration
  const market = getMarket(network, marketName);
  const marketId = market.objectId;
  const marketType = market.type;

  const obligationId = "0x764ce13cab4446b18080dc987434aefc7eda95bdbb935bc54f26072b3bc6370a";
  const collateralCoinType = "dba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC";
  const repayCoinType = "0xc060006111016b8a020ad5b33834984a437aaa7d3c74c18e09a95d48aceab08c::coin::COIN";
  const repayAmount = 38753030n;

  console.log(`Sender: ${sender}`);

  const tx = new Transaction();

  const coinObjectId = await prepareCoinsForAmount(tx, client.provider, sender, repayCoinType, repayAmount);
  await client.populateLiquidateTxn(tx, marketId, marketType, obligationId, repayCoinType, collateralCoinType, coinObjectId);

  const result = await client.provider.signAndExecuteTransaction({
      transaction: tx,
      signer: keypair,
  });

  console.log(result);
  console.log(`Liquidate transaction: ${result.digest}`);
}

// Run examples
if (import.meta.url.startsWith('file:')) {
  liquidate( "mainnet").catch(console.error);
}


