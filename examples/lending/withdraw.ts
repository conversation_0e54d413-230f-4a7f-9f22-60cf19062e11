import { getMarket, Obligation, PebbleClient } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

async function withdrawAsset(coinType: string, withdrawAmount: bigint, network: string = "mainnet", marketName: string = "MainMarket") {
  const client = PebbleClient.fromNetwork(network);
  
  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  console.log('=== WITHDRAW ASSET ===');
  console.log(`Sender: ${sender}`);
  console.log(`Coin Type: ${coinType}`);
  console.log(`Withdraw Amount: ${withdrawAmount}`);
  
  const obligationOwnerCapId = "";
  const obligationId = await client.getObligationIdFromOwnerCapId(obligationOwnerCapId);
  
  const marketInfo = getMarket(network, marketName);
  const details = await client.getObligationDetail(marketInfo.type, marketInfo.objectId, obligationId);
  const obligation = new Obligation(details);
  
  const deposit = obligation.getDeposit(coinType);

  let ctokenAmount;
  if (withdrawAmount >= deposit.amount()) {
    ctokenAmount = deposit.ctokenAmount();
  } else {
    ctokenAmount = deposit.ctokenAmount() * withdrawAmount / deposit.amount();
  }

  // Use the convenient withdrawFromWallet method
  const txHash = await client.withdraw(
    marketInfo.objectId,
    marketInfo.type,
    obligationOwnerCapId,
    coinType,
    ctokenAmount,
    keypair,
  );
  
  console.log(`\n✅ Withdraw transaction successful!`);
  console.log(`Transaction: ${txHash}`);
  
  return txHash;
}

function getCoinSymbol(coinType: string): string {
  const parts = coinType.split('::');
  return parts[parts.length - 1];
}

function getDecimals(coinType: string): number {
  const symbol = getCoinSymbol(coinType);
  const decimals: Record<string, number> = {
    'BTC': 8,
    'ETH': 8,
    'USDC': 6,
    'USDT': 6,
  };
  return decimals[symbol] || 6;
}

// Run examples
if (import.meta.url.startsWith('file:')) {
  // Example: Withdraw 0.5 USDC (6 decimals, so 500000 = 0.5 USDC)
  const withdrawAmount = 1n; // 0.5 USDC
  const coinType = "0x73a1c782178f77b3f10da48eac0eeab8217aac0d427fbead88e42f6caadcc15a::usdt::USDT";
  
  console.log(`Attempting to withdraw ${Number(withdrawAmount) / 1000000} USDC`);
  console.log('⚠️  Important Notes:');
  console.log('   - You can only withdraw assets that you have deposited as collateral');
  console.log('   - Withdrawal cannot reduce collateral below what\'s needed to back your debts');
  console.log('   - If you have borrowed assets, the withdrawal may be limited to maintain safety');
  console.log('   - If withdrawal fails due to safety constraints, repay some debt first\n');
  
  withdrawAsset(coinType, withdrawAmount).catch(console.error);
}

export { withdrawAsset };