import { PebbleClient } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

export async function listObligations(network: string = "testnet", marketName: string = "MainMarket"): Promise<string[]> {
  // Create client instance for testnet (provider is created automatically)
  const client = PebbleClient.fromNetwork(network);
  
  // Get keypair from environment variable
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();
  
  // Get the MainMarket from configuration
  console.log('Fetching obligations from wallet...');
  console.log(`Sender: ${sender}`);
    
  // Fetch obligation owner caps from user's wallet
  return await client.getObligationsFromWallet(sender);
}

// Run examples
if (import.meta.url.startsWith('file:')) {
  listObligations()
    .then((obligationOwnerCaps) => {
      if (obligationOwnerCaps.length === 0) {
        throw new Error('No obligation owner caps found in wallet. Please enter the market first.');
      }

      console.log(`Found ${obligationOwnerCaps.length} obligation owner cap(s):`);
      obligationOwnerCaps.forEach((cap, index) => {
        console.log(`  ${index}: ${cap}`);
      });
    })
    .catch(console.error);
}
