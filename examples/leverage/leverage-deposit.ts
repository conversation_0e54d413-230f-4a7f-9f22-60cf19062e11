import { Transaction } from '@mysten/sui/transactions';
import { AggregatorClient } from '@cetusprotocol/aggregator-sdk';
import { PebbleClient, LeverageClient, getNetworkConfig, prepareCoinsForAmount } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

async function run(
  depositAmount: bigint,
  network: string = "mainnet"
) {
  const client = PebbleClient.fromNetwork(network);
  
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();

  const networkConfig = getNetworkConfig(network);
  const leverageMarket = networkConfig.leverageMarkets[0]!;

  const leverageOwnerCapId = "0x205ce31b6fbcac7f4cde1b7ba5b9a258215e36e975ae09ba9fd7e18b913c3418";
  const leverageClient = new LeverageClient(
    new AggregatorClient({}),
    client,
    { leverageMarketId: leverageMarket.objectId, leveragePackageId: networkConfig.leveragePackageId }
  );

  console.log(`Sender: ${sender}`);
  console.log(`Network: ${network}`);
  console.log(`Leverage Market: ${leverageMarket.objectId}`);
  console.log(`Deposit Amount: ${depositAmount}`);

  const position = await leverageClient.getObligationSummary(leverageOwnerCapId);

  const tx = new Transaction();
  const coinType = `${position.leverageObligation.info?.deposit!}`;
  console.log(`Deposit Type: ${coinType}`);

  const coinObjectId = await prepareCoinsForAmount(tx, client.provider, sender, coinType, depositAmount);  
  await leverageClient.populateDepositTransaction(
    tx,
    leverageOwnerCapId,
    position,
    coinObjectId,
  );
  const result = await client.provider.signAndExecuteTransaction(
    {
      transaction: tx,
      signer: keypair,
    }
  );
  console.log(`Transaction hash: ${result.digest}`);
}

if (import.meta.url.startsWith('file:')) {
  const depositAmount = 1000000n;
  
  run(depositAmount, "mainnet")
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}