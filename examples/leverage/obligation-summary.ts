import { AggregatorClient } from '@cetusprotocol/aggregator-sdk';
import { PebbleClient, LeverageClient, getNetworkConfig } from '@pebble-protocol/pebble-sdk';

async function listObligationDetails(
  network: string = "mainnet",
) {
  const client = PebbleClient.fromNetwork(network);

  const networkConfig = getNetworkConfig(network);
  const leverageMarket = networkConfig.leverageMarkets[0];

  const leverageConfig = {
    leverageMarketId: leverageMarket.objectId,
    leveragePackageId: networkConfig.leveragePackageId,
  };

  const leverageClient = new LeverageClient(
    new AggregatorClient({}),
    client,
    leverageConfig
  );

  const ownerCap = "0x21b8b91b2190f506056fac9234c46e5307491759fcc34715ec5c63ed544cf712";
  const position = await leverageClient.getObligationSummary(ownerCap);
  const leftCoinType = 'aafb102dd0902f5055cadecd687fb5b71ca82ef0e0285d90afde828ec58ca96b::btc::BTC';

  console.log("is long:", position.isLong(leftCoinType));
  console.log("assets:");
  console.log("  principle:", position.principleCoinType());
  console.log("  deposit:", position.leverageObligation.info!.deposit);
  console.log("  borrow:", position.leverageObligation.info!.borrow);
  console.log("leverage:", position.leverageAsFixedOne());

  console.log("position:", position.debtAmount(), position.averagePrice().asNumber(), position.principleAmount(), position.collateralAmount());
  console.log("  debt:", position.debtAmount());
  console.log("  average price:", position.averagePrice().asNumber());
  console.log("  principle amount:", position.principleAmount());
  console.log("  collateral amount:", position.collateralAmount());

  const market = await leverageClient.obtainUnderlyingMarket();
  console.log("pnl: ", position.pnl(market).asNumber());

}

if (import.meta.url.startsWith('file:')) {
  listObligationDetails("mainnet")
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
}