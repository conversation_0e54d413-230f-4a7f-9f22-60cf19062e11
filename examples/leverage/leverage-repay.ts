import { Transaction } from '@mysten/sui/transactions';
import { AggregatorClient } from '@cetusprotocol/aggregator-sdk';
import { PebbleClient, LeverageClient, getNetworkConfig, prepareCoinsForAmount } from '@pebble-protocol/pebble-sdk';
import { getKeypair } from '../utils';

async function run(
  repayAmount: bigint,
  network: string = "mainnet"
) {
  const client = PebbleClient.fromNetwork(network);
  
  const keypair = getKeypair();
  const sender = keypair.getPublicKey().toSuiAddress();

  const networkConfig = getNetworkConfig(network);
  const leverageMarket = networkConfig.leverageMarkets[0]!;

  const leverageOwnerCapId = "0x205ce31b6fbcac7f4cde1b7ba5b9a258215e36e975ae09ba9fd7e18b913c3418";
  const leverageClient = new LeverageClient(
    new AggregatorClient({}),
    client,
    { leverageMarketId: leverageMarket.objectId, leveragePackageId: networkConfig.leveragePackageId }
  );

  console.log(`Sender: ${sender}`);
  console.log(`Network: ${network}`);
  console.log(`Leverage Market: ${leverageMarket.objectId}`);
  console.log(`Repay Amount: ${repayAmount}`);

  const position = await leverageClient.getObligationSummary(leverageOwnerCapId);

  const tx = new Transaction();
  const coinType = `0x${position.leverageObligation.info?.borrow!}`;

  const coinObjectId = await prepareCoinsForAmount(tx, client.provider, sender, coinType, repayAmount);  
  await leverageClient.populateRepayTransaction(
    tx,
    leverageOwnerCapId,
    position,
    coinObjectId,
    keypair
  );
  const result = await client.provider.signAndExecuteTransaction(
    {
      transaction: tx,
      signer: keypair,
    }
  );
  console.log(`Transaction hash: ${result.digest}`);
}

if (import.meta.url.startsWith('file:')) {
  const repayAmount = 1963495n;
  
  run(repayAmount, "mainnet")
    .catch(error => {
      console.error('Error:', error);
      process.exit(1);
    });
}