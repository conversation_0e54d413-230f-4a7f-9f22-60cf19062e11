import { AggregatorClient } from '@cetusprotocol/aggregator-sdk';
import { PebbleClient, LeverageClient, getMarket, getNetworkConfig } from '@pebble-protocol/pebble-sdk';

async function listObligationDetails(
  network: string = "mainnet",
  marketName: string = "MainMarket"
) {
  const client = PebbleClient.fromNetwork(network);

  const networkConfig = getNetworkConfig(network);
  const leverageMarket = networkConfig.leverageMarkets[0];

  const leverageConfig = {
    leverageMarketId: leverageMarket.objectId,
    leveragePackageId: networkConfig.leveragePackageId,
  };

  const leverageClient = new LeverageClient(
    new AggregatorClient({}),
    client,
    leverageConfig
  );

  const ownerCap = "0x5a5c856bf393e28b308a32f592872399d5946bb375fe7172a9b009570907da7d";
  const position = await leverageClient.getLeverageObligationDetail(ownerCap);

  const marketInfo = getMarket(network, marketName);
  const details = await leverageClient.pebbleClient.getObligationDetail(marketInfo.type, marketInfo.objectId, position.lendingObligationId);

  if (details.borrows.length > 0) {
    console.log('\n  Borrows:');
    details.borrows.forEach((borrow, index) => {
      console.log(`    ${index}: ${borrow.coinType} - ${borrow.amount()} (USD: ${borrow.usdValue.toString()})`);
    });
  }

  if (details.deposits.length > 0) {
    console.log('\n  Deposits:');
    details.deposits.forEach((deposit, index) => {
      console.log(`    ${index}: ${deposit.coinType} - ${deposit.amount()} (USD: ${deposit.usdValue.toString()}) [Collateral: ${deposit.canBeCollateral}]`);
    });
  }
}

if (import.meta.url.startsWith('file:')) {
  listObligationDetails("mainnet")
    .catch(error => {
      console.error(error);
      process.exit(1);
    });
}