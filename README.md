# Pebble UI

This is the main repository for the Pebble UI project.

## Project Structure

```
pebble-ui/
├── frontend/          # Frontend application (React + TypeScript + Vite)
│   ├── src/          # Source code
│   ├── public/       # Public assets
│   ├── dist/         # Build output
│   ├── package.json  # Frontend dependencies
│   └── ...           # Other frontend config files
├── .git/             # Git repository
└── .github/          # GitHub workflows and templates
```

## Development

### Frontend

To work on the frontend application:

```bash
pnpm install

# Start the frontend dev server by running the script at the project root
pnpm dev

# Install dependencies at the project root
pnpm add <package-name> -F pebble-frontend        # frontend
pnpm add <package-name> -F @pebble-protocol/pebble-sdk   # sdk

# build SDK
pnpm --filter @pebble-protocol/pebble-sdk build

# build frontend
pnpm --filter pebble-frontend build

# run SDK test
pnpm --filter @pebble-protocol/pebble-sdk test
```

## Technologies

- **Frontend**: React 19, <PERSON>Script, Vite, Tailwind CSS
- **State Management**: Zustand
- **Data Fetching**: TanStack Query
- **UI Components**: Radix UI
- **Charts**: Lightweight Charts
- **Blockchain**: Sui SDK, Pebble Protocol SDK
