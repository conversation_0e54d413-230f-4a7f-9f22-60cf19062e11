{"name": "pebble-ui-monorepo", "version": "1.0.0", "private": true, "workspaces": ["frontend", "sdk"], "scripts": {"dev": "pnpm build:sdk && pnpm dev:web", "dev:web": "pnpm --filter pebble-frontend dev", "build:sdk": "pnpm --filter @pebble-protocol/pebble-sdk build"}, "devDependencies": {"simple-git-hooks": "^2.13.1", "typescript": "^5.8.3"}, "simple-git-hooks": {"pre-commit": "pnpm -F pebble-frontend lint-staged"}, "pnpm": {"overrides": {"@mysten/sui": "1.36.2"}, "onlyBuiltDependencies": ["@tailwindcss/oxide", "core-js-pure", "esbuild", "simple-git-hooks"]}, "packageManager": "pnpm@9.0.0"}