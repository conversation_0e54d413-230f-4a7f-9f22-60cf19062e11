name: Deploy to S3 on Develop Push

on:
  push:
    branches:
      - develop

jobs:
  deploy:
    name: Build and Deploy to S3
    runs-on: ubuntu-latest

    permissions:
      id-token: write
      contents: read
      packages: read

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18

      - name: Install pnpm globally
        run: |
          npm install -g pnpm@9
          pnpm -v

      - name: Configure AWS credentials via OIDC
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::439863938209:role/github-actions-pebble
          aws-region: ap-southeast-1

      - name: Install dependencies
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: pnpm i --frozen-lockfile

      - name: Build sdk
        run: pnpm --filter @pebble-protocol/pebble-sdk build

      - name: Build frontend
        run: pnpm --filter pebble-frontend build

      - name: Deploy to S3
        run: |
          cd frontend
          aws s3 sync ./dist s3://pebble-app-develop/ --delete

      - name: Invalidate CloudFront cache
        run: |
          aws cloudfront create-invalidation \
            --distribution-id E14MJCG3N0CQ0J \
            --paths "/*"